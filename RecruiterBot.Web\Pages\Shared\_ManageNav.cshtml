@using Microsoft.AspNetCore.Identity
@using RecruiterBot.Core.Models
@using RecruiterBot.Web.Services
@inject SignInManager<User> SignInManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
}

<ul class="menu bg-base-200 rounded-box">
    <li class="menu-title">
        <span>Manage your account</span>
    </li>
    <li>
        <a class="@ManageNavPages.IndexNavClass(ViewContext)" id="profile" asp-page="./Index">
            <i class="fas fa-user mr-2"></i> Profile
        </a>
    </li>
    <li>
        <a class="@ManageNavPages.ChangePasswordNavClass(ViewContext)" id="change-password" asp-page="./ChangePassword">
            <i class="fas fa-key mr-2"></i> Password
        </a>
    </li>
    @if (hasExternalLogins)
    {
        <li>
            <a class="@ManageNavPages.ExternalLoginsNavClass(ViewContext)" id="external-login" asp-page="./ExternalLogins">
                <i class="fas fa-external-link-alt mr-2"></i> External logins
            </a>
        </li>
    }
    <li>
        <a class="@ManageNavPages.PersonalDataNavClass(ViewContext)" id="personal-data" asp-page="./PersonalData">
            <i class="fas fa-database mr-2"></i> Personal data
        </a>
    </li>
</ul>
