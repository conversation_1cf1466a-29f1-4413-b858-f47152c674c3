@page "{id}"
@model RecruiterBot.Web.Pages.Consultant.InterviewProfiles.DetailsModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "Interview Profile Details";
}

@section Styles {
    <style>
        .profile-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .section-card {
            border-left: 4px solid #0d6efd;
            margin-bottom: 1.5rem;
        }
        .tech-badge {
            background-color: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
            border-radius: 15px;
            padding: 5px 12px;
            margin: 2px;
            display: inline-block;
            font-size: 0.875rem;
        }
        .experience-timeline {
            border-left: 3px solid #0d6efd;
            padding-left: 20px;
            margin-left: 10px;
        }
        .experience-item {
            position: relative;
            margin-bottom: 30px;
        }
        .experience-item::before {
            content: '';
            position: absolute;
            left: -26px;
            top: 5px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #0d6efd;
        }
        .current-position::before {
            background-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
        }
        .achievement-item {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
        }
        .stat-badge {
            background: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
            border: 1px solid rgba(13, 110, 253, 0.2);
            border-radius: 8px;
            padding: 10px;
            text-align: center;
        }
    </style>
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            @if (Model.Profile != null)
            {
                <!-- Header -->
                <div class="profile-header p-4 mb-4">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h2 class="mb-2">
                                <i class="bi bi-person-badge me-2"></i>
                                Interview Profile
                            </h2>
                            <p class="mb-0 opacity-75">Created @Model.Profile.CreatedDisplay</p>
                            @if (Model.Profile.UpdatedAt > Model.Profile.CreatedAt)
                            {
                                <p class="mb-0 opacity-75">Last updated @Model.Profile.LastUpdatedDisplay</p>
                            }
                        </div>
                        <div class="d-flex gap-2">
                            <a asp-page="./Edit" asp-route-id="@Model.Profile.Id" class="btn btn-light">
                                <i class="bi bi-pencil me-2"></i>Edit Profile
                            </a>
                            <a asp-page="./Index" class="btn btn-outline-light">
                                <i class="bi bi-arrow-left me-2"></i>Back to Profiles
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="stat-badge">
                            <div class="h4 mb-1">@Model.Profile.ExperienceCount</div>
                            <small>Work Experience@(Model.Profile.ExperienceCount != 1 ? "s" : "")</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-badge">
                            <div class="h4 mb-1">@(string.IsNullOrWhiteSpace(Model.Profile.PrimaryTechnology) ? 0 : 1)</div>
                            <small>Technology</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-badge">
                            <div class="h4 mb-1">@Model.Profile.AchievementCount</div>
                            <small>Achievement@(Model.Profile.AchievementCount != 1 ? "s" : "")</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-badge">
                            <div class="h4 mb-1">@Model.Profile.TotalYearsExperience</div>
                            <small>Year@(Model.Profile.TotalYearsExperience != 1 ? "s" : "") Experience</small>
                        </div>
                    </div>
                </div>

                <!-- About You Section -->
                <div class="card section-card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-person-circle me-2"></i>About You
                        </h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">@Model.Profile.AboutYou</p>
                    </div>
                </div>

                <!-- Primary Technology Section -->
                @if (!string.IsNullOrWhiteSpace(Model.Profile.PrimaryTechnology))
                {
                    <div class="card section-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-code-slash me-2"></i>Primary Technology
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex flex-wrap gap-1">
                                <span class="tech-badge">@Model.Profile.PrimaryTechnology</span>
                            </div>
                        </div>
                    </div>
                }

                <!-- Work Experience Section -->
                @if (Model.Profile.Experiences.Any())
                {
                    <div class="card section-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-briefcase me-2"></i>Work Experience
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="experience-timeline">
                                @foreach (var experience in Model.Profile.Experiences.OrderByDescending(e => e.StartDate))
                                {
                                    <div class="experience-item @(experience.IsCurrentProject ? "current-position" : "")">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <div>
                                                <h6 class="mb-1 fw-bold">@experience.CompanyName</h6>
                                                <div class="text-muted">
                                                    <i class="bi bi-calendar3 me-1"></i>
                                                    @experience.DateRange
                                                    @if (experience.IsCurrentProject)
                                                    {
                                                        <span class="badge bg-success ms-2">Current</span>
                                                    }
                                                </div>
                                                <div class="text-muted">
                                                    <i class="bi bi-clock me-1"></i>
                                                    @experience.DurationDisplay
                                                </div>
                                            </div>
                                        </div>
                                        
                                        @if (!string.IsNullOrWhiteSpace(experience.ProjectDescription))
                                        {
                                            <div class="mb-3">
                                                <p class="mb-0">@experience.ProjectDescription</p>
                                            </div>
                                        }

                                        @if (experience.TechnologiesUsed.Any())
                                        {
                                            <div class="mb-2">
                                                <strong class="text-muted">Technologies:</strong>
                                                <div class="d-flex flex-wrap gap-1 mt-1">
                                                    @foreach (var tech in experience.TechnologiesUsed)
                                                    {
                                                        <span class="badge bg-secondary">@tech</span>
                                                    }
                                                </div>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                }

                <!-- Achievements Section -->
                @if (Model.Profile.Achievements.Any())
                {
                    <div class="card section-card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="bi bi-trophy me-2"></i>Professional Achievements
                            </h5>
                        </div>
                        <div class="card-body">
                            @foreach (var achievement in Model.Profile.Achievements)
                            {
                                <div class="achievement-item">
                                    <i class="bi bi-award text-warning me-2"></i>
                                    @achievement
                                </div>
                            }
                        </div>
                    </div>
                }

                <!-- AI Context Preview -->
                <div class="card section-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="bi bi-robot me-2"></i>AI Context Preview
                        </h5>
                        <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" 
                                data-bs-target="#aiContextCollapse" aria-expanded="false">
                            <i class="bi bi-eye me-1"></i>Show/Hide
                        </button>
                    </div>
                    <div class="collapse" id="aiContextCollapse">
                        <div class="card-body">
                            <p class="text-muted mb-3">This is how your profile will be presented to the AI for interview question responses:</p>
                            <pre class="bg-light p-3 rounded" style="font-size: 0.875rem; white-space: pre-wrap;">@Model.Profile.GetFormattedProfileForAI()</pre>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between">
                            <a asp-page="./Index" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to Profiles
                            </a>
                            <div class="d-flex gap-2">
                                <a asp-page="./Edit" asp-route-id="@Model.Profile.Id" class="btn btn-primary">
                                    <i class="bi bi-pencil me-2"></i>Edit Profile
                                </a>
                                <button class="btn btn-outline-danger" onclick="confirmDelete('@Model.Profile.Id')">
                                    <i class="bi bi-trash me-2"></i>Delete Profile
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            }
            else
            {
                <div class="card border-warning">
                    <div class="card-body text-center py-5">
                        <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
                        <h4 class="mt-3">Profile Not Found</h4>
                        <p class="text-muted">The requested interview profile could not be found or you don't have access to it.</p>
                        <a asp-page="./Index" class="btn btn-primary">
                            <i class="bi bi-arrow-left me-2"></i>Back to Profiles
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this interview profile?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let deleteProfileId = '';

        function confirmDelete(profileId) {
            deleteProfileId = profileId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            if (deleteProfileId) {
                window.location.href = `/Consultant/InterviewProfiles/Delete/${deleteProfileId}`;
            }
        });
    </script>
}
