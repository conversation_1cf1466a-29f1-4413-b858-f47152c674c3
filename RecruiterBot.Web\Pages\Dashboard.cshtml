@page
@model RecruiterBot.Web.Pages.DashboardModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "Dashboard";
}

<div class="content-wrapper">
    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
        <h1 class="h2">Dashboard</h1>
        <div class="btn-toolbar mb-2 mb-md-0">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i> Refresh
            </button>
        </div>
    </div>

    @if (User.IsInRole(UserRoles.Admin))
    {
        <!-- Admin Dashboard -->
        <div class="row mb-4">
            <div class="col-md-6 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Total Corp Admins</h6>
                                <h2 class="mb-0">@(Model.Stats?.TotalCorpAdmins ?? 0)</h2>
                            </div>
                            <i class="bi bi-people fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Active Corp Admins</h6>
                                <h2 class="mb-0">@(Model.Stats?.ActiveCorpAdmins ?? 0)</h2>
                            </div>
                            <i class="bi bi-person-check fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">User Management</h6>
                                <p class="text-muted mb-0">Manage Corporate Administrators</p>
                            </div>
                            <div>
                                <div class="btn-group">
                                    <a href="/UserManagement" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-people me-1"></i> View All Users
                                    </a>
                                    <a href="/UserManagement/Create" class="btn btn-sm btn-primary">
                                        <i class="bi bi-person-plus me-1"></i> Create Corp Admin
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
    else if (Model.Stats != null)
    {
        <!-- Corp Admin and User Dashboard -->
        <div class="row mb-4">
            <div class="col-md-3 mb-4">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Total Bots</h6>
                                <h2 class="mb-0">@Model.Stats.TotalBots</h2>
                            </div>
                            <i class="bi bi-robot fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Active Bots</h6>
                                <h2 class="mb-0">@Model.Stats.ActiveBots</h2>
                            </div>
                            <i class="bi bi-play-circle fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Inactive Bots</h6>
                                <h2 class="mb-0">@Model.Stats.InactiveBots</h2>
                            </div>
                            <i class="bi bi-pause-circle fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">AI Search Bots</h6>
                                <h2 class="mb-0">@Model.Stats.AiSearchBots</h2>
                            </div>
                            <i class="bi bi-cpu fs-1 opacity-50"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="card-title mb-1">Bot Statistics</h6>
                                <p class="text-muted mb-0">Last updated: @Model.Stats.LastUpdated.ToString("g")</p>
                            </div>
                            <div>
                                <div class="btn-group">
                                    <a href="/BotConfigurations" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-gear me-1"></i> Manage Bots
                                    </a>
                                    <a href="/BotConfigurations/Create" class="btn btn-sm btn-primary">
                                        <i class="bi bi-plus-lg me-1"></i> Add New Bot
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

@section Styles {
    <style>
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            transition: transform 0.2s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card-header {
            background-color: transparent;
            border-bottom: 1px solid rgba(0,0,0,.05);
            padding: 1.25rem 1.5rem;
        }
        .card-title {
            font-size: 0.875rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.5rem;
        }
        .card h2 {
            font-weight: 700;
            margin: 0;
        }
        .card i {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        .btn-group .btn {
            border-radius: 6px;
            margin-left: 0.5rem;
        }
    </style>
}
