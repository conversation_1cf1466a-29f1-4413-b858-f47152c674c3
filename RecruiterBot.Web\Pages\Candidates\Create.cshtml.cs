using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using UglyToad.PdfPig;
using System.Text;

namespace RecruiterBot.Web.Pages.Candidates
{
    [Authorize(Policy = RecruiterBot.Core.Constants.AuthorizationPolicies.AllRoles)]
    public class CreateModel : PageModel
    {
        private readonly IResumeParsingService _resumeParsingService;
        private readonly ICandidateService _candidateService;
        private readonly IWebHostEnvironment _environment;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(
            IResumeParsingService resumeParsingService,
            ICandidateService candidateService,
            IWebHostEnvironment environment,
            IFileStorageService fileStorageService,
            ILogger<CreateModel> logger)
        {
            _resumeParsingService = resumeParsingService;
            _candidateService = candidateService;
            _environment = environment;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        [BindProperty]
        public IFormFile? ResumeFile { get; set; }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public bool IsOllamaAvailable { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "First name is required")]
            [Display(Name = "First Name")]
            public string FirstName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Last name is required")]
            [Display(Name = "Last Name")]
            public string LastName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Email is required")]
            [EmailAddress(ErrorMessage = "Please enter a valid email address")]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;

            [Required(ErrorMessage = "Phone number is required")]
            [Display(Name = "Phone")]
            public string Phone { get; set; } = string.Empty;

            [Display(Name = "Current Title")]
            public string CurrentTitle { get; set; } = string.Empty;

            [Display(Name = "Current Company")]
            public string CurrentCompany { get; set; } = string.Empty;

            [Display(Name = "Professional Summary")]
            public string Summary { get; set; } = string.Empty;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            IsOllamaAvailable = await _resumeParsingService.IsServiceAvailableAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostUploadAsync()
        {
            IsOllamaAvailable = await _resumeParsingService.IsServiceAvailableAsync();

            if (ResumeFile == null || ResumeFile.Length == 0)
            {
                ModelState.AddModelError("ResumeFile", "Please select a resume file.");
                return Page();
            }

            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    ModelState.AddModelError(string.Empty, "User not found.");
                    return Page();
                }

                // Validate file size (10MB max)
                const int maxFileSize = 10 * 1024 * 1024;
                if (ResumeFile.Length > maxFileSize)
                {
                    ModelState.AddModelError("ResumeFile", "File size must be less than 10MB.");
                    return Page();
                }

                // Validate file type
                var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".txt" };
                var fileExtension = Path.GetExtension(ResumeFile.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    ModelState.AddModelError("ResumeFile", "Only PDF, DOC, DOCX, and TXT files are allowed.");
                    return Page();
                }

                // Upload the file to storage service
                var fileUrl = await _fileStorageService.UploadFileAsync(ResumeFile, "resumes");

                // Extract text from the file
                string resumeText;
                try
                {
                    resumeText = await ExtractTextFromFileAsync(ResumeFile, fileExtension);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error extracting text from file: {FileName}", ResumeFile.FileName);
                    ModelState.AddModelError(string.Empty, "Error reading the file. Please ensure it's a valid document.");

                    // Clean up the uploaded file
                    await _fileStorageService.DeleteFileAsync(fileUrl);

                    return Page();
                }

                if (string.IsNullOrWhiteSpace(resumeText))
                {
                    ModelState.AddModelError(string.Empty, "No text could be extracted from the file. Please check the file format.");

                    // Clean up the uploaded file
                    await _fileStorageService.DeleteFileAsync(fileUrl);

                    return Page();
                }

                // Parse the resume using AI (if available)
                var candidate = await _resumeParsingService.ParseResumeAsync(resumeText, ResumeFile.FileName);
                candidate.ResumeFilePath = fileUrl;

                // Validate that the parsed resume contains required information
                var validationErrors = ValidateParsedCandidate(candidate);
                if (validationErrors.Any())
                {
                    // If validation fails, still save the candidate but show warnings
                    await _candidateService.CreateCandidateAsync(candidate, userId);

                    TempData["WarningMessage"] = "Resume uploaded but some required information is missing: " +
                                                string.Join(", ", validationErrors) +
                                                ". Please review and complete the candidate information.";
                    return RedirectToPage("./Edit", new { id = candidate.Id });
                }

                // Save the candidate to database
                await _candidateService.CreateCandidateAsync(candidate, userId);

                TempData["SuccessMessage"] = "Resume uploaded and parsed successfully!";
                return RedirectToPage("./Edit", new { id = candidate.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing resume upload: {FileName}", ResumeFile?.FileName);
                ModelState.AddModelError(string.Empty, "An error occurred while processing the resume. Please try again.");
                return Page();
            }
        }

        public async Task<IActionResult> OnPostManualAsync()
        {
            IsOllamaAvailable = await _resumeParsingService.IsServiceAvailableAsync();

            if (!ModelState.IsValid)
            {
                return Page();
            }

            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    ModelState.AddModelError(string.Empty, "User not found.");
                    return Page();
                }

                // Create candidate from manual input
                var candidate = new Candidate
                {
                    FirstName = Input.FirstName,
                    LastName = Input.LastName,
                    Email = Input.Email,
                    Phone = Input.Phone,
                    CurrentTitle = Input.CurrentTitle,
                    CurrentCompany = Input.CurrentCompany,
                    Summary = Input.Summary,
                    ParsingStatus = ParsingStatus.Manual,
                    AiConfidenceScore = 0,
                    OriginalResumeFilename = "Manual Entry",
                    ResumeText = $"Manual entry for {Input.FirstName} {Input.LastName}"
                };

                // Save the candidate to database
                await _candidateService.CreateCandidateAsync(candidate, userId);

                TempData["SuccessMessage"] = "Candidate created successfully!";
                return RedirectToPage("./Edit", new { id = candidate.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating candidate manually");
                ModelState.AddModelError(string.Empty, "An error occurred while creating the candidate. Please try again.");
                return Page();
            }
        }

        private async Task<string> ExtractTextFromFileAsync(IFormFile file, string fileExtension)
        {
            switch (fileExtension)
            {
                case ".pdf":
                    return await ExtractTextFromPdfAsync(file);

                case ".doc":
                case ".docx":
                    // For now, we'll return a message indicating manual text extraction is needed
                    // In a production environment, you might want to use a library like DocumentFormat.OpenXml
                    throw new NotSupportedException("DOC/DOCX files are not yet supported. Please convert to PDF or TXT format.");

                default:
                    throw new NotSupportedException($"File type {fileExtension} is not supported.");
            }
        }

        private async Task<string> ExtractTextFromFileAsync(string filePath, string fileExtension)
        {
            switch (fileExtension)
            {
                case ".pdf":
                    return ExtractTextFromPdf(filePath);

                case ".txt":
                    return await System.IO.File.ReadAllTextAsync(filePath);

                case ".doc":
                case ".docx":
                    // For now, we'll return a message indicating manual text extraction is needed
                    // In a production environment, you might want to use a library like DocumentFormat.OpenXml
                    throw new NotSupportedException("DOC/DOCX files are not yet supported. Please convert to PDF or TXT format.");

                default:
                    throw new NotSupportedException($"File type {fileExtension} is not supported.");
            }
        }

        private async Task<string> ExtractTextFromPdfAsync(IFormFile file)
        {
            try
            {
                using var stream = file.OpenReadStream();
                using var document = PdfDocument.Open(stream);
                var text = new StringBuilder();

                foreach (var page in document.GetPages())
                {
                    text.AppendLine(page.Text);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FileName}", file.FileName);
                throw new Exception("Error reading PDF file. The file may be corrupted or password-protected.");
            }
        }

        private string ExtractTextFromPdf(string filePath)
        {
            try
            {
                using var document = PdfDocument.Open(filePath);
                var text = new StringBuilder();

                foreach (var page in document.GetPages())
                {
                    text.AppendLine(page.Text);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FilePath}", filePath);
                throw new Exception("Error reading PDF file. The file may be corrupted or password-protected.");
            }
        }

        private List<string> ValidateParsedCandidate(Candidate candidate)
        {
            var errors = new List<string>();

            // Check for required fields based on user preferences
            if (string.IsNullOrWhiteSpace(candidate.FirstName) && string.IsNullOrWhiteSpace(candidate.LastName))
            {
                errors.Add("Name");
            }

            if (string.IsNullOrWhiteSpace(candidate.Email))
            {
                errors.Add("Email");
            }

            if (string.IsNullOrWhiteSpace(candidate.Phone))
            {
                errors.Add("Contact number");
            }

            return errors;
        }
    }
}
