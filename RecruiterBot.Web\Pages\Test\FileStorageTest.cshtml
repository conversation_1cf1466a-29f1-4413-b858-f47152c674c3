@page
@model RecruiterBot.Web.Pages.Test.FileStorageTestModel
@{
    ViewData["Title"] = "File Storage Test";
}

<div class="container mt-4">
    <h2>File Storage Service Test</h2>
    <div class="alert alert-info">
        <strong>Current Storage Provider:</strong> @Model.StorageProvider
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Upload Test File</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="TestFile" class="form-label">Test File</label>
                            <input type="file" class="form-control" id="TestFile" name="TestFile" 
                                   accept=".pdf,.txt,.doc,.docx" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Upload Test File</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5>Test Results</h5>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(Model.TestResult))
                    {
                        <div class="alert @(Model.Success ? "alert-success" : "alert-danger")">
                            @Model.TestResult
                        </div>
                    }
                    
                    @if (!string.IsNullOrEmpty(Model.FileUrl))
                    {
                        <div class="mt-3">
                            <h6>Uploaded File:</h6>
                            <p><strong>URL:</strong> @Model.FileUrl</p>
                            <a href="@Model.FileUrl" target="_blank" class="btn btn-outline-primary btn-sm">
                                View File
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
