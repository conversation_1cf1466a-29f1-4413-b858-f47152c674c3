using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using RecruiterBot.Core.Models;

namespace RecruiterBot.Web.ViewComponents
{
    public class NavigationViewComponent : ViewComponent
    {
        private readonly UserManager<User> _userManager;

        public NavigationViewComponent(UserManager<User> userManager)
        {
            _userManager = userManager;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var user = await _userManager.GetUserAsync(HttpContext.User);
            
            if (user != null)
            {
                // Ensure we have the display name and profile image URL
                ViewBag.UserDisplayName = user.DisplayName;
                ViewBag.ProfileImageUrl = !string.IsNullOrEmpty(user.ProfileImageUrl) 
                    ? user.ProfileImageUrl 
                    : "/images/default-avatar.svg";
            }
            
            return View(user);
        }
    }
}
