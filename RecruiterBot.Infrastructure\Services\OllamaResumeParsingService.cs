using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using OllamaSharp;
using OllamaSharp.Models;
using RecruiterBot.Core.Models;
using System.Text;
using System.Text.Json;

namespace RecruiterBot.Infrastructure.Services
{
    public class OllamaResumeParsingService : IResumeParsingService
    {
        private readonly IOllamaApiClient _ollamaClient;
        private readonly ILogger<OllamaResumeParsingService> _logger;
        private readonly string _modelName;

        public string ServiceName => "Ollama";

        public OllamaResumeParsingService(
            IOllamaApiClient ollamaClient,
            IConfiguration configuration,
            ILogger<OllamaResumeParsingService> logger)
        {
            _ollamaClient = ollamaClient;
            _logger = logger;
            _modelName = configuration["Ollama:ModelName"] ?? "llama3.2";

            // Log initialization for verification
            _logger.LogInformation("OllamaResumeParsingService initialized with model: {ModelName}", _modelName);
        }

        public async Task<bool> IsServiceAvailableAsync()
        {
            try
            {
                var models = await _ollamaClient.ListLocalModels();
                return models.Any();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Ollama service is not available");
                return false;
            }
        }

        public async Task<Candidate> ParseResumeAsync(string resumeText, string originalFilename)
        {
            try
            {
                _logger.LogInformation("Starting resume parsing for file: {Filename}", originalFilename);

                var prompt = CreateParsingPrompt(resumeText);
                var ollamaResponse = await CallOllamaAsync(prompt);

                if (string.IsNullOrEmpty(ollamaResponse))
                {
                    throw new Exception("Empty response from Ollama");
                }

                var candidate = ParseOllamaResponse(ollamaResponse);
                candidate.OriginalResumeFilename = originalFilename;
                candidate.ResumeText = resumeText;
                candidate.ParsingStatus = ParsingStatus.Completed;

                _logger.LogInformation("Successfully parsed resume for: {Name}", candidate.FullName);
                return candidate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing resume: {Filename}", originalFilename);
                
                return new Candidate
                {
                    OriginalResumeFilename = originalFilename,
                    ResumeText = resumeText,
                    ParsingStatus = ParsingStatus.Failed,
                    ParsingError = ex.Message
                };
            }
        }

        private string CreateParsingPrompt(string resumeText)
        {
            return $@"
Please extract the following information from this resume and return it as a JSON object with the exact structure shown below. 
If any information is not available, use null or empty arrays as appropriate, Do not provide information which is not available in resume text.

Resume text:
{resumeText}

Please return a JSON object with this exact structure:
{{
    ""firstName"": ""First Name of candidate"",
    ""lastName"": ""Last Name of candidate"",
    ""email"": ""email of candidate"",
    ""phone"": ""string"",
    ""address"": ""string"",
    ""city"": ""string"",
    ""state"": ""string"",
    ""zipCode"": ""string"",
    ""country"": ""string"",
    ""linkedInUrl"": ""string"",
    ""portfolioUrl"": ""string"",
    ""currentTitle"": ""string"",
    ""currentCompany"": ""string"",
    ""yearsOfExperience"": number,
    ""skills"": [""skill1"", ""skill2""],
    ""summary"": ""string"",
    ""workExperience"": [
        {{
            ""company"": ""string"",
            ""title"": ""string"",
            ""startDate"": ""string"",
            ""endDate"": ""string"",
            ""description"": ""string"",
            ""location"": ""string""
        }}
    ],
    ""education"": [
        {{
            ""institution"": ""string"",
            ""degree"": ""string"",
            ""fieldOfStudy"": ""string"",
            ""graduationYear"": ""string"",
            ""gpa"": ""string""
        }}
    ],
    ""certifications"": [""cert1"", ""cert2""],
    ""aiConfidenceScore"": 0.95
}}

Return only the JSON object, no additional text or explanation.";
        }

        private async Task<string> CallOllamaAsync(string prompt)
        {
            try
            {
                var request = new GenerateRequest
                {

                    Model = _modelName,
                    Prompt = prompt,
                    Stream = false,
                    Options = new RequestOptions {
                        Temperature = (float?)0.1, 
                        NumCtx = 128000,

                    
                    }
                };

                var responseBuilder = new StringBuilder();
                await foreach (var response in _ollamaClient.Generate(request))
                {
                    if (response?.Response != null)
                    {
                        responseBuilder.Append(response.Response);
                    }
                }

                return responseBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling Ollama API");
                throw new Exception($"Ollama API error: {ex.Message}");
            }
        }

        private Candidate ParseOllamaResponse(string jsonResponse)
        {
            try
            {
                // Clean the response to extract just the JSON
                var cleanJson = ExtractJsonFromResponse(jsonResponse);
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var parsedData = JsonSerializer.Deserialize<CandidateParseResult>(cleanJson, options);
                
                return new Candidate
                {
                    FirstName = parsedData.FirstName,
                    LastName = parsedData.LastName,
                    Email = parsedData.Email,
                    Phone = parsedData.Phone,
                    Address = parsedData.Address,
                    City = parsedData.City,
                    State = parsedData.State,
                    ZipCode = parsedData.ZipCode,
                    Country = parsedData.Country,
                    LinkedInUrl = parsedData.LinkedInUrl,
                    PortfolioUrl = parsedData.PortfolioUrl,
                    CurrentTitle = parsedData.CurrentTitle,
                    CurrentCompany = parsedData.CurrentCompany,
                    YearsOfExperience = parsedData.YearsOfExperience,
                    Skills = parsedData.Skills ?? new List<string>(),
                    Summary = parsedData.Summary,
                    WorkExperience = ConvertWorkExperience(parsedData.WorkExperience),
                    Education = ConvertEducation(parsedData.Education),
                    Certifications = parsedData.Certifications ?? new List<string>(),
                    AiConfidenceScore = parsedData.AiConfidenceScore
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing Ollama JSON response: {Response}", jsonResponse);
                throw new Exception($"Failed to parse AI response: {ex.Message}");
            }
        }

        private string ExtractJsonFromResponse(string response)
        {
            // Find the first { and last } to extract JSON
            var startIndex = response.IndexOf('{');
            var lastIndex = response.LastIndexOf('}');
            
            if (startIndex >= 0 && lastIndex > startIndex)
            {
                return response.Substring(startIndex, lastIndex - startIndex + 1);
            }
            
            return response;
        }

        private List<WorkExperience> ConvertWorkExperience(List<WorkExperienceParseResult> workExp)
        {
            if (workExp == null) return new List<WorkExperience>();
            
            var result = new List<WorkExperience>();
            foreach (var exp in workExp)
            {
                result.Add(new WorkExperience
                {
                    Company = exp.Company,
                    Title = exp.Title,
                    StartDate = exp.StartDate,
                    EndDate = exp.EndDate,
                    Description = exp.Description,
                    Location = exp.Location
                });
            }
            return result;
        }

        private List<Education> ConvertEducation(List<EducationParseResult> education)
        {
            if (education == null) return new List<Education>();
            
            var result = new List<Education>();
            foreach (var edu in education)
            {
                result.Add(new Education
                {
                    Institution = edu.Institution,
                    Degree = edu.Degree,
                    FieldOfStudy = edu.FieldOfStudy,
                    GraduationYear = edu.GraduationYear,
                    GPA = edu.GPA
                });
            }
            return result;
        }
    }

}
