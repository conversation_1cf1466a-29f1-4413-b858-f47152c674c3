@page
@model RecruiterBot.Web.Pages.Admin.LLMModels.IndexModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "LLM Model Management";
}

@section Styles {
    <style>
        .model-card {
            transition: transform 0.2s ease-in-out;
        }
        .model-card:hover {
            transform: translateY(-2px);
        }
        .provider-badge {
            font-size: 0.75rem;
        }
        .search-container {
            position: relative;
        }
        .search-container .bi-search {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .search-input {
            padding-left: 35px;
        }
    </style>
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-cpu me-2"></i>
                        LLM Model Management
                    </h2>
                    <p class="text-muted mb-0">Configure and manage Large Language Models for the platform</p>
                </div>
                <a asp-page="./Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Add New Model
                </a>
            </div>

            <!-- Search and Filter -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="search-container">
                        <i class="bi bi-search"></i>
                        <input type="text" id="searchInput" class="form-control search-input" 
                               placeholder="Search models by name or description..." 
                               value="@Model.SearchTerm">
                    </div>
                </div>
                <div class="col-md-3">
                    <select id="providerFilter" class="form-select">
                        <option value="">All Providers</option>
                        @foreach (var provider in Enum.GetValues<RecruiterBot.Core.Models.ModelProvider>())
                        {
                            <option value="@provider" selected="@(Model.SelectedProvider == provider)">@provider</option>
                        }
                    </select>
                </div>
                <div class="col-md-3">
                    <select id="statusFilter" class="form-select">
                        <option value="">All Status</option>
                        <option value="true" selected="@(Model.ActiveOnly == true)">Active Only</option>
                        <option value="false" selected="@(Model.ActiveOnly == false)">Inactive Only</option>
                    </select>
                </div>
            </div>

            <!-- Success/Error Messages -->
            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>@Model.SuccessMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>@Model.ErrorMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Models Grid -->
            @if (Model.Models?.Any() == true)
            {
                <div class="row g-4" id="modelsContainer">
                    @foreach (var llmModel in Model.Models)
                    {
                        <div class="col-lg-4 col-md-6 model-item"
                             data-provider="@llmModel.ModelProvider"
                             data-status="@llmModel.IsActive.ToString().ToLower()"
                             data-search="@($"{llmModel.ModelName} {llmModel.DisplayName} {llmModel.Description}".ToLower())">
                            <div class="card model-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge provider-badge bg-info">@llmModel.ProviderDisplayName</span>
                                        <span class="@llmModel.StatusBadgeClass ms-2">@llmModel.StatusDisplayName</span>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" asp-page="./Edit" asp-route-id="@llmModel.Id">
                                                <i class="bi bi-pencil me-2"></i>Edit
                                            </a></li>
                                            <li><button class="dropdown-item" onclick="toggleStatus('@llmModel.Id', @llmModel.IsActive.ToString().ToLower())">
                                                <i class="bi bi-@(llmModel.IsActive ? "pause" : "play") me-2"></i>
                                                @(llmModel.IsActive ? "Deactivate" : "Activate")
                                            </button></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><button class="dropdown-item text-danger" onclick="confirmDelete('@llmModel.Id', '@llmModel.DisplayName')">
                                                <i class="bi bi-trash me-2"></i>Delete
                                            </button></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h5 class="card-title">@llmModel.DisplayName</h5>
                                    <h6 class="card-subtitle mb-2 text-muted">@llmModel.ModelName</h6>
                                    @if (!string.IsNullOrEmpty(llmModel.Description))
                                    {
                                        <p class="card-text">@llmModel.Description</p>
                                    }
                                    <div class="row text-center mt-3">
                                        <div class="col-6">
                                            <small class="text-muted">Type</small>
                                            <div class="fw-bold">@llmModel.TypeDisplayName</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Max Tokens</small>
                                            <div class="fw-bold">@llmModel.MaxTokens.ToString("N0")</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer text-muted">
                                    <small>
                                        Created @llmModel.CreatedAt.ToString("MMM dd, yyyy")
                                        @if (llmModel.UpdatedAt > llmModel.CreatedAt)
                                        {
                                            <br />@:Updated @llmModel.UpdatedAt.ToString("MMM dd, yyyy")
                                        }
                                    </small>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-cpu display-1 text-muted"></i>
                    <h4 class="mt-3">No LLM Models Found</h4>
                    <p class="text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            <text>No models match your search criteria. Try adjusting your filters.</text>
                        }
                        else
                        {
                            <text>Get started by adding your first LLM model configuration.</text>
                        }
                    </p>
                    @if (string.IsNullOrEmpty(Model.SearchTerm))
                    {
                        <a asp-page="./Create" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Add First Model
                        </a>
                    }
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the model <strong id="deleteModelName"></strong>?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let deleteModelId = '';

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', filterModels);
        document.getElementById('providerFilter').addEventListener('change', filterModels);
        document.getElementById('statusFilter').addEventListener('change', filterModels);

        function filterModels() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const provider = document.getElementById('providerFilter').value;
            const status = document.getElementById('statusFilter').value;
            
            const modelItems = document.querySelectorAll('.model-item');
            
            modelItems.forEach(item => {
                const searchText = item.dataset.search;
                const itemProvider = item.dataset.provider;
                const itemStatus = item.dataset.status;
                
                const matchesSearch = !searchTerm || searchText.includes(searchTerm);
                const matchesProvider = !provider || itemProvider === provider;
                const matchesStatus = !status || itemStatus === status;
                
                if (matchesSearch && matchesProvider && matchesStatus) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function confirmDelete(modelId, modelName) {
            deleteModelId = modelId;
            document.getElementById('deleteModelName').textContent = modelName;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            if (deleteModelId) {
                window.location.href = `/Admin/LLMModels/Delete/${deleteModelId}`;
            }
        });

        function toggleStatus(modelId, currentStatus) {
            const action = currentStatus ? 'deactivate' : 'activate';
            if (confirm(`Are you sure you want to ${action} this model?`)) {
                fetch(`/Admin/LLMModels/ToggleStatus/${modelId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]')?.value
                    }
                })
                .then(response => {
                    if (response.ok) {
                        location.reload();
                    } else {
                        alert('Error updating model status');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error updating model status');
                });
            }
        }
    </script>
}
