@page
@model PersonalDataModel
@{
    ViewData["Title"] = "Personal Data";
    ViewData["ActivePage"] = ManageNavPages.PersonalData;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <div class="alert alert-warning">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <label>Your account contains personal data that you have given us. This page allows you to download or delete that data.</label>
        </div>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="card bg-base-100 shadow">
            <div class="card-body
                <h4 class="card-title">Download Your Data</h4>
                <p class="mb-4">Download all of your data in a single file.</p>
                <form id="download-data" asp-page="DownloadPersonalData" method="post" class="form-group">
                    <button class="btn btn-primary" type="submit">Download</button>
                </form>
            </div>
        </div>
        
        <div class="card bg-base-100 shadow">
            <div class="card-body">
                <h4 class="card-title">Delete Your Account</h4>
                <p class="mb-4">Permanently remove your account and all associated data.</p>
                <div>
                    <a id="delete" asp-page="DeletePersonalData" class="btn btn-error">Delete</a>
                </div>
            </div>
        </div>
    </div>
</div>
