@page
@using RecruiterBot.Web.Models
@model RecruiterBot.Web.Pages.BotConfigurations.CreateModel
@{
    ViewData["Title"] = "Create Bot Configuration";
}

<div class="container-fluid">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="./Index">Bot Configurations</a></li>
            <li class="breadcrumb-item active" aria-current="page">Create</li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">New Bot Configuration</h6>
        </div>
        <div class="card-body">
            <form method="post">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input asp-for="Input.Query" class="form-control" placeholder="Enter search query" />
                            <label asp-for="Input.Query"></label>
                            <span asp-validation-for="Input.Query" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <select asp-for="Input.CandidateSearch" class="form-select" asp-items="Html.GetEnumSelectList<CandidateSearchType>()" id="candidateSearchType">
                            </select>
                            <label asp-for="Input.CandidateSearch"></label>
                        </div>
                    </div>
                </div>

                <!-- Candidate Selection Section (shown only for Manual search type) -->
                <div id="candidateSelectionSection" class="card mb-4" style="display: none;">
                    <div class="card-header">
                        <h6 class="mb-0">Select Candidates</h6>
                        <small class="text-muted">Choose candidates to assign to this bot configuration</small>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <select asp-for="Input.SelectedCandidates" class="form-select" multiple size="8" asp-items="Model.AvailableCandidates">
                                </select>
                                <div class="form-text">Hold Ctrl (Cmd on Mac) to select multiple candidates</div>
                                <span asp-validation-for="Input.SelectedCandidates" class="text-danger"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">LinkedIn Credentials (Optional)</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input asp-for="Input.LinkedInUsername" class="form-control" placeholder="LinkedIn username" />
                                    <label asp-for="Input.LinkedInUsername"></label>
                                    <div class="form-text">Leave blank if you'll provide credentials later.</div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <div class="form-floating">
                                    <input asp-for="Input.LinkedInPassword" class="form-control" placeholder="LinkedIn password" />
                                    <label asp-for="Input.LinkedInPassword"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-check form-switch mb-4">
                    <input class="form-check-input" type="checkbox" asp-for="Input.IsActive" checked>
                    <label class="form-check-label" asp-for="Input.IsActive"></label>
                </div>

                <div class="d-flex justify-content-between">
                    <a asp-page="./Index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to List
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save me-2"></i>Create Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const candidateSearchType = document.getElementById('candidateSearchType');
            const candidateSelectionSection = document.getElementById('candidateSelectionSection');

            function toggleCandidateSelection() {
                if (candidateSearchType.value === '2') { // Manual = 2
                    candidateSelectionSection.style.display = 'block';
                } else {
                    candidateSelectionSection.style.display = 'none';
                }
            }

            // Initial check
            toggleCandidateSelection();

            // Listen for changes
            candidateSearchType.addEventListener('change', toggleCandidateSelection);
        });
    </script>
}
