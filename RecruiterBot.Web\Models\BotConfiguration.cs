using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using RecruiterBot.Web.Services;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;

namespace RecruiterBot.Web.Models
{
    public class BotConfiguration
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("last_run_utc")]
        public DateTime? LastRunUtc { get; set; }


        [BsonElement("query")]
        public string Query { get; set; }

        [BsonElement("tenant")]
        public string Tenant { get; set; }


        // These properties are only used for form binding and are not stored in the database
        [NotMapped]
        [Display(Name = "LinkedIn Username")]
        public string LinkedInUsername { get; set; }

        [NotMapped]
        [Display(Name = "LinkedIn Password")]
        [DataType(DataType.Password)]
        public string LinkedInPassword { get; set; }

        // These properties store the encrypted values in the database
        [BsonElement("enc_linkedIn_username")]
        public string EncryptedLinkedInUsername { get; set; }

        [BsonElement("enc_linkedIn_password")]
        public string EncryptedLinkedInPassword { get; set; }


        [BsonElement("candidate_search")]
        [BsonRepresentation(BsonType.String)]
        public CandidateSearchType CandidateSearch { get; set; }


        [BsonElement("assigned_candidates")]
        public List<string> AssignedCandidates { get; set; } = new List<string>();

        [BsonElement("is_active")]
        public bool IsActive { get; set; }


        [BsonElement("created_date_utc")]
        public DateTime CreatedDateUtc { get; set; } = DateTime.UtcNow;

        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        [BsonElement("user_id")]
        public string UserId { get; set; }

        [BsonIgnore]
        [JsonIgnore]
        public ICollection<JobRun> JobRuns { get; set; } = new List<JobRun>();

        // Helper methods to handle encryption/decryption
        public void EncryptCredentials(IEncryptionService encryptionService)
        {
            if (!string.IsNullOrEmpty(LinkedInUsername))
                EncryptedLinkedInUsername = encryptionService.Encrypt(LinkedInUsername);
            
            if (!string.IsNullOrEmpty(LinkedInPassword))
                EncryptedLinkedInPassword = encryptionService.Encrypt(LinkedInPassword);
        }

        public void DecryptCredentials(IEncryptionService encryptionService)
        {
            if (!string.IsNullOrEmpty(EncryptedLinkedInUsername))
                LinkedInUsername = encryptionService.Decrypt(EncryptedLinkedInUsername);
            
            if (!string.IsNullOrEmpty(EncryptedLinkedInPassword))
                LinkedInPassword = encryptionService.Decrypt(EncryptedLinkedInPassword);
        }
    }
}
