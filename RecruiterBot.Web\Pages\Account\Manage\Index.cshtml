@page "/Account/Manage/Index"
@model RecruiterBot.Web.Pages.Account.Manage.IndexModel
@{
    ViewData["Title"] = "Profile Settings";
    ViewData["ActivePage"] = ManageNavPages.Index;
    Layout = "_AuthenticatedLayout";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0">@ViewData["Title"]</h1>
            <p class="text-muted">Manage your account information and preferences</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="alert alert-success">
                    @TempData["SuccessMessage"]
                </div>
            }
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body">
                    <form id="profile-form" method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="row g-3">
                            <div class="form-control">
                                <label asp-for="Input.FirstName" class="form-label">First Name</label>
                                <input asp-for="Input.FirstName" class="form-control" required />
                                <div class="invalid-feedback">
                                    Please provide your first name.
                                </div>
                                <span asp-validation-for="Input.FirstName" class="text-danger small"></span>
                            </div>

                            <div class="form-control">
                                <label asp-for="Input.LastName" class="form-label">Last Name</label>
                                <input asp-for="Input.LastName" class="form-control" required />
                                <div class="invalid-feedback">
                                    Please provide your last name.
                                </div>
                                <span asp-validation-for="Input.LastName" class="text-danger small"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" disabled />
                            <span asp-validation-for="Input.Email" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.PhoneNumber" class="form-label">Phone Number</label>
                            <input asp-for="Input.PhoneNumber" class="form-control" />
                            <span asp-validation-for="Input.PhoneNumber" class="text-danger small"></span>
                        </div>

                        <div class="mb-4">
                            <label class="form-label">Profile Picture</label>
                            <div class="d-flex align-items-center gap-3">
                                <div class="position-relative">
                                    <img src="@Model.ProfileImageUrl" alt="Profile" class="rounded-circle border" style="width: 64px; height: 64px; object-fit: cover;" />
                                </div>
                                <div class="flex-grow-1">
                                    <input type="file" asp-for="Input.ProfileImage" class="form-control" accept="image/*" />
                                    <div class="form-text">JPG, GIF or PNG. Max size 2MB</div>
                                    <span asp-validation-for="Input.ProfileImage" class="text-danger small"></span>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <button type="submit" class="btn btn-primary px-4">
                                <span class="spinner-border spinner-border-sm d-none me-1" role="status" aria-hidden="true"></span>
                                Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Form validation and submission handling
        document.addEventListener('DOMContentLoaded', function() {
            // Profile image preview
            const profileImageInput = document.getElementById('Input_ProfileImage');
            const profileImage = document.querySelector('.rounded-circle[alt="Profile"]');
            
            if (profileImageInput && profileImage) {
                profileImageInput.addEventListener('change', function(e) {
                    const file = e.target.files[0];
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = function(event) {
                            profileImage.src = event.target.result;
                        };
                        reader.readAsDataURL(file);
                    }
                });
            }

            // Form submission with loading state
            const form = document.getElementById('profile-form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    } else {
                        const submitButton = form.querySelector('button[type="submit"]');
                        if (submitButton) {
                            const spinner = submitButton.querySelector('.spinner-border');
                            const buttonText = submitButton.querySelector('span:not(.spinner-border)');
                            
                            submitButton.disabled = true;
                            spinner.classList.remove('d-none');
                            buttonText.textContent = 'Saving...';
                        }
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            }
        });
    </script>
}
