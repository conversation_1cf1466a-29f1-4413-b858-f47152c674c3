using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Candidates
{
    [Authorize]
    public class DeleteModel : PageModel
    {
        private readonly ICandidateService _candidateService;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<DeleteModel> _logger;

        public DeleteModel(
            ICandidateService candidateService,
            IFileStorageService fileStorageService,
            ILogger<DeleteModel> logger)
        {
            _candidateService = candidateService;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        public Candidate Candidate { get; set; }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public class InputModel
        {
            public string CandidateId { get; set; }
        }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                Candidate = await _candidateService.GetCandidateAsync(id, userId);
                if (Candidate == null)
                {
                    return NotFound();
                }

                // Set the candidate ID in the input model
                Input.CandidateId = Candidate.Id;

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidate {CandidateId} for deletion by user {UserId}", id, userId);
                TempData["ErrorMessage"] = "Error loading candidate information.";
                return RedirectToPage("./Index");
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (string.IsNullOrEmpty(Input?.CandidateId))
            {
                return NotFound();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                // Get the candidate first to access file information
                var candidateToDelete = await _candidateService.GetCandidateAsync(Input.CandidateId, userId);
                if (candidateToDelete == null)
                {
                    TempData["ErrorMessage"] = "Candidate not found.";
                    return RedirectToPage("./Index");
                }

                // Delete the candidate from database
                var deleted = await _candidateService.DeleteCandidateAsync(Input.CandidateId, userId);
                
                if (deleted)
                {
                    // Try to delete the associated resume file if it exists
                    if (!string.IsNullOrEmpty(candidateToDelete.ResumeFilePath))
                    {
                        try
                        {
                            var isDeleted = await _fileStorageService.DeleteFileAsync(candidateToDelete.ResumeFilePath);
                            if (isDeleted)
                            {
                                _logger.LogInformation("Deleted resume file: {FilePath}", candidateToDelete.ResumeFilePath);
                            }
                        }
                        catch (Exception fileEx)
                        {
                            _logger.LogWarning(fileEx, "Could not delete resume file for candidate {CandidateId}", candidateToDelete.Id);
                            // Don't fail the operation if file deletion fails
                        }
                    }

                    TempData["SuccessMessage"] = $"Candidate '{candidateToDelete.FullName}' has been permanently deleted.";
                }
                else
                {
                    TempData["ErrorMessage"] = "Failed to delete candidate. It may have already been deleted.";
                }

                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting candidate {CandidateId} for user {UserId}", Input.CandidateId, userId);
                TempData["ErrorMessage"] = "An error occurred while deleting the candidate.";
                return RedirectToPage("./Index");
            }
        }
    }
}
