using MongoDB.Bson.Serialization.Attributes;

namespace RecruiterBot.Core.Models
{
    public class NotificationPreferences
    {
        [BsonElement("newMessage")]
        public bool NewMessage { get; set; } = true;

        [BsonElement("jobMatch")]
        public bool JobMatch { get; set; } = true;

        [BsonElement("applicationUpdate")]
        public bool ApplicationUpdate { get; set; } = true;

        [BsonElement("newsletter")]
        public bool Newsletter { get; set; } = true;
    }
}
