@page "{id}"
@using RecruiterBot.Web.Models
@model RecruiterBot.Web.Pages.BotConfigurations.DetailsModel
@{
    ViewData["Title"] = "Bot Configuration Details";
}

<div class="container-fluid">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="./Index">Bot Configurations</a></li>
            <li class="breadcrumb-item active" aria-current="page">Details</li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Configuration Details</h6>
            <div>
                <a asp-page="./Edit" asp-route-id="@Model.Configuration.Id" class="btn btn-sm btn-primary">
                    <i class="bi bi-pencil-square me-1"></i>Edit
                </a>
            </div>
        </div>
        <div class="card-body">
            <dl class="row">
                <dt class="col-sm-3">Search Query</dt>
                <dd class="col-sm-9">@Model.Configuration.Query</dd>

                <dt class="col-sm-3">Candidate Search</dt>
                <dd class="col-sm-9">@Model.Configuration.CandidateSearch.ToString()</dd>

                @if (Model.Configuration.CandidateSearch == RecruiterBot.Web.Models.CandidateSearchType.Manual && Model.AssignedCandidates.Any())
                {
                    <dt class="col-sm-3">Assigned Candidates</dt>
                    <dd class="col-sm-9">
                        <div class="d-flex flex-wrap gap-2">
                            @foreach (var candidate in Model.AssignedCandidates)
                            {
                                <a asp-page="/Candidates/Details" asp-route-id="@candidate.Id" class="badge bg-primary text-decoration-none">
                                    @candidate.FullName
                                </a>
                            }
                        </div>
                        <small class="text-muted">@Model.AssignedCandidates.Count candidate(s) assigned</small>
                    </dd>
                }

                <dt class="col-sm-3">Status</dt>
                <dd class="col-sm-9">
                    @if (Model.Configuration.IsActive)
                    {
                        <span class="badge bg-success">Active</span>
                    }
                    else
                    {
                        <span class="badge bg-secondary">Inactive</span>
                    }
                </dd>

                <dt class="col-sm-3">Last Run</dt>
                <dd class="col-sm-9">@(Model.Configuration.LastRunUtc?.ToString("g") ?? "Never")</dd>

                <dt class="col-sm-3">Created</dt>
                <dd class="col-sm-9">@Model.Configuration.CreatedDateUtc.ToString("g")</dd>

            </dl>

            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0">LinkedIn Account</h6>
                </div>
                <div class="card-body">
                    <dl class="row mb-0">
                        <dt class="col-sm-3">Username</dt>
                        <dd class="col-sm-9">
                            @if (!string.IsNullOrEmpty(Model.Configuration.EncryptedLinkedInUsername))
                            {
                                @Model.Configuration.EncryptedLinkedInUsername
                            }
                            else
                            {
                                <span class="text-muted">Not set</span>
                            }
                        </dd>
                        <dt class="col-sm-3">Password</dt>
                        <dd class="col-sm-9">
                            @if (!string.IsNullOrEmpty(Model.Configuration.EncryptedLinkedInPassword))
                            {
                                <span class="text-muted">•••••••••</span>
                            }
                            else
                            {
                                <span class="text-muted">Not set</span>
                            }
                        </dd>
                    </dl>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <a asp-page="./Index" class="btn btn-secondary">
                <i class="bi bi-arrow-left me-2"></i>Back to List
            </a>
        </div>
    </div>
</div>
