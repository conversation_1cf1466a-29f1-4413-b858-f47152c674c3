using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Core.Models
{
    [BsonIgnoreExtraElements]
    public class Interview
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("tenant")]
        public string Tenant { get; set; }

        [BsonElement("created_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreatedDateUtc { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime UpdatedDateUtc { get; set; } = DateTime.UtcNow;

        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        [BsonElement("job_description")]
        [Display(Name = "Job Description")]
        [Required(ErrorMessage = "Job description is required")]
        public string JobDescription { get; set; }

        [BsonElement("interview_date_time_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [Display(Name = "Interview Date & Time")]
        [Required(ErrorMessage = "Interview date and time is required")]
        public DateTime InterviewDateTimeUtc { get; set; }

        [BsonElement("candidate_id")]
        [Display(Name = "Candidate")]
        [Required(ErrorMessage = "Candidate selection is required")]
        public string CandidateId { get; set; }

        [BsonElement("consultant_id")]
        [Display(Name = "Consultant")]
        [Required(ErrorMessage = "Consultant assignment is required")]
        public string ConsultantId { get; set; }

        [BsonElement("llm_model_id")]
        [Display(Name = "LLM Model")]
        [Required(ErrorMessage = "LLM model selection is required")]
        public string LLMModelId { get; set; }

        [BsonElement("status")]
        [BsonRepresentation(BsonType.String)]
        [Display(Name = "Status")]
        public InterviewStatus Status { get; set; } = InterviewStatus.Scheduled;

        [BsonElement("notes")]
        [Display(Name = "Notes")]
        public string Notes { get; set; }

        [BsonElement("is_active")]
        public bool IsActive { get; set; } = true;

        [BsonElement("completed_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime? CompletedDateUtc { get; set; }

        [BsonElement("cancelled_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime? CancelledDateUtc { get; set; }

        [BsonElement("cancellation_reason")]
        public string CancellationReason { get; set; }

        // Navigation properties (not stored in MongoDB)
        [BsonIgnore]
        public Candidate Candidate { get; set; }

        [BsonIgnore]
        public User Consultant { get; set; }

        [BsonIgnore]
        public LLMModel LLMModel { get; set; }

        // Helper properties
        [BsonIgnore]
        public string StatusDisplayName => Status switch
        {
            InterviewStatus.Scheduled => "Scheduled",
            InterviewStatus.InProgress => "In Progress",
            InterviewStatus.Completed => "Completed",
            InterviewStatus.Cancelled => "Cancelled",
            InterviewStatus.Rescheduled => "Rescheduled",
            _ => "Unknown"
        };

        [BsonIgnore]
        public string StatusBadgeClass => Status switch
        {
            InterviewStatus.Scheduled => "badge bg-primary",
            InterviewStatus.InProgress => "badge bg-warning",
            InterviewStatus.Completed => "badge bg-success",
            InterviewStatus.Cancelled => "badge bg-danger",
            InterviewStatus.Rescheduled => "badge bg-info",
            _ => "badge bg-secondary"
        };

        [BsonIgnore]
        public bool IsUpcoming => Status == InterviewStatus.Scheduled && InterviewDateTimeUtc > DateTime.UtcNow;

        [BsonIgnore]
        public bool IsPast => InterviewDateTimeUtc < DateTime.UtcNow;
    }

    public enum InterviewStatus
    {
        Scheduled,
        InProgress,
        Completed,
        Cancelled,
        Rescheduled
    }
}
