using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;

namespace RecruiterBot.Web.Pages;

public class IndexModel : PageModel
{
    private readonly ILogger<IndexModel> _logger;

    public IndexModel(ILogger<IndexModel> logger)
    {
        _logger = logger;
    }

    public void OnGet()
    {
        _logger.LogInformation("Home page accessed at {Timestamp} from IP {RemoteIpAddress}",
            DateTime.UtcNow,
            HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown");

        _logger.LogDebug("Debug log: Processing home page request with User-Agent: {UserAgent}",
            HttpContext.Request.Headers.UserAgent.ToString());
    }
}
