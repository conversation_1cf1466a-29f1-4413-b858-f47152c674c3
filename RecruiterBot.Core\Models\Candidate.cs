using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Core.Models
{
    [BsonIgnoreExtraElements]
    public class Candidate
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("tenant")]
        public string Tenant { get; set; }

        [BsonElement("created_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreatedDateUtc { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime UpdatedDateUtc { get; set; } = DateTime.UtcNow;

        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        // Personal Information
        [BsonElement("first_name")]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [BsonElement("last_name")]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [BsonElement("email")]
        [Display(Name = "Email")]
        [EmailAddress]
        public string Email { get; set; }

        [BsonElement("phone")]
        [Display(Name = "Phone")]
        public string Phone { get; set; }

        [BsonElement("address")]
        [Display(Name = "Address")]
        public string Address { get; set; }

        [BsonElement("city")]
        [Display(Name = "City")]
        public string City { get; set; }

        [BsonElement("state")]
        [Display(Name = "State")]
        public string State { get; set; }

        [BsonElement("zip_code")]
        [Display(Name = "Zip Code")]
        public string ZipCode { get; set; }

        [BsonElement("country")]
        [Display(Name = "Country")]
        public string Country { get; set; }

        [BsonElement("linkedin_url")]
        [Display(Name = "LinkedIn URL")]
        public string LinkedInUrl { get; set; }

        [BsonElement("portfolio_url")]
        [Display(Name = "Portfolio URL")]
        public string PortfolioUrl { get; set; }

        // Professional Information
        [BsonElement("current_title")]
        [Display(Name = "Current Title")]
        public string CurrentTitle { get; set; }

        [BsonElement("current_company")]
        [Display(Name = "Current Company")]
        public string CurrentCompany { get; set; }

        [BsonElement("years_of_experience")]
        [Display(Name = "Years of Experience")]
        public int? YearsOfExperience { get; set; }

        [BsonElement("skills")]
        [Display(Name = "Skills")]
        public List<string> Skills { get; set; } = new List<string>();

        [BsonElement("summary")]
        [Display(Name = "Professional Summary")]
        public string Summary { get; set; }

        // Work Experience
        [BsonElement("work_experience")]
        [Display(Name = "Work Experience")]
        public List<WorkExperience> WorkExperience { get; set; } = new List<WorkExperience>();

        // Education
        [BsonElement("education")]
        [Display(Name = "Education")]
        public List<Education> Education { get; set; } = new List<Education>();

        // Certifications
        [BsonElement("certifications")]
        [Display(Name = "Certifications")]
        public List<string> Certifications { get; set; } = new List<string>();

        // Resume Information
        [BsonElement("original_resume_filename")]
        public string OriginalResumeFilename { get; set; }

        [BsonElement("resume_file_path")]
        public string ResumeFilePath { get; set; }

        [BsonElement("resume_text")]
        public string ResumeText { get; set; }

        [BsonElement("parsing_status")]
        [BsonRepresentation(BsonType.String)]
        public ParsingStatus ParsingStatus { get; set; } = ParsingStatus.Pending;

        [BsonElement("parsing_error")]
        public string ParsingError { get; set; }

        [BsonElement("ai_confidence_score")]
        public double? AiConfidenceScore { get; set; }

        [BsonElement("is_active")]
        public bool IsActive { get; set; } = true;

        [BsonElement("deactivated_date_utc")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime? DeactivatedDateUtc { get; set; }

        [BsonElement("deactivated_by")]
        public string DeactivatedBy { get; set; }

        [BsonElement("deactivation_reason")]
        public string DeactivationReason { get; set; }

        // Bot Associations
        [BsonElement("assigned_bot_ids")]
        public List<string> AssignedBotIds { get; set; } = new List<string>();

        [BsonElement("assigned_bot_queries")]
        public List<string> AssignedBotQueries { get; set; } = new List<string>();

        // Helper property for display
        [BsonIgnore]
        public string FullName => $"{FirstName} {LastName}".Trim();
    }

    public class WorkExperience
    {
        [BsonElement("company")]
        [Display(Name = "Company")]
        public string Company { get; set; }

        [BsonElement("title")]
        [Display(Name = "Job Title")]
        public string Title { get; set; }

        [BsonElement("start_date")]
        [Display(Name = "Start Date")]
        public string StartDate { get; set; }

        [BsonElement("end_date")]
        [Display(Name = "End Date")]
        public string EndDate { get; set; }

        [BsonElement("description")]
        [Display(Name = "Description")]
        public string Description { get; set; }

        [BsonElement("location")]
        [Display(Name = "Location")]
        public string Location { get; set; }
    }

    public class Education
    {
        [BsonElement("institution")]
        [Display(Name = "Institution")]
        public string Institution { get; set; }

        [BsonElement("degree")]
        [Display(Name = "Degree")]
        public string Degree { get; set; }

        [BsonElement("field_of_study")]
        [Display(Name = "Field of Study")]
        public string FieldOfStudy { get; set; }

        [BsonElement("graduation_year")]
        [Display(Name = "Graduation Year")]
        public string GraduationYear { get; set; }

        [BsonElement("gpa")]
        [Display(Name = "GPA")]
        public string GPA { get; set; }
    }

    public enum ParsingStatus
    {
        Pending,
        InProgress,
        Completed,
        Failed,
        Manual
    }
}
