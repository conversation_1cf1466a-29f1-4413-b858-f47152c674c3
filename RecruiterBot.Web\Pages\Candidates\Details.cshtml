@page "{id?}"
@model RecruiterBot.Web.Pages.Candidates.DetailsModel
@{
    ViewData["Title"] = "Candidate Details";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-person me-2"></i>
                        @Model.Candidate.FullName
                    </h3>
                    <div class="card-tools">
                        <a asp-page="./Edit" asp-route-id="@Model.Candidate.Id" class="btn btn-primary btn-sm">
                            <i class="bi bi-pencil me-2"></i>
                            Edit
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Personal Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-person me-2"></i>
                                Personal Information
                            </h5>
                            
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>@Model.Candidate.FullName</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.Candidate.Email))
                                {
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td><a href="mailto:@Model.Candidate.Email">@Model.Candidate.Email</a></td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.Phone))
                                {
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>@Model.Candidate.Phone</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.Address))
                                {
                                    <tr>
                                        <td><strong>Address:</strong></td>
                                        <td>
                                            @Model.Candidate.Address
                                            @if (!string.IsNullOrEmpty(Model.Candidate.City) || !string.IsNullOrEmpty(Model.Candidate.State))
                                            {
                                                <br>@Model.Candidate.City @Model.Candidate.State @Model.Candidate.ZipCode
                                            }
                                        </td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.LinkedInUrl))
                                {
                                    <tr>
                                        <td><strong>LinkedIn:</strong></td>
                                        <td><a href="@Model.Candidate.LinkedInUrl" target="_blank">@Model.Candidate.LinkedInUrl</a></td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.PortfolioUrl))
                                {
                                    <tr>
                                        <td><strong>Portfolio:</strong></td>
                                        <td><a href="@Model.Candidate.PortfolioUrl" target="_blank">@Model.Candidate.PortfolioUrl</a></td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.Country))
                                {
                                    <tr>
                                        <td><strong>Country:</strong></td>
                                        <td>@Model.Candidate.Country</td>
                                    </tr>
                                }
                            </table>
                        </div>

                        <!-- Professional Information -->
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-briefcase me-2"></i>
                                Professional Information
                            </h5>
                            
                            <table class="table table-borderless">
                                @if (!string.IsNullOrEmpty(Model.Candidate.CurrentTitle))
                                {
                                    <tr>
                                        <td><strong>Current Title:</strong></td>
                                        <td>@Model.Candidate.CurrentTitle</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.CurrentCompany))
                                {
                                    <tr>
                                        <td><strong>Current Company:</strong></td>
                                        <td>@Model.Candidate.CurrentCompany</td>
                                    </tr>
                                }
                                @if (Model.Candidate.YearsOfExperience.HasValue)
                                {
                                    <tr>
                                        <td><strong>Experience:</strong></td>
                                        <td>@Model.Candidate.YearsOfExperience years</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Parsing Status:</strong></td>
                                    <td>
                                        @switch (Model.Candidate.ParsingStatus)
                                        {
                                            case RecruiterBot.Core.Models.ParsingStatus.Completed:
                                                <span class="badge badge-success">
                                                    <i class="bi bi-check-circle me-1"></i>
                                                    AI Parsed
                                                </span>
                                                break;
                                            case RecruiterBot.Core.Models.ParsingStatus.Failed:
                                                <span class="badge badge-warning">
                                                    <i class="bi bi-exclamation-triangle me-1"></i>
                                                    Manual Entry
                                                </span>
                                                break;
                                            case RecruiterBot.Core.Models.ParsingStatus.InProgress:
                                                <span class="badge badge-info">
                                                    <i class="spinner-border spinner-border-sm me-1"></i>
                                                    Processing
                                                </span>
                                                break;
                                            default:
                                                <span class="badge badge-secondary">
                                                    <i class="bi bi-clock me-1"></i>
                                                    Pending
                                                </span>
                                                break;
                                        }
                                    </td>
                                </tr>
                                @if (Model.Candidate.AiConfidenceScore.HasValue)
                                {
                                    <tr>
                                        <td><strong>AI Confidence:</strong></td>
                                        <td>@((Model.Candidate.AiConfidenceScore.Value * 100).ToString("F1"))%</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Status:</strong></td>
                                    <td>
                                        @if (Model.Candidate.IsActive)
                                        {
                                            <span class="badge badge-success">
                                                <i class="bi bi-check-circle me-1"></i>
                                                Active
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge badge-secondary">
                                                <i class="bi bi-pause-circle me-1"></i>
                                                Inactive
                                            </span>
                                        }
                                    </td>
                                </tr>
                                @if (!Model.Candidate.IsActive && Model.Candidate.DeactivatedDateUtc.HasValue)
                                {
                                    <tr>
                                        <td><strong>Deactivated:</strong></td>
                                        <td>
                                            @Model.Candidate.DeactivatedDateUtc.Value.ToString("MMM dd, yyyy 'at' HH:mm")
                                            @if (!string.IsNullOrEmpty(Model.Candidate.DeactivationReason))
                                            {
                                                <br><small class="text-muted">Reason: @Model.Candidate.DeactivationReason</small>
                                            }
                                        </td>
                                    </tr>
                                }
                            </table>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h5 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                System Information
                            </h5>

                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        <tr>
                                            <td><strong>Created:</strong></td>
                                            <td>
                                                @Model.Candidate.CreatedDateUtc.ToString("MMM dd, yyyy 'at' HH:mm") UTC
                                                @if (!string.IsNullOrEmpty(Model.Candidate.CreatedBy))
                                                {
                                                    <br><small class="text-muted">by @Model.Candidate.CreatedBy</small>
                                                }
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Last Updated:</strong></td>
                                            <td>@Model.Candidate.UpdatedDateUtc.ToString("MMM dd, yyyy 'at' HH:mm") UTC</td>
                                        </tr>
                                        @if (!string.IsNullOrEmpty(Model.Candidate.Tenant))
                                        {
                                            <tr>
                                                <td><strong>Tenant:</strong></td>
                                                <td>@Model.Candidate.Tenant</td>
                                            </tr>
                                        }
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-borderless">
                                        @if (Model.Candidate.AssignedBotIds != null && Model.Candidate.AssignedBotIds.Any())
                                        {
                                            <tr>
                                                <td><strong>Assigned Bots:</strong></td>
                                                <td>
                                                    @foreach (var botId in Model.Candidate.AssignedBotIds)
                                                    {
                                                        <span class="badge bg-info me-1 mb-1">@botId</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                        @if (Model.Candidate.AssignedBotQueries != null && Model.Candidate.AssignedBotQueries.Any())
                                        {
                                            <tr>
                                                <td><strong>Bot Queries:</strong></td>
                                                <td>
                                                    @foreach (var query in Model.Candidate.AssignedBotQueries)
                                                    {
                                                        <span class="badge bg-secondary me-1 mb-1">@query</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                        @if (!string.IsNullOrEmpty(Model.Candidate.ParsingError))
                                        {
                                            <tr>
                                                <td><strong>Parsing Error:</strong></td>
                                                <td>
                                                    <span class="text-warning">@Model.Candidate.ParsingError</span>
                                                </td>
                                            </tr>
                                        }
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Skills -->
                    @if (Model.Candidate.Skills != null && Model.Candidate.Skills.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-gear me-2"></i>
                                    Skills
                                </h5>
                                <div>
                                    @foreach (var skill in Model.Candidate.Skills)
                                    {
                                        <span class="badge badge-secondary mr-2 mb-2">@skill</span>
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Summary -->
                    @if (!string.IsNullOrEmpty(Model.Candidate.Summary))
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-file-text me-2"></i>
                                    Professional Summary
                                </h5>
                                <p class="text-justify">@Model.Candidate.Summary</p>
                            </div>
                        </div>
                    }

                    <!-- Work Experience -->
                    @if (Model.Candidate.WorkExperience != null && Model.Candidate.WorkExperience.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-briefcase me-2"></i>
                                    Work Experience
                                </h5>
                                @foreach (var exp in Model.Candidate.WorkExperience)
                                {
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">@exp.Title</h6>
                                            <h6 class="card-subtitle mb-2 text-muted">@exp.Company</h6>
                                            @if (!string.IsNullOrEmpty(exp.StartDate) || !string.IsNullOrEmpty(exp.EndDate))
                                            {
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        @exp.StartDate @(string.IsNullOrEmpty(exp.EndDate) ? "- Present" : $"- {exp.EndDate}")
                                                        @if (!string.IsNullOrEmpty(exp.Location))
                                                        {
                                                            <span> | @exp.Location</span>
                                                        }
                                                    </small>
                                                </p>
                                            }
                                            @if (!string.IsNullOrEmpty(exp.Description))
                                            {
                                                <p class="card-text">@exp.Description</p>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Education -->
                    @if (Model.Candidate.Education != null && Model.Candidate.Education.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-mortarboard me-2"></i>
                                    Education
                                </h5>
                                @foreach (var edu in Model.Candidate.Education)
                                {
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <h6 class="card-title">@edu.Degree @(!string.IsNullOrEmpty(edu.FieldOfStudy) ? $"in {edu.FieldOfStudy}" : "")</h6>
                                            <h6 class="card-subtitle mb-2 text-muted">@edu.Institution</h6>
                                            @if (!string.IsNullOrEmpty(edu.GraduationYear))
                                            {
                                                <p class="card-text">
                                                    <small class="text-muted">Graduated: @edu.GraduationYear</small>
                                                    @if (!string.IsNullOrEmpty(edu.GPA))
                                                    {
                                                        <span> | GPA: @edu.GPA</span>
                                                    }
                                                </p>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    }

                    <!-- Certifications -->
                    @if (Model.Candidate.Certifications != null && Model.Candidate.Certifications.Any())
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-award me-2"></i>
                                    Certifications
                                </h5>
                                <ul class="list-unstyled">
                                    @foreach (var cert in Model.Candidate.Certifications)
                                    {
                                        <li><i class="bi bi-check-circle text-success me-2"></i>@cert</li>
                                    }
                                </ul>
                            </div>
                        </div>
                    }

                    <!-- Resume File -->
                    @if (!string.IsNullOrEmpty(Model.Candidate.ResumeFilePath))
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-file-earmark-pdf me-2"></i>
                                    Resume File
                                </h5>
                                <p>
                                    <a href="@Model.Candidate.ResumeFilePath" target="_blank" class="btn btn-outline-primary">
                                        <i class="bi bi-download me-2"></i>
                                        Download Resume (@Model.Candidate.OriginalResumeFilename)
                                    </a>
                                </p>
                            </div>
                        </div>
                    }

                    <!-- Resume Text Content -->
                    @if (!string.IsNullOrEmpty(Model.Candidate.ResumeText))
                    {
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-file-text me-2"></i>
                                    Resume Text Content
                                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" data-bs-toggle="collapse" data-bs-target="#resumeTextContent" aria-expanded="false">
                                        <i class="bi bi-eye me-1"></i>Toggle View
                                    </button>
                                </h5>
                                <div class="collapse" id="resumeTextContent">
                                    <div class="card">
                                        <div class="card-body">
                                            <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit; font-size: 0.9em;">@Model.Candidate.ResumeText</pre>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <a asp-page="./Edit" asp-route-id="@Model.Candidate.Id" class="btn btn-primary">
                                <i class="bi bi-pencil me-2"></i>
                                Edit Candidate
                            </a>

                            @if (Model.Candidate.IsActive)
                            {
                                <a asp-page="./Deactivate" asp-route-id="@Model.Candidate.Id" class="btn btn-warning ms-2">
                                    <i class="bi bi-pause-circle me-2"></i>
                                    Deactivate
                                </a>
                            }
                            else
                            {
                                <button type="button" class="btn btn-success ms-2" onclick="activateCandidate('@Model.Candidate.Id')">
                                    <i class="bi bi-play-circle me-2"></i>
                                    Activate
                                </button>
                            }

                            <a asp-page="./Delete" asp-route-id="@Model.Candidate.Id" class="btn btn-danger ms-2">
                                <i class="bi bi-trash me-2"></i>
                                Delete
                            </a>

                            <a asp-page="./Index" class="btn btn-secondary ms-2">
                                <i class="bi bi-arrow-left me-2"></i>
                                Back to Candidates
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function activateCandidate(candidateId) {
            if (confirm('Are you sure you want to activate this candidate?')) {
                // Show loading state
                var button = event.target;
                var originalHtml = button.innerHTML;
                button.disabled = true;
                button.innerHTML = '<i class="spinner-border spinner-border-sm me-2"></i>Activating...';

                // Make AJAX call to activate candidate
                fetch('/Candidates/Activate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    body: JSON.stringify({ candidateId: candidateId })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Reload the page to show updated status
                        location.reload();
                    } else {
                        alert('Error activating candidate: ' + (data.message || 'Unknown error'));
                        button.disabled = false;
                        button.innerHTML = originalHtml;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error activating candidate');
                    button.disabled = false;
                    button.innerHTML = originalHtml;
                });
            }
        }
    </script>
}
