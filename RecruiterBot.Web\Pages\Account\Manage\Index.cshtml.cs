using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using Microsoft.AspNetCore.Hosting;
using System.IO;
using System;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Logging;
using RecruiterBot.Web.Services;

namespace RecruiterBot.Web.Pages.Account.Manage
{
    [Authorize]
    public class IndexModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly UserProfileService _profileService;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(
            UserManager<User> userManager,
            UserProfileService profileService,
            IWebHostEnvironment environment,
            ILogger<IndexModel> logger)
        {
            _userManager = userManager;
            _profileService = profileService;
            _environment = environment;
            _logger = logger;
        }

        public string Username { get; set; } = string.Empty;
        public string ProfileImageUrl { get; set; } = string.Empty;

        [TempData]
        public string? StatusMessage { get; set; }

        [BindProperty]
        public InputModel Input { get; set; }

        public class InputModel
        {
            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; } = string.Empty;

            [Required]
            [Display(Name = "First Name")]
            public string FirstName { get; set; } = string.Empty;

            [Required]
            [Display(Name = "Last Name")]
            public string LastName { get; set; } = string.Empty;

            [Phone]
            [Display(Name = "Phone number")]
            public string? PhoneNumber { get; set; }

            [Display(Name = "Profile Image")]
            public IFormFile? ProfileImage { get; set; }
        }


        private async Task LoadAsync(User user)
        {
            var userName = await _userManager.GetUserNameAsync(user);
            var phoneNumber = await _userManager.GetPhoneNumberAsync(user);
            var profileImageUrl = await _profileService.GetUserAvatarUrlAsync(user.Id);

            Username = userName;
            ProfileImageUrl = string.IsNullOrEmpty(profileImageUrl) 
                ? "/images/default-avatar.svg" 
                : profileImageUrl;

            Input = new InputModel
            {
                Email = user.Email ?? string.Empty,
                FirstName = user.FirstName,
                LastName = user.LastName,
                PhoneNumber = phoneNumber
            };
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            ViewData["ActivePage"] = ManageNavPages.Index;
            await LoadAsync(user);
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            if (!ModelState.IsValid)
            {
                await LoadAsync(user);
                return Page();
            }

            // Handle profile image upload
            if (Input.ProfileImage != null && Input.ProfileImage.Length > 0)
            {
                try
                {
                    var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "profile-images");
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    var uniqueFileName = $"{Guid.NewGuid()}_{Input.ProfileImage.FileName}";
                    var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await Input.ProfileImage.CopyToAsync(fileStream);
                    }

                    var imageUrl = $"/uploads/profile-images/{uniqueFileName}";
                    var updateProfileResult = await _profileService.UpdateProfileAsync(
                        user.Id,
                        Input.FirstName,
                        Input.LastName,
                        Input.Email,
                        Input.ProfileImage);
                    
                    if (!updateProfileResult.Success)
                    {
                        ModelState.AddModelError(string.Empty, updateProfileResult.ErrorMessage ?? "Error updating profile.");
                        await LoadAsync(user);
                        return Page();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error uploading profile image");
                    ModelState.AddModelError(string.Empty, "Error uploading profile image. Please try again.");
                    await LoadAsync(user);
                    return Page();
                }
            }

            // Update user properties using the profile service
            var (success, error) = await _profileService.UpdateProfileAsync(
                user.Id,
                Input.FirstName,
                Input.LastName,
                Input.Email,
                null); // No profile image update here
                
            if (!success)
            {
                ModelState.AddModelError(string.Empty, error ?? "Error updating profile.");
                await LoadAsync(user);
                return Page();
            }
            
            StatusMessage = "Your profile has been updated";
            return RedirectToPage();
        }
    }
}
