@page
@model LogoutModel
@{
    ViewData["Title"] = "Sign Out";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    @if (User.Identity.IsAuthenticated)
                    {
                        <div class="text-center mb-4">
                            <i class="bi bi-box-arrow-right" style="font-size: 3rem; color: #dc3545;"></i>
                            <h2 class="mt-3">Sign Out</h2>
                            <p class="text-muted">Are you sure you want to sign out of your account?</p>
                        </div>

                        <form method="post">
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-danger" id="logoutBtn">
                                    <i class="bi bi-box-arrow-right me-2"></i>
                                    Yes, Sign Me Out
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-4">
                            <a href="javascript:history.back()" class="text-decoration-none">
                                <i class="bi bi-arrow-left me-1"></i>
                                Cancel
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="text-center mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 text-success">Signed Out Successfully</h2>
                            <p class="text-muted">You have been successfully signed out of your account.</p>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            For your security, please close your browser if you're using a shared computer.
                        </div>

                        <div class="d-grid gap-2">
                            <a asp-page="./Login" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                Sign In Again
                            </a>
                        </div>

                        <div class="text-center mt-3">
                            <a href="/" class="text-decoration-none">
                                <i class="bi bi-house me-1"></i>
                                Return to Home
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#logoutBtn').html();

            // Show loading state when form is submitted
            $('form').on('submit', function() {
                var $btn = $('#logoutBtn');
                if ($btn.length) {
                    $btn.prop('disabled', true);
                    $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Signing out...');
                }
            });
        });
    </script>
}
