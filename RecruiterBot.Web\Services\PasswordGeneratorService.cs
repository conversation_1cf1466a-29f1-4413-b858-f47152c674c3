using System;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Services
{
    public class PasswordGeneratorService : IPasswordGeneratorService
    {
        private const string UppercaseChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        private const string LowercaseChars = "abcdefghijklmnopqrstuvwxyz";
        private const string DigitChars = "0123456789";
        private const string SpecialChars = "!@#$%^&*";

        public string GenerateTemporaryPassword(int length = 12)
        {
            if (length < 8)
                throw new ArgumentException("Password length must be at least 8 characters", nameof(length));

            var allChars = UppercaseChars + LowercaseChars + DigitChars + SpecialChars;
            var password = new StringBuilder();

            using (var rng = RandomNumberGenerator.Create())
            {
                // Ensure at least one character from each category
                password.Append(GetRandomChar(UppercaseChars, rng));
                password.Append(GetRandomChar(LowercaseChars, rng));
                password.Append(GetRandomChar(DigitChars, rng));
                password.Append(GetRandomChar(SpecialChars, rng));

                // Fill the rest with random characters
                for (int i = 4; i < length; i++)
                {
                    password.Append(GetRandomChar(allChars, rng));
                }
            }

            // Shuffle the password to avoid predictable patterns
            return ShuffleString(password.ToString());
        }

        public string GenerateActivationToken()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                var bytes = new byte[32];
                rng.GetBytes(bytes);
                return Convert.ToBase64String(bytes).Replace("+", "-").Replace("/", "_").Replace("=", "");
            }
        }

        private char GetRandomChar(string chars, RandomNumberGenerator rng)
        {
            var bytes = new byte[4];
            rng.GetBytes(bytes);
            var randomIndex = Math.Abs(BitConverter.ToInt32(bytes, 0)) % chars.Length;
            return chars[randomIndex];
        }

        private string ShuffleString(string input)
        {
            var array = input.ToCharArray();
            using (var rng = RandomNumberGenerator.Create())
            {
                for (int i = array.Length - 1; i > 0; i--)
                {
                    var bytes = new byte[4];
                    rng.GetBytes(bytes);
                    var j = Math.Abs(BitConverter.ToInt32(bytes, 0)) % (i + 1);
                    (array[i], array[j]) = (array[j], array[i]);
                }
            }
            return new string(array);
        }
    }
}
