# LinkedIn Credentials Encryption

This document explains how LinkedIn credentials are encrypted in the RecruiterBot application and how to work with them.

## Encryption Overview

LinkedIn credentials (username and password) are encrypted using AES-256-CBC encryption before being stored in the database. The encryption uses:

- **Algorithm**: AES-256-CBC
- **Key**: 32-byte (256-bit) encryption key
- **IV**: 16-byte initialization vector
- **Padding**: PKCS#7

## Configuration

The encryption settings are stored in `appsettings.json`:

```json
"Encryption": {
  "Key": "YourSuperSecretKeyMustBe32CharsLong256Bits",
  "IV": "16ByteIV12345678"
}
```

**Important**: In production, these values should be stored in a secure configuration system like Azure Key Vault, AWS Secrets Manager, or environment variables.

## .NET Implementation

### Encryption Service

The `AesEncryptionService` class handles encryption and decryption in the .NET application. It's registered as a singleton in `Program.cs`.

### BotConfiguration Model

The `BotConfiguration` model has been updated to handle encryption transparently:

- `LinkedInUsername` and `LinkedInPassword` properties are used in the UI and forms
- `EncryptedLinkedInUsername` and `EncryptedLinkedInPassword` are stored in the database
- `EncryptCredentials` and `DecryptCredentials` methods handle the conversion

## Python Implementation

For Python services that need to decrypt the credentials, use the provided `decrypt_credentials.py` script as a reference.

### Prerequisites

Install the required Python packages:

```bash
pip install pycryptodome pymongo
```

### Usage Example

```python
from decrypt_credentials import AesDecryptor

# Initialize with the same key and IV as in appsettings.json
decryptor = AesDecryptor(
    key="YourSuperSecretKeyMustBe32CharsLong256Bits",
    iv="16ByteIV12345678"
)

# Decrypt credentials
encrypted_username = "..."  # From MongoDB
encrypted_password = "..."  # From MongoDB

username = decryptor.decrypt(encrypted_username)
password = decryptor.decrypt(encrypted_password)

print(f"Username: {username}")
print(f"Password: {password}")
```

## Security Considerations

1. **Key Management**: The encryption key should be rotated periodically in production.
2. **Access Control**: Limit access to the encryption key and the MongoDB database.
3. **Environment Variables**: Consider using environment variables or a secure secret manager for production keys.
4. **Audit Logging**: Log access to sensitive operations involving credentials.
5. **Network Security**: Ensure all communications with the database are encrypted (TLS/SSL).

## Troubleshooting

- **Decryption fails**: Ensure the key and IV match exactly between encryption and decryption.
- **Padding errors**: The encryption uses PKCS#7 padding. Make sure the decryption code uses the same padding scheme.
- **Character encoding**: Both sides should use UTF-8 for string-to-bytes conversion.

## Future Improvements

1. Implement key rotation
2. Add key versioning to support seamless key rotation
3. Add key derivation (e.g., PBKDF2) for additional security
4. Add key storage in a dedicated key management service
