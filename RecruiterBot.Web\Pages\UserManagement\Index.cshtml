@page
@model RecruiterBot.Web.Pages.UserManagement.IndexModel
@{
    ViewData["Title"] = "User Management";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-people me-2"></i>
                        User Management
                    </h2>
                    <p class="text-muted mb-0">Manage users and their roles</p>
                </div>
                @if (Model.CreatableRoles.Any())
                {
                    <a asp-page="./Create" class="btn btn-primary">
                        <i class="bi bi-person-plus me-2"></i>
                        @if (Model.CurrentUserRole == "Admin")
                        {
                            <text>Create Corp User</text>
                        }
                        else
                        {
                            <text>Create User</text>
                        }
                    </a>
                }
            </div>

            @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
            {
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="bi bi-check-circle me-2"></i>
                    @TempData["SuccessMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
            {
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    @TempData["ErrorMessage"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list me-2"></i>
                        Users
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Users.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Role</th>
                                        <th>Created</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var user in Model.Users)
                                    {
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3">
                                                        @user.FirstName.Substring(0, 1).ToUpper()@user.LastName.Substring(0, 1).ToUpper()
                                                    </div>
                                                    <div>
                                                        <div class="fw-semibold">@user.FirstName @user.LastName</div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>@user.Email</td>
                                            <td>
                                                <span class="badge bg-@(user.Role == "Admin" ? "danger" : user.Role == "Corp" ? "warning" : "info")">
                                                    @user.RoleDisplayName
                                                </span>
                                            </td>
                                            <td>@user.CreatedAt.ToString("MMM dd, yyyy")</td>
                                            <td>
                                                @if (user.IsActive)
                                                {
                                                    <span class="badge bg-success">Active</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-secondary">Inactive</span>
                                                }
                                            </td>
                                            <td>
                                                @if (user.CanManage)
                                                {
                                                    <div class="btn-group" role="group">
                                                        <a asp-page="./Edit" asp-route-id="@user.Id" class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-pencil"></i>
                                                        </a>
                                                        <form method="post" asp-page-handler="ToggleActive" asp-route-userId="@user.Id" class="d-inline">
                                                            <button type="submit" class="btn btn-sm btn-outline-@(user.IsActive ? "warning" : "success")" 
                                                                    onclick="return confirm('Are you sure you want to @(user.IsActive ? "deactivate" : "activate") this user?')">
                                                                <i class="bi bi-@(user.IsActive ? "pause" : "play")"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">No access</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-people display-1 text-muted"></i>
                            <h5 class="mt-3 text-muted">No users found</h5>
                            <p class="text-muted">
                                @if (Model.CreatableRoles.Any())
                                {
                                    @if (Model.CurrentUserRole == "Admin")
                                    {
                                        <text>Create your first corporate user to get started.</text>
                                    }
                                    else
                                    {
                                        <text>Create your first user to get started.</text>
                                    }
                                }
                                else
                                {
                                    <text>You don't have permission to create users.</text>
                                }
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .avatar-sm {
        width: 40px;
        height: 40px;
        font-size: 14px;
        font-weight: 600;
    }
</style>
