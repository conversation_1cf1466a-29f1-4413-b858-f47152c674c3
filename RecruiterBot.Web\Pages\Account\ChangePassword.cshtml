@page
@model RecruiterBot.Web.Pages.Account.ChangePasswordModel
@{
    ViewData["Title"] = Model.IsForced ? "Change Password Required" : "Change Password";
    Layout = Model.IsForced ? "_PlainLayout" : "_AuthenticatedLayout";
}

@section Styles {
    @if (Model.IsForced)
    {
        <link rel="stylesheet" href="~/css/auth.css" />
    }
}

@if (Model.IsForced)
{
    <div class="min-h-screen bg-gradient-to-br from-orange-50 via-white to-red-50 flex items-center justify-center p-4">
        <div class="w-full max-w-md">
            <!-- Success/Error Messages -->
            @if (TempData["SuccessMessage"] != null)
            {
                <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="bi bi-check-circle-fill text-green-500 mr-3"></i>
                        <p class="text-green-800 text-sm">@TempData["SuccessMessage"]</p>
                    </div>
                </div>
            }

            @if (TempData["ErrorMessage"] != null)
            {
                <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                    <div class="flex items-center">
                        <i class="bi bi-exclamation-triangle-fill text-red-500 mr-3"></i>
                        <p class="text-red-800 text-sm">@TempData["ErrorMessage"]</p>
                    </div>
                </div>
            }

            <!-- Password Change Card -->
            <div class="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
                <!-- Header -->
                <div class="bg-gradient-to-r from-orange-600 to-red-600 px-8 py-10 text-center">
                    <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="bi bi-shield-lock text-white text-2xl"></i>
                    </div>
                    <h1 class="text-2xl font-bold text-white mb-2">Password Change Required</h1>
                    <p class="text-orange-100">Set a new secure password to continue</p>
                </div>

                <!-- Form Body -->
                <div class="px-8 py-8">
                    <!-- Security Notice -->
                    <div class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="bi bi-exclamation-triangle text-amber-600 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-amber-800 font-medium mb-1">Security Notice</h3>
                                <p class="text-amber-700 text-sm">
                                    You must change your temporary password before you can continue using the system.
                                </p>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="changePasswordForm">
                        <input type="hidden" asp-for="ReturnUrl" />
                        <input type="hidden" name="forced" value="true" />

                        <!-- Validation Summary -->
                        <div asp-validation-summary="ModelOnly" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm" style="display: none;"></div>

                        <!-- Current Password -->
                        <div class="mb-6">
                            <label asp-for="Input.CurrentPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-key mr-2 text-gray-400"></i>Current Password
                            </label>
                            <div class="relative">
                                <input asp-for="Input.CurrentPassword"
                                       type="password"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                                       placeholder="Enter your temporary password"
                                       autocomplete="current-password"
                                       required />
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.CurrentPassword', 'currentToggleIcon')">
                                    <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="currentToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Input.CurrentPassword" class="text-red-500 text-xs mt-1 block"></span>
                            <p class="text-xs text-gray-500 mt-1">Enter your temporary password</p>
                        </div>

                        <!-- New Password -->
                        <div class="mb-6">
                            <label asp-for="Input.NewPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-shield-lock mr-2 text-gray-400"></i>New Password
                            </label>
                            <div class="relative">
                                <input asp-for="Input.NewPassword"
                                       type="password"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                                       placeholder="Enter your new password"
                                       autocomplete="new-password"
                                       required />
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.NewPassword', 'newToggleIcon')">
                                    <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="newToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Input.NewPassword" class="text-red-500 text-xs mt-1 block"></span>
                            <p class="text-xs text-gray-500 mt-1">Must be at least 8 characters long</p>
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-6">
                            <label asp-for="Input.ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-shield-check mr-2 text-gray-400"></i>Confirm New Password
                            </label>
                            <div class="relative">
                                <input asp-for="Input.ConfirmPassword"
                                       type="password"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors duration-200"
                                       placeholder="Confirm your new password"
                                       autocomplete="new-password"
                                       required />
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.ConfirmPassword', 'confirmToggleIcon')">
                                    <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="confirmToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Input.ConfirmPassword" class="text-red-500 text-xs mt-1 block"></span>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-orange-600 to-red-600 text-white py-3 px-4 rounded-lg font-medium hover:from-orange-700 hover:to-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                id="changePasswordBtn">
                            <span id="changePasswordButtonText">
                                <i class="bi bi-shield-lock mr-2"></i>Change Password
                            </span>
                            <span id="changePasswordButtonLoading" class="hidden">
                                <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>Changing...
                            </span>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
}
else
{
    <!-- Regular Password Change (Authenticated Layout) -->
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card shadow-lg border-0 rounded-3">
                    <div class="card-header bg-gradient-to-r from-indigo-600 to-purple-600 text-white border-0 rounded-top-3">
                        <h4 class="card-title mb-0 d-flex align-items-center">
                            <i class="bi bi-shield-lock me-2"></i>
                            Change Password
                        </h4>
                    </div>
                    <div class="card-body p-4">
                        @if (TempData["SuccessMessage"] != null)
                        {
                            <div class="alert alert-success d-flex align-items-center mb-4">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                @TempData["SuccessMessage"]
                            </div>
                        }

                        <form method="post" id="changePasswordFormRegular">
                            <input type="hidden" asp-for="ReturnUrl" />

                            <div asp-validation-summary="ModelOnly" class="alert alert-danger d-flex align-items-center mb-4" style="display: none;">
                                <i class="bi bi-exclamation-triangle-fill me-2"></i>
                                <div></div>
                            </div>

                            <!-- Current Password -->
                            <div class="mb-3">
                                <label asp-for="Input.CurrentPassword" class="form-label fw-medium">
                                    <i class="bi bi-key me-1 text-muted"></i>Current Password
                                </label>
                                <div class="input-group">
                                    <input asp-for="Input.CurrentPassword"
                                           type="password"
                                           class="form-control form-control-lg"
                                           placeholder="Enter your current password"
                                           autocomplete="current-password"
                                           required />
                                    <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordRegular('Input.CurrentPassword', 'currentToggleIconRegular')">
                                        <i class="bi bi-eye" id="currentToggleIconRegular"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.CurrentPassword" class="text-danger small"></span>
                            </div>

                            <!-- New Password -->
                            <div class="mb-3">
                                <label asp-for="Input.NewPassword" class="form-label fw-medium">
                                    <i class="bi bi-shield-lock me-1 text-muted"></i>New Password
                                </label>
                                <div class="input-group">
                                    <input asp-for="Input.NewPassword"
                                           type="password"
                                           class="form-control form-control-lg"
                                           placeholder="Enter your new password"
                                           autocomplete="new-password"
                                           required />
                                    <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordRegular('Input.NewPassword', 'newToggleIconRegular')">
                                        <i class="bi bi-eye" id="newToggleIconRegular"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.NewPassword" class="text-danger small"></span>
                                <div class="form-text">Must be at least 8 characters long</div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="mb-4">
                                <label asp-for="Input.ConfirmPassword" class="form-label fw-medium">
                                    <i class="bi bi-shield-check me-1 text-muted"></i>Confirm New Password
                                </label>
                                <div class="input-group">
                                    <input asp-for="Input.ConfirmPassword"
                                           type="password"
                                           class="form-control form-control-lg"
                                           placeholder="Confirm your new password"
                                           autocomplete="new-password"
                                           required />
                                    <button type="button" class="btn btn-outline-secondary" onclick="togglePasswordRegular('Input.ConfirmPassword', 'confirmToggleIconRegular')">
                                        <i class="bi bi-eye" id="confirmToggleIconRegular"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.ConfirmPassword" class="text-danger small"></span>
                            </div>

                            <!-- Action Buttons -->
                            <div class="d-flex justify-content-end gap-2">
                                <a href="@Model.ReturnUrl" class="btn btn-outline-secondary btn-lg">
                                    <i class="bi bi-x-circle me-2"></i>
                                    Cancel
                                </a>
                                <button type="submit" class="btn btn-primary btn-lg" id="changePasswordBtnRegular">
                                    <span id="changePasswordButtonTextRegular">
                                        <i class="bi bi-shield-lock me-2"></i>Change Password
                                    </span>
                                    <span id="changePasswordButtonLoadingRegular" class="d-none">
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>Changing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        // Password toggle functionality for forced change
        function togglePassword(inputName, iconId) {
            const passwordInput = document.querySelector(`input[name="${inputName}"]`);
            const toggleIcon = document.getElementById(iconId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash text-gray-400 hover:text-gray-600 transition-colors duration-200';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200';
            }
        }

        // Password toggle functionality for regular change
        function togglePasswordRegular(inputName, iconId) {
            const passwordInput = document.querySelector(`input[name="${inputName}"]`);
            const toggleIcon = document.getElementById(iconId);

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Handle forced password change form
            const forcedForm = document.getElementById('changePasswordForm');
            if (forcedForm) {
                const submitButton = document.getElementById('changePasswordBtn');
                const buttonText = document.getElementById('changePasswordButtonText');
                const buttonLoading = document.getElementById('changePasswordButtonLoading');
                let isSubmitting = false;

                // Show validation summary if there are errors
                const validationSummary = forcedForm.querySelector('[asp-validation-summary]');
                if (validationSummary && validationSummary.innerHTML.trim()) {
                    validationSummary.style.display = 'block';
                }

                forcedForm.addEventListener('submit', function(e) {
                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }

                    // Check if form is valid
                    if (!this.checkValidity()) {
                        return true;
                    }

                    const hasValidationErrors = Array.from(forcedForm.querySelectorAll('.text-red-500')).some(el => el.textContent.trim() !== '');
                    if (hasValidationErrors) {
                        return true;
                    }

                    isSubmitting = true;

                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('hidden');
                    buttonLoading.classList.remove('hidden');

                    setTimeout(function() {
                        if (submitButton.disabled) {
                            submitButton.disabled = false;
                            buttonText.classList.remove('hidden');
                            buttonLoading.classList.add('hidden');
                            isSubmitting = false;
                        }
                    }, 30000);
                });
            }

            // Handle regular password change form
            const regularForm = document.getElementById('changePasswordFormRegular');
            if (regularForm) {
                const submitButton = document.getElementById('changePasswordBtnRegular');
                const buttonText = document.getElementById('changePasswordButtonTextRegular');
                const buttonLoading = document.getElementById('changePasswordButtonLoadingRegular');
                let isSubmitting = false;

                // Show validation summary if there are errors
                const validationSummary = regularForm.querySelector('[asp-validation-summary]');
                if (validationSummary && validationSummary.innerHTML.trim()) {
                    validationSummary.style.display = 'block';
                }

                regularForm.addEventListener('submit', function(e) {
                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }

                    // Check if form is valid
                    if (!this.checkValidity()) {
                        return true;
                    }

                    const hasValidationErrors = Array.from(regularForm.querySelectorAll('.text-danger')).some(el => el.textContent.trim() !== '');
                    if (hasValidationErrors) {
                        return true;
                    }

                    isSubmitting = true;

                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('d-none');
                    buttonLoading.classList.remove('d-none');

                    setTimeout(function() {
                        if (submitButton.disabled) {
                            submitButton.disabled = false;
                            buttonText.classList.remove('d-none');
                            buttonLoading.classList.add('d-none');
                            isSubmitting = false;
                        }
                    }, 30000);
                });
            }

            // Auto-focus first password field
            const firstPasswordInput = document.querySelector('input[name="Input.CurrentPassword"]');
            if (firstPasswordInput) {
                firstPasswordInput.focus();
            }
        });
    </script>
}
