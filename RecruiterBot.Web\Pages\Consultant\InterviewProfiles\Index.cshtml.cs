using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Consultant.InterviewProfiles
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class IndexModel : PageModel
    {
        private readonly IInterviewProfileService _interviewProfileService;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(IInterviewProfileService interviewProfileService, ILogger<IndexModel> logger)
        {
            _interviewProfileService = interviewProfileService;
            _logger = logger;
        }

        public IEnumerable<InterviewProfile> Profiles { get; set; } = new List<InterviewProfile>();
        
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }
        
        [TempData]
        public string? ErrorMessage { get; set; }

        // Statistics properties
        public int TotalProfiles { get; set; }
        public int TotalExperiences { get; set; }
        public int TotalTechnologies { get; set; }
        public int TotalAchievements { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                // Get profiles based on search term
                if (!string.IsNullOrWhiteSpace(SearchTerm))
                {
                    Profiles = await _interviewProfileService.SearchProfilesAsync(SearchTerm, consultantId);
                }
                else
                {
                    Profiles = await _interviewProfileService.GetProfilesByConsultantAsync(consultantId);
                }

                // Calculate statistics
                CalculateStatistics();

                _logger.LogInformation("Consultant {ConsultantId} accessed interview profiles page with {Count} profiles", 
                    consultantId, Profiles.Count());

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading interview profiles page");
                ErrorMessage = "An error occurred while loading your profiles. Please try again.";
                Profiles = new List<InterviewProfile>();
                return Page();
            }
        }

        private void CalculateStatistics()
        {
            var profilesList = Profiles.ToList();
            
            TotalProfiles = profilesList.Count;
            TotalExperiences = profilesList.Sum(p => p.ExperienceCount);
            TotalAchievements = profilesList.Sum(p => p.AchievementCount);
            
            // Calculate unique technologies across all profiles
            var allTechnologies = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
            foreach (var profile in profilesList)
            {
                if (!string.IsNullOrWhiteSpace(profile.PrimaryTechnology))
                {
                    allTechnologies.Add(profile.PrimaryTechnology);
                }

                foreach (var experience in profile.Experiences)
                {
                    foreach (var tech in experience.TechnologiesUsed)
                    {
                        allTechnologies.Add(tech);
                    }
                }
            }

            TotalTechnologies = allTechnologies.Count;
        }
    }
}
