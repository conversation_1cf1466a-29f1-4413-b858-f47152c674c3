using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace RecruiterBot.Infrastructure.Services
{
    public class LocalFileStorageService : IFileStorageService
    {
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<LocalFileStorageService> _logger;
        private readonly string _uploadsPath;

        public LocalFileStorageService(IWebHostEnvironment environment, ILogger<LocalFileStorageService> logger)
        {
            _environment = environment;
            _logger = logger;
            _uploadsPath = Path.Combine(_environment.WebRootPath, "uploads");
            
            // Ensure uploads directory exists
            if (!Directory.Exists(_uploadsPath))
            {
                Directory.CreateDirectory(_uploadsPath);
                _logger.LogInformation("Created uploads directory: {UploadsPath}", _uploadsPath);
            }
        }

        public async Task<string> UploadFileAsync(IFormFile file, string folder)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is null or empty");

            var folderPath = Path.Combine(_uploadsPath, folder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            var fileName = GenerateUniqueFileName(file.FileName);
            var filePath = Path.Combine(folderPath, fileName);

            try
            {
                using var fileStream = new FileStream(filePath, FileMode.Create);
                await file.CopyToAsync(fileStream);
                
                var relativePath = $"/uploads/{folder}/{fileName}";
                _logger.LogInformation("File uploaded locally: {FileName} -> {RelativePath}", file.FileName, relativePath);
                
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {FileName} locally", file.FileName);
                throw;
            }
        }

        public async Task<string> UploadFileAsync(string localFilePath, string fileName, string folder)
        {
            if (!File.Exists(localFilePath))
                throw new FileNotFoundException($"Local file not found: {localFilePath}");

            var folderPath = Path.Combine(_uploadsPath, folder);
            if (!Directory.Exists(folderPath))
            {
                Directory.CreateDirectory(folderPath);
            }

            var uniqueFileName = GenerateUniqueFileName(fileName);
            var destinationPath = Path.Combine(folderPath, uniqueFileName);

            try
            {
                await using var sourceStream = File.OpenRead(localFilePath);
                await using var destinationStream = File.Create(destinationPath);
                await sourceStream.CopyToAsync(destinationStream);
                
                var relativePath = $"/uploads/{folder}/{uniqueFileName}";
                _logger.LogInformation("File copied locally: {LocalPath} -> {RelativePath}", localFilePath, relativePath);
                
                return relativePath;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to copy file {LocalPath} locally", localFilePath);
                throw;
            }
        }

        public Task<bool> DeleteFileAsync(string fileUrl)
        {
            try
            {
                var filePath = GetLocalFilePath(fileUrl);
                if (string.IsNullOrEmpty(filePath) || !File.Exists(filePath))
                {
                    _logger.LogWarning("File not found for deletion: {FileUrl}", fileUrl);
                    return Task.FromResult(true); // Consider it successful if file doesn't exist
                }

                File.Delete(filePath);
                _logger.LogInformation("File deleted locally: {FilePath}", filePath);
                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete file: {FileUrl}", fileUrl);
                return Task.FromResult(false);
            }
        }

        public Task<string> GetSignedUrlAsync(string fileUrl, int expirationMinutes = 60)
        {
            // For local storage, we just return the original URL since files are publicly accessible
            _logger.LogDebug("Returning original URL for local storage: {FileUrl}", fileUrl);
            return Task.FromResult(fileUrl);
        }

        public Task<bool> FileExistsAsync(string fileUrl)
        {
            try
            {
                var filePath = GetLocalFilePath(fileUrl);
                var exists = !string.IsNullOrEmpty(filePath) && File.Exists(filePath);
                return Task.FromResult(exists);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists: {FileUrl}", fileUrl);
                return Task.FromResult(false);
            }
        }

        private string GenerateUniqueFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var sanitizedName = Regex.Replace(nameWithoutExtension, @"[^a-zA-Z0-9\-_]", "_");
            return $"{Guid.NewGuid()}_{sanitizedName}{extension}";
        }

        private string GetLocalFilePath(string fileUrl)
        {
            if (string.IsNullOrEmpty(fileUrl))
                return string.Empty;

            // Handle relative URLs like "/uploads/resumes/file.pdf"
            if (fileUrl.StartsWith("/uploads/"))
            {
                var relativePath = fileUrl.TrimStart('/').Replace('/', Path.DirectorySeparatorChar);
                return Path.Combine(_environment.WebRootPath, relativePath);
            }

            // Handle absolute local paths
            if (Path.IsPathRooted(fileUrl) && fileUrl.Contains(_uploadsPath))
            {
                return fileUrl;
            }

            return string.Empty;
        }
    }
}
