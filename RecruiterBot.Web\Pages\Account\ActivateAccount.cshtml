@page
@model RecruiterBot.Web.Pages.Account.ActivateAccountModel
@{
    ViewData["Title"] = "Activate Account | RecruiterBot";
    Layout = "_PlainLayout";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth.css" />
}

<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
    <div class="w-full max-w-lg">
        <!-- Success/Error Messages -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-check-circle-fill text-green-500 mr-3"></i>
                    <p class="text-green-800 text-sm">@TempData["SuccessMessage"]</p>
                </div>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle-fill text-red-500 mr-3"></i>
                    <p class="text-red-800 text-sm">@TempData["ErrorMessage"]</p>
                </div>
            </div>
        }

        <!-- Activation Card -->
        <div class="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 to-emerald-600 px-8 py-10 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="bi bi-shield-check text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Account Activation</h1>
                <p class="text-green-100">Complete your RecruiterBot setup</p>
            </div>


            <!-- Form Body -->
            <div class="px-8 py-8">
                @if (Model.IsValidToken)
                {
                    <!-- Welcome Message -->
                    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="bi bi-info-circle text-blue-500 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-blue-800 font-medium mb-1">Welcome, @Model.User.FirstName!</h3>
                                <p class="text-blue-700 text-sm">
                                    Click the button below to activate your RecruiterBot account. After activation, you'll be able to log in with your temporary credentials and will be prompted to set a new password.
                                </p>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="activationForm">
                        <input type="hidden" name="userId" value="@Model.UserId" />
                        <input type="hidden" name="token" value="@Model.Token" />

                        <!-- Account Details -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                                <i class="bi bi-person-circle text-indigo-600 mr-2"></i>
                                Account Details
                            </h3>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Name:</span>
                                    <span class="text-sm text-gray-900">@Model.User.FirstName @Model.User.LastName</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Email:</span>
                                    <span class="text-sm text-gray-900">@Model.User.Email</span>
                                </div>
                            </div>
                        </div>

                        <!-- Security Notice -->
                        <div class="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                            <div class="flex items-start">
                                <i class="bi bi-shield-exclamation text-amber-600 mr-3 mt-0.5"></i>
                                <div>
                                    <h4 class="text-amber-800 font-medium mb-1">Security Notice</h4>
                                    <p class="text-amber-700 text-sm">
                                        You will need to change your temporary password immediately after your first login for security purposes.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Activate Button -->
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-green-600 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                                id="activateBtn">
                            <span id="activateButtonText">
                                <i class="bi bi-shield-check mr-2"></i>Activate My Account
                            </span>
                            <span id="activateButtonLoading" class="hidden">
                                <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>Activating...
                            </span>
                        </button>
                    </form>

                    <!-- Help Text -->
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            Having trouble? Contact your administrator for assistance.
                        </p>
                    </div>
                }
                else
                {
                    <!-- Error State -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="bi bi-exclamation-triangle text-red-600 text-2xl"></i>
                        </div>

                        <h3 class="text-lg font-medium text-gray-900 mb-2">Invalid Activation Link</h3>
                        <p class="text-gray-600 mb-6">
                            This activation link is invalid or has expired. Please contact your administrator for a new activation link.
                        </p>

                        <a href="/Account/Login"
                           class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">
                            <i class="bi bi-arrow-left mr-2"></i>
                            Back to Login
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('activationForm');
            const submitButton = document.getElementById('activateBtn');
            const buttonText = document.getElementById('activateButtonText');
            const buttonLoading = document.getElementById('activateButtonLoading');
            let isSubmitting = false;

            if (form) {
                form.addEventListener('submit', function(e) {
                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }

                    isSubmitting = true;

                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('hidden');
                    buttonLoading.classList.remove('hidden');

                    // Set a timeout to reset the button if something goes wrong
                    setTimeout(function() {
                        if (submitButton.disabled) {
                            submitButton.disabled = false;
                            buttonText.classList.remove('hidden');
                            buttonLoading.classList.add('hidden');
                            isSubmitting = false;
                        }
                    }, 30000);
                });
            }
        });
    </script>
}
