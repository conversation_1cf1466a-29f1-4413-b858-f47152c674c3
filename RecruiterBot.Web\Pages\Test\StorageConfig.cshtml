@page
@model RecruiterBot.Web.Pages.Test.StorageConfigModel
@{
    ViewData["Title"] = "Storage Configuration";
}

<div class="container mt-4">
    <h2>Storage Configuration</h2>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5>Current Storage Settings</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Active Storage Provider</h6>
                            <p class="badge @(Model.IsGcpEnabled ? "bg-primary" : "bg-secondary") fs-6">
                                @Model.CurrentProvider
                            </p>
                        </div>
                        <div class="col-md-6">
                            <h6>GCP Storage Status</h6>
                            <p class="badge @(Model.IsGcpEnabled ? "bg-success" : "bg-warning") fs-6">
                                @(Model.IsGcpEnabled ? "Enabled" : "Disabled")
                            </p>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6>Configuration Details</h6>
                    <ul class="list-unstyled">
                        <li><strong>Bucket Name:</strong> @Model.BucketName</li>
                        <li><strong>Environment:</strong> @Model.Environment</li>
                        <li><strong>GCP Project ID:</strong> @Model.ProjectId</li>
                    </ul>
                    
                    <div class="mt-3">
                        <h6>Instructions to Enable GCP Storage</h6>
                        <ol>
                            <li>Set <code>"GCP:Storage:Enabled": true</code> in appsettings.json</li>
                            <li>Ensure you have GCP credentials configured:
                                <ul>
                                    <li>Set <code>GOOGLE_APPLICATION_CREDENTIALS</code> environment variable, or</li>
                                    <li>Run <code>gcloud auth application-default login</code>, or</li>
                                    <li>Deploy to GCP with service account</li>
                                </ul>
                            </li>
                            <li>Restart the application</li>
                        </ol>
                    </div>
                    
                    <div class="mt-3">
                        <a href="/Test/FileStorageTest" class="btn btn-primary">Test File Storage</a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="/Candidates/Create" class="btn btn-outline-primary">
                            <i class="bi bi-person-plus"></i> Create Candidate
                        </a>
                        <a href="/Candidates" class="btn btn-outline-secondary">
                            <i class="bi bi-people"></i> View Candidates
                        </a>
                        <a href="/Test/FileStorageTest" class="btn btn-outline-info">
                            <i class="bi bi-cloud-upload"></i> Test Storage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
