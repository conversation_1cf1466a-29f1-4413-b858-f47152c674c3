using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class JobRunsModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ILogger<JobRunsModel> _logger;

        public Dictionary<BotConfiguration, List<JobRun>> JobRunsByBot { get; set; } = new Dictionary<BotConfiguration, List<JobRun>>();
        public int TotalJobRuns { get; set; }
        public int TotalBots { get; set; }

        public JobRunsModel(IBotConfigurationService botConfigService, ILogger<JobRunsModel> logger)
        {
            _botConfigService = botConfigService;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                // Get all job runs grouped by bot
                JobRunsByBot = await _botConfigService.GetAllJobRunsGroupedByBotAsync(userId);

                // Calculate statistics
                TotalBots = JobRunsByBot.Count;
                TotalJobRuns = JobRunsByBot.Values.Sum(runs => runs.Count);

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading all job runs for user {UserId}", userId);
                ModelState.AddModelError("", "An error occurred while loading job runs.");
                return Page();
            }
        }
    }
}
