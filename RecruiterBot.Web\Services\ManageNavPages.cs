using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.AspNetCore.Mvc.ViewFeatures;
using System;
using System.IO;

namespace RecruiterBot.Web.Services
{
    public static class ManageNavPages
    {
        public static string Index => "./Index";
        public static string ChangePassword => "./ChangePassword";
        public static string ExternalLogins => "./ExternalLogins";
        public static string PersonalData => "./PersonalData";
        public static string TwoFactorAuthentication => "./TwoFactorAuthentication";
        public static string DownloadPersonalData => "./DownloadPersonalData";
        public static string DeletePersonalData => "./DeletePersonalData";
        public static string Disable2fa => "./Disable2fa";
        public static string EnableAuthenticator => "./EnableAuthenticator";
        public static string GenerateRecoveryCodes => "./GenerateRecoveryCodes";
        public static string ResetAuthenticator => "./ResetAuthenticator";
        public static string ShowRecoveryCodes => "./ShowRecoveryCodes";

        public static string IndexNavClass(ViewContext viewContext) => PageNavClass(viewContext, Index);
        public static string ChangePasswordNavClass(ViewContext viewContext) => PageNavClass(viewContext, ChangePassword);
        public static string ExternalLoginsNavClass(ViewContext viewContext) => PageNavClass(viewContext, ExternalLogins);
        public static string PersonalDataNavClass(ViewContext viewContext) => PageNavClass(viewContext, PersonalData);
        public static string TwoFactorAuthenticationNavClass(ViewContext viewContext) => PageNavClass(viewContext, TwoFactorAuthentication);

        private static string PageNavClass(ViewContext viewContext, string page)
        {
            var activePage = viewContext.ViewData["ActivePage"] as string
                ?? Path.GetFileNameWithoutExtension(viewContext.ActionDescriptor.DisplayName);
            
            // Normalize the page path for comparison
            var normalizedPage = page.StartsWith("./") ? page.Substring(2) : page;
            return string.Equals(activePage, normalizedPage, StringComparison.OrdinalIgnoreCase) ? "active" : null;
        }
    }
}
