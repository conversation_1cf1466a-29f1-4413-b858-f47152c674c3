using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Interviews
{
    [Authorize(Policy = AuthorizationPolicies.AllRoles)]
    public class CreateModel : PageModel
    {
        private readonly IInterviewService _interviewService;
        private readonly ICandidateService _candidateService;
        private readonly ILLMModelService _llmModelService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(
            IInterviewService interviewService,
            ICandidateService candidateService,
            ILLMModelService llmModelService,
            IRoleManagementService roleManagementService,
            ILogger<CreateModel> logger)
        {
            _interviewService = interviewService;
            _candidateService = candidateService;
            _llmModelService = llmModelService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public SelectList CandidateOptions { get; set; } = new SelectList(Enumerable.Empty<SelectListItem>());
        public SelectList ConsultantOptions { get; set; } = new SelectList(Enumerable.Empty<SelectListItem>());
        public SelectList LLMModelOptions { get; set; } = new SelectList(Enumerable.Empty<SelectListItem>());

        public class InputModel
        {
            [Required(ErrorMessage = "Job description is required")]
            [Display(Name = "Job Description")]
            [StringLength(5000, ErrorMessage = "Job description cannot exceed 5000 characters")]
            public string JobDescription { get; set; }

            [Required(ErrorMessage = "Interview date and time is required")]
            [Display(Name = "Interview Date & Time")]
            public DateTime InterviewDateTime { get; set; } = DateTime.Now.AddDays(1);

            [Required(ErrorMessage = "Please select a candidate")]
            [Display(Name = "Candidate")]
            public string CandidateId { get; set; }

            [Required(ErrorMessage = "Please select a consultant")]
            [Display(Name = "Consultant")]
            public string ConsultantId { get; set; }

            [Required(ErrorMessage = "Please select an LLM model")]
            [Display(Name = "LLM Model")]
            public string LLMModelId { get; set; }

            [Display(Name = "Notes")]
            [StringLength(1000, ErrorMessage = "Notes cannot exceed 1000 characters")]
            public string Notes { get; set; }
        }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Check if user is authorized to create interviews (Standard Users only)
                var userRoles = await _roleManagementService.GetUserRolesAsync(userId);
                if (!userRoles.Contains(UserRoles.User))
                {
                    return Forbid();
                }

                await LoadDropdownDataAsync(userId);
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create interview page for user {UserId}", User.Identity?.Name);
                TempData["ErrorMessage"] = "An error occurred while loading the page. Please try again.";
                return RedirectToPage("/Dashboard");
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Check if user is authorized to create interviews
                var userRoles = await _roleManagementService.GetUserRolesAsync(userId);
                if (!userRoles.Contains(UserRoles.User))
                {
                    return Forbid();
                }

                if (!ModelState.IsValid)
                {
                    await LoadDropdownDataAsync(userId);
                    return Page();
                }

                // Validate interview date is in the future
                if (Input.InterviewDateTime <= DateTime.Now)
                {
                    ModelState.AddModelError(nameof(Input.InterviewDateTime), "Interview date must be in the future");
                    await LoadDropdownDataAsync(userId);
                    return Page();
                }

                // Create interview object
                var interview = new Interview
                {
                    JobDescription = Input.JobDescription,
                    InterviewDateTimeUtc = Input.InterviewDateTime.ToUniversalTime(),
                    CandidateId = Input.CandidateId,
                    ConsultantId = Input.ConsultantId,
                    LLMModelId = Input.LLMModelId,
                    Notes = Input.Notes
                };

                // Create the interview
                var createdInterview = await _interviewService.CreateInterviewAsync(interview, userId);

                _logger.LogInformation("User {UserId} created interview {InterviewId}", userId, createdInterview.Id);

                TempData["SuccessMessage"] = "Interview scheduled successfully!";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating interview for user {UserId}", User.Identity?.Name);
                ModelState.AddModelError(string.Empty, "An error occurred while creating the interview. Please try again.");
                
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    await LoadDropdownDataAsync(userId);
                }
                
                return Page();
            }
        }

        private async Task LoadDropdownDataAsync(string userId)
        {
            try
            {
                // Load candidates for the user's tenant
                var candidates = await _candidateService.GetCandidatesForUserAsync(userId);
                CandidateOptions = new SelectList(
                    candidates.Where(c => c.IsActive).Select(c => new SelectListItem
                    {
                        Value = c.Id,
                        Text = $"{c.FullName} ({c.Email})"
                    }),
                    "Value",
                    "Text"
                );

                // Load consultants for the user's tenant
                var consultants = await _roleManagementService.GetUsersInTenantByRoleAsync(
                    await _roleManagementService.GetCorpAdminIdAsync(userId), 
                    UserRoles.Consultant);
                
                ConsultantOptions = new SelectList(
                    consultants.Where(c => c.IsActive).Select(c => new SelectListItem
                    {
                        Value = c.Id,
                        Text = $"{c.FirstName} {c.LastName} ({c.Email})"
                    }),
                    "Value",
                    "Text"
                );

                // Load active LLM models
                var llmModels = await _llmModelService.GetActiveModelsAsync();
                LLMModelOptions = new SelectList(
                    llmModels.Select(m => new SelectListItem
                    {
                        Value = m.Id,
                        Text = $"{m.DisplayName} ({m.ProviderDisplayName})"
                    }),
                    "Value",
                    "Text"
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dropdown data for user {UserId}", userId);
                // Initialize empty lists to prevent null reference errors
                CandidateOptions = new SelectList(Enumerable.Empty<SelectListItem>());
                ConsultantOptions = new SelectList(Enumerable.Empty<SelectListItem>());
                LLMModelOptions = new SelectList(Enumerable.Empty<SelectListItem>());
            }
        }
    }
}
