@page
@model ForgotPasswordModel
@{
    ViewData["Title"] = "Forgot Password";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-key" style="font-size: 3rem; color: #007bff;"></i>
                        <h2 class="mt-3">Forgot Password?</h2>
                        <p class="text-muted">No worries! Enter your email address and we'll send you a link to reset your password.</p>
                    </div>

                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="form-group mb-3">
                            <label asp-for="Input.Email" class="form-label">Email Address</label>
                            <input asp-for="Input.Email" class="form-control" autocomplete="email" placeholder="Enter your email address" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="resetBtn">
                                <i class="bi bi-envelope me-2"></i>
                                Send Reset Link
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a asp-page="./Login" class="text-decoration-none">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Login
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Remember your password?
                            <a asp-page="./Login" class="text-decoration-none">Sign in here</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#resetBtn').html();

            // Show loading state when form is submitted
            $('form').on('submit', function() {
                var $btn = $('#resetBtn');
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Sending...');
            });

            // Reset button state if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $btn = $('#resetBtn');
                $btn.prop('disabled', false);
                $btn.html(originalButtonHtml);
            }
        });
    </script>
}
