using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace RecruiterBot.Web.Models
{
    public class JobRun
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("date_time")]
        public DateTime RunDate { get; set; }

        [BsonRepresentation(BsonType.ObjectId)]
        [BsonElement("bot_id")]
        public string BotId { get; set; }

        [BsonIgnore]
        public BotConfiguration Bot { get; set; }

        [BsonElement("posts")]
        public List<JobPost> Posts { get; set; } = new List<JobPost>();

        [BsonElement("is_processed")]
        public bool IsProcessed { get; set; }
    }

    public class JobPost
    {
        [BsonElement("post_id")]
        public string PostId { get; set; }

        [BsonElement("email")]
        public string Email { get; set; }

        [BsonElement("author")]
        public string Author { get; set; }

        [BsonElement("author_profile")]
        public string AuthorProfile { get; set; }

        [BsonElement("text")]
        public string Text { get; set; }

        [BsonElement("url")]
        public string Url { get; set; }

        [BsonElement("job_information")]
        public JobInformation JobInformation { get; set; }

    }

    public class JobInformation
    {
        [BsonElement("mandatory_skills")]
        public List<string> MandatorySkills { get; set; } = new List<string>();

        [BsonElement("keywords")]
        public List<string> Keywords { get; set; } = new List<string>();

        [BsonElement("job_title")]
        public string JobTitle { get; set; }

        [BsonElement("job_location")]
        public string JobLocation { get; set; }

        [BsonElement("is_remote")]
        public bool? IsRemote { get; set; }

        [BsonElement("is_looking_for_local_candidates_only")]
        public bool? IsLookingForLocalCandidatesOnly { get; set; }

        [BsonElement("does_accept_w2_candidates")]
        public bool? DoesAcceptW2Candidates { get; set; }

        [BsonElement("does_accept_c2c_candidates")]
        public bool? DoesAcceptC2CCandidates { get; set; }

        [BsonElement("candidate_required_info")]
        public List<string> CandidateRequiredInfo { get; set; } = new List<string>();

        [BsonElement("visa_accepted")]
        public List<string> VisaAccepted { get; set; } = new List<string>();

        [BsonElement("visa_not_accepted")]
        public List<string> VisaNotAccepted { get; set; } = new List<string>();

        [BsonElement("is_job_offer")]
        public bool? IsJobOffer { get; set; }
    }
}
