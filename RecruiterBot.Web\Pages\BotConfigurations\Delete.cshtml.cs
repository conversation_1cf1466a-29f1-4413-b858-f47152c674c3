using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class DeleteModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ILogger<DeleteModel> _logger;

        public DeleteModel(IBotConfigurationService botConfigService, ILogger<DeleteModel> logger)
        {
            _botConfigService = botConfigService;
            _logger = logger;
        }

        [BindProperty]
        public BotConfiguration Configuration { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            Configuration = await _botConfigService.GetConfigurationAsync(id, User.Identity.Name);
            if (Configuration == null)
            {
                return NotFound();
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            try
            {
                var result = await _botConfigService.DeleteConfigurationAsync(id, User.Identity.Name);
                if (!result)
                {
                    return NotFound();
                }

                TempData["SuccessMessage"] = "Bot configuration deleted successfully.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting bot configuration");
                ModelState.AddModelError("", "An error occurred while deleting the bot configuration.");
                return Page();
            }
        }
    }
}
