using Microsoft.Extensions.AI;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Models;
using System.Text.Json;

namespace RecruiterBot.Infrastructure.Services
{
    public class OpenAIResumeParsingService : IResumeParsingService
    {
        private readonly IChatClient _chatClient;
        private readonly ILogger<OpenAIResumeParsingService> _logger;

        public string ServiceName => "OpenAI";

        public OpenAIResumeParsingService(
            IChatClient chatClient,
            ILogger<OpenAIResumeParsingService> logger)
        {
            _chatClient = chatClient;
            _logger = logger;
        }

        public async Task<bool> IsServiceAvailableAsync()
        {
            try
            {
                // Test with a simple prompt to check if the service is available
                var testMessages = new List<ChatMessage>
                {
                    new(ChatRole.User, "Hello")
                };

                var response = await _chatClient.CompleteAsync(testMessages);
                return !string.IsNullOrEmpty(response.Message.Text);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "OpenAI service is not available");
                return false;
            }
        }

        public async Task<Candidate> ParseResumeAsync(string resumeText, string originalFilename)
        {
            try
            {
                _logger.LogInformation("Starting resume parsing with OpenAI for file: {Filename}", originalFilename);

                var prompt = CreateParsingPrompt(resumeText);
                var messages = new List<ChatMessage>
                {
                    new(ChatRole.System, "You are an expert resume parser. Extract information from resumes and return it as valid JSON."),
                    new(ChatRole.User, prompt)
                };

                var response = await _chatClient.CompleteAsync(messages);
                var jsonResponse = response.Message.Text;

                if (string.IsNullOrEmpty(jsonResponse))
                {
                    throw new Exception("Empty response from OpenAI");
                }

                var candidate = ParseOpenAIResponse(jsonResponse);
                candidate.OriginalResumeFilename = originalFilename;
                candidate.ResumeText = resumeText;
                candidate.ParsingStatus = ParsingStatus.Completed;

                _logger.LogInformation("Successfully parsed resume with OpenAI for: {Name}", candidate.FullName);
                return candidate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing resume with OpenAI: {Filename}", originalFilename);
                
                return new Candidate
                {
                    OriginalResumeFilename = originalFilename,
                    ResumeText = resumeText,
                    ParsingStatus = ParsingStatus.Failed,
                    ParsingError = ex.Message
                };
            }
        }

        private string CreateParsingPrompt(string resumeText)
        {
            return $@"
Please extract the following information from this resume and return it as a JSON object with the exact structure shown below. 
If any information is not available, use null or empty arrays as appropriate.

Resume text:
{resumeText}

Please return a JSON object with this exact structure:
{{
    ""firstName"": ""string"",
    ""lastName"": ""string"",
    ""email"": ""string"",
    ""phone"": ""string"",
    ""address"": ""string"",
    ""city"": ""string"",
    ""state"": ""string"",
    ""zipCode"": ""string"",
    ""country"": ""string"",
    ""linkedInUrl"": ""string"",
    ""portfolioUrl"": ""string"",
    ""currentTitle"": ""string"",
    ""currentCompany"": ""string"",
    ""yearsOfExperience"": number,
    ""skills"": [""skill1"", ""skill2""],
    ""summary"": ""string"",
    ""workExperience"": [
        {{
            ""company"": ""string"",
            ""title"": ""string"",
            ""startDate"": ""string"",
            ""endDate"": ""string"",
            ""description"": ""string"",
            ""location"": ""string""
        }}
    ],
    ""education"": [
        {{
            ""institution"": ""string"",
            ""degree"": ""string"",
            ""fieldOfStudy"": ""string"",
            ""graduationYear"": ""string"",
            ""gpa"": ""string""
        }}
    ],
    ""certifications"": [""cert1"", ""cert2""],
    ""aiConfidenceScore"": 0.95
}}

Return only the JSON object, no additional text or explanation.";
        }

        private Candidate ParseOpenAIResponse(string jsonResponse)
        {
            try
            {
                // Clean the response to extract just the JSON
                var cleanJson = ExtractJsonFromResponse(jsonResponse);
                
                var options = new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                };

                var parsedData = JsonSerializer.Deserialize<CandidateParseResult>(cleanJson, options);
                
                return new Candidate
                {
                    FirstName = parsedData.FirstName,
                    LastName = parsedData.LastName,
                    Email = parsedData.Email,
                    Phone = parsedData.Phone,
                    Address = parsedData.Address,
                    City = parsedData.City,
                    State = parsedData.State,
                    ZipCode = parsedData.ZipCode,
                    Country = parsedData.Country,
                    LinkedInUrl = parsedData.LinkedInUrl,
                    PortfolioUrl = parsedData.PortfolioUrl,
                    CurrentTitle = parsedData.CurrentTitle,
                    CurrentCompany = parsedData.CurrentCompany,
                    YearsOfExperience = parsedData.YearsOfExperience,
                    Skills = parsedData.Skills ?? new List<string>(),
                    Summary = parsedData.Summary,
                    WorkExperience = ConvertWorkExperience(parsedData.WorkExperience),
                    Education = ConvertEducation(parsedData.Education),
                    Certifications = parsedData.Certifications ?? new List<string>(),
                    AiConfidenceScore = parsedData.AiConfidenceScore
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing OpenAI JSON response: {Response}", jsonResponse);
                throw new Exception($"Failed to parse AI response: {ex.Message}");
            }
        }

        private string ExtractJsonFromResponse(string response)
        {
            // Find the first { and last } to extract JSON
            var startIndex = response.IndexOf('{');
            var lastIndex = response.LastIndexOf('}');
            
            if (startIndex >= 0 && lastIndex > startIndex)
            {
                return response.Substring(startIndex, lastIndex - startIndex + 1);
            }
            
            return response;
        }

        private List<WorkExperience> ConvertWorkExperience(List<WorkExperienceParseResult> workExperience)
        {
            if (workExperience == null) return new List<WorkExperience>();
            
            var result = new List<WorkExperience>();
            foreach (var work in workExperience)
            {
                result.Add(new WorkExperience
                {
                    Company = work.Company,
                    Title = work.Title,
                    StartDate = work.StartDate,
                    EndDate = work.EndDate,
                    Description = work.Description,
                    Location = work.Location
                });
            }
            return result;
        }

        private List<Education> ConvertEducation(List<EducationParseResult> education)
        {
            if (education == null) return new List<Education>();
            
            var result = new List<Education>();
            foreach (var edu in education)
            {
                result.Add(new Education
                {
                    Institution = edu.Institution,
                    Degree = edu.Degree,
                    FieldOfStudy = edu.FieldOfStudy,
                    GraduationYear = edu.GraduationYear,
                    GPA = edu.GPA
                });
            }
            return result;
        }
    }
}
