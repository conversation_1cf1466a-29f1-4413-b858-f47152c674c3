using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using System;

namespace RecruiterBot.Infrastructure.Data
{
    public class MongoDbContext
    {
        private readonly IMongoDatabase _database;
        private readonly ILogger<MongoDbContext> _logger;

        public MongoDbContext(IOptions<MongoDbSettings> settings, ILogger<MongoDbContext> logger)
        {
            try
            {
                _logger = logger;
                var mongoSettings = settings?.Value ?? throw new ArgumentNullException(nameof(settings));
                
                if (string.IsNullOrEmpty(mongoSettings.ConnectionString))
                    throw new ArgumentException("MongoDB connection string is not configured");
                
                if (string.IsNullOrEmpty(mongoSettings.DatabaseName))
                    throw new ArgumentException("MongoDB database name is not configured");
                
                var client = new MongoClient(mongoSettings.ConnectionString);
                _database = client.GetDatabase(mongoSettings.DatabaseName);
                
                // Ensure indexes are created
                EnsureIndexes();
                
                _logger.LogInformation("MongoDB connection established successfully to database: {DatabaseName}", mongoSettings.DatabaseName);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error connecting to MongoDB");
                throw;
            }
        }

        public IMongoCollection<User> Users => _database.GetCollection<User>("Users");
        public IMongoCollection<IdentityRole> Roles => _database.GetCollection<IdentityRole>("Roles");
        public IMongoCollection<Interview> Interviews => _database.GetCollection<Interview>("Interviews");
        
        // Create indexes for better query performance
        public void EnsureIndexes()
        {
            // Create indexes for Users collection
            var userIndexes = new List<CreateIndexModel<User>>
            {
                new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(u => u.UserName), new CreateIndexOptions { Unique = true }),
                new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(u => u.NormalizedUserName), new CreateIndexOptions { Unique = true }),
                new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(u => u.Email)),
                new CreateIndexModel<User>(Builders<User>.IndexKeys.Ascending(u => u.NormalizedEmail))
            };
            Users.Indexes.CreateMany(userIndexes);
            
            // Create indexes for Roles collection
            var roleIndexes = new List<CreateIndexModel<IdentityRole>>
            {
                new CreateIndexModel<IdentityRole>(Builders<IdentityRole>.IndexKeys.Ascending(r => r.Name), new CreateIndexOptions { Unique = true }),
                new CreateIndexModel<IdentityRole>(Builders<IdentityRole>.IndexKeys.Ascending(r => r.NormalizedName), new CreateIndexOptions { Unique = true })
            };
            Roles.Indexes.CreateMany(roleIndexes);
        }
    }

}
