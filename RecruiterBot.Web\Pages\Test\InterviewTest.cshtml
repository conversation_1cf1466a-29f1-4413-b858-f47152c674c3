@page
@model RecruiterBot.Web.Pages.Test.InterviewTestModel
@{
    ViewData["Title"] = "Interview Functionality Test";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-gear me-2"></i>
                        Interview Functionality Test
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        This page tests the interview management functionality to ensure all services are properly configured and working.
                    </div>

                    <!-- Test Results -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <h5>Test Results:</h5>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <pre style="white-space: pre-wrap; font-family: 'Courier New', monospace;">@Model.TestResults</pre>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Test Actions -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Test Actions:</h5>
                            <div class="d-flex gap-2 mb-3">
                                <a href="/Test/InterviewTest" class="btn btn-primary">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    Refresh Tests
                                </a>
                                <button type="button" class="btn btn-success" id="createTestInterviewBtn">
                                    <i class="bi bi-plus-circle me-1"></i>
                                    Create Test Interview
                                </button>
                                <a href="/Interviews" class="btn btn-outline-info">
                                    <i class="bi bi-calendar-event me-1"></i>
                                    View Interviews
                                </a>
                                <a href="/Interviews/Create" class="btn btn-outline-secondary">
                                    <i class="bi bi-calendar-plus me-1"></i>
                                    Create Interview
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Links -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Quick Links:</h5>
                            <div class="list-group">
                                <a href="/Interviews" class="list-group-item list-group-item-action">
                                    <i class="bi bi-calendar-event me-2"></i>
                                    Interview Management (All Roles)
                                </a>
                                <a href="/Consultant/MyInterviews" class="list-group-item list-group-item-action">
                                    <i class="bi bi-calendar-check me-2"></i>
                                    My Assigned Interviews (Consultants)
                                </a>
                                <a href="/Candidates" class="list-group-item list-group-item-action">
                                    <i class="bi bi-people me-2"></i>
                                    Candidate Management
                                </a>
                                <a href="/Admin/LLMModels" class="list-group-item list-group-item-action">
                                    <i class="bi bi-cpu me-2"></i>
                                    LLM Model Management (Admin)
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Test Status -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="alert alert-success">
                                <h6><i class="bi bi-check-circle me-2"></i>Interview Management Features Implemented:</h6>
                                <ul class="mb-0">
                                    <li>✅ Interview entity with proper MongoDB attributes and tenant isolation</li>
                                    <li>✅ Interview service with CRUD operations and role-based access</li>
                                    <li>✅ Interview creation for Standard Users with validation</li>
                                    <li>✅ Interview assignment with email notifications to consultants</li>
                                    <li>✅ Consultant view for assigned interviews with timezone conversion</li>
                                    <li>✅ Status management and interview lifecycle tracking</li>
                                    <li>✅ Navigation and authorization policies</li>
                                    <li>✅ Database registration and service injection</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const createTestBtn = document.getElementById('createTestInterviewBtn');
            
            createTestBtn.addEventListener('click', function() {
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Creating...';
                
                fetch('/Test/InterviewTest?handler=CreateTestInterview', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('✅ Test interview created successfully!\n\nInterview ID: ' + data.interviewId + '\n\nYou can view it in the Interviews section.');
                    } else {
                        alert('❌ Error creating test interview:\n\n' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('❌ An error occurred while creating the test interview.');
                })
                .finally(() => {
                    this.disabled = false;
                    this.innerHTML = '<i class="bi bi-plus-circle me-1"></i>Create Test Interview';
                });
            });
        });
    </script>
}
