using RecruiterBot.Web.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Services
{
    /// <summary>
    /// Service for managing bot configurations in the system.
    /// All operations are scoped to the authenticated user.
    /// </summary>
    public interface IBotConfigurationService
    {
        /// <summary>
        /// Retrieves all bot configurations for the specified user.
        /// </summary>
        /// <param name="userId">The ID of the user who owns the configurations.</param>
        /// <returns>A list of bot configurations sorted by creation date (newest first).</returns>
        /// <exception cref="ArgumentException">Thrown when userId is null or empty.</exception>
        Task<IEnumerable<BotConfiguration>> GetAllConfigurationsAsync(string userId);

        /// <summary>
        /// Retrieves a specific bot configuration by ID, ensuring it belongs to the specified user.
        /// </summary>
        /// <param name="id">The ID of the configuration to retrieve.</param>
        /// <param name="userId">The ID of the user who owns the configuration.</param>
        /// <returns>The bot configuration if found; otherwise, null.</returns>
        /// <exception cref="ArgumentException">Thrown when id or userId is null or empty.</exception>
        Task<BotConfiguration> GetConfigurationAsync(string id, string userId);

        /// <summary>
        /// Creates a new bot configuration for the specified user.
        /// </summary>
        /// <param name="configuration">The configuration to create.</param>
        /// <param name="userId">The ID of the user who will own the configuration.</param>
        /// <returns>The created configuration with generated ID and timestamps.</returns>
        /// <exception cref="ArgumentNullException">Thrown when configuration is null.</exception>
        /// <exception cref="ArgumentException">Thrown when userId is null or empty.</exception>
        Task<BotConfiguration> CreateConfigurationAsync(BotConfiguration configuration, string userId);

        /// <summary>
        /// Updates an existing bot configuration, ensuring it belongs to the specified user.
        /// </summary>
        /// <param name="id">The ID of the configuration to update.</param>
        /// <param name="configuration">The updated configuration data.</param>
        /// <param name="userId">The ID of the user who owns the configuration.</param>
        /// <returns>True if the update was successful; otherwise, false.</returns>
        /// <exception cref="ArgumentException">Thrown when id or userId is null or empty.</exception>
        /// <exception cref="ArgumentNullException">Thrown when configuration is null.</exception>
        Task<bool> UpdateConfigurationAsync(string id, BotConfiguration configuration, string userId);

        /// <summary>
        /// Deletes a bot configuration, ensuring it belongs to the specified user.
        /// </summary>
        /// <param name="id">The ID of the configuration to delete.</param>
        /// <param name="userId">The ID of the user who owns the configuration.</param>
        /// <returns>True if the deletion was successful; otherwise, false.</returns>
        /// <exception cref="ArgumentException">Thrown when id or userId is null or empty.</exception>
        Task<bool> DeleteConfigurationAsync(string id, string userId);
        
        /// <summary>
        /// Updates the last run timestamp for a bot configuration.
        /// </summary>
        /// <param name="id">The ID of the configuration to update.</param>
        /// <param name="userId">The ID of the user who owns the configuration.</param>
        /// <param name="lastRunUtc">The UTC timestamp of when the bot was last run.</param>
        /// <returns>True if the update was successful; otherwise, false.</returns>
        /// <exception cref="ArgumentException">Thrown when id or userId is null or empty.</exception>
        Task<bool> UpdateLastRunAsync(string id, string userId, DateTime lastRunUtc);
        
        /// <summary>
        /// Retrieves all active bot configurations across all users.
        /// This is typically used by background services to process active configurations.
        /// </summary>
        /// <returns>A list of all active bot configurations.</returns>
        Task<IEnumerable<BotConfiguration>> GetActiveConfigurationsAsync(string userId = null);

        /// <summary>
        /// Retrieves all job runs for a specific bot configuration.
        /// </summary>
        /// <param name="botId">The ID of the bot configuration.</param>
        /// <param name="userId">The ID of the user who owns the configuration.</param>
        /// <returns>A list of job runs sorted by run date (newest first).</returns>
        /// <exception cref="ArgumentException">Thrown when botId or userId is null or empty.</exception>
        Task<IEnumerable<JobRun>> GetJobRunsAsync(string botId, string userId);

        /// <summary>
        /// Retrieves a specific job run by ID, ensuring it belongs to the specified user.
        /// </summary>
        /// <param name="jobRunId">The ID of the job run to retrieve.</param>
        /// <param name="userId">The ID of the user who owns the job run.</param>
        /// <returns>The job run if found; otherwise, null.</returns>
        /// <exception cref="ArgumentException">Thrown when jobRunId or userId is null or empty.</exception>
        Task<JobRun> GetJobRunAsync(string jobRunId, string userId);

        /// <summary>
        /// Retrieves all job runs across all bot configurations for a specific user, grouped by bot.
        /// </summary>
        /// <param name="userId">The ID of the user who owns the configurations.</param>
        /// <returns>A dictionary where keys are bot configurations and values are lists of job runs.</returns>
        /// <exception cref="ArgumentException">Thrown when userId is null or empty.</exception>
        Task<Dictionary<BotConfiguration, List<JobRun>>> GetAllJobRunsGroupedByBotAsync(string userId);

        /// <summary>
        /// Retrieves all bot configurations for a Corp Admin's team (all users created by the Corp Admin).
        /// </summary>
        /// <param name="corpAdminId">The ID of the Corp Admin.</param>
        /// <returns>A list of bot configurations from all team members.</returns>
        /// <exception cref="ArgumentException">Thrown when corpAdminId is null or empty.</exception>
        Task<IEnumerable<BotConfiguration>> GetTeamConfigurationsAsync(string corpAdminId);

        /// <summary>
        /// Retrieves bot configurations for users under a specific Corp Admin.
        /// </summary>
        /// <param name="userIds">List of user IDs to get configurations for.</param>
        /// <returns>A list of bot configurations.</returns>
        Task<IEnumerable<BotConfiguration>> GetConfigurationsForUsersAsync(IEnumerable<string> userIds);
    }
}
