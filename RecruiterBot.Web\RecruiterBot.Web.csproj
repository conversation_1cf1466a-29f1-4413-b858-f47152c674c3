<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-RecruiterBot.Web-71be973f-8cb6-4ce0-b025-df0c1328c56f</UserSecretsId>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <None Update="app.db" CopyToOutputDirectory="PreserveNewest" ExcludeFromSingleFile="true" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.5" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="MongoDB.Driver.Core" Version="2.30.0" />
    <PackageReference Include="MongoDB.Driver.GridFS" Version="2.30.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.5" Condition="'$(Configuration)' == 'Debug'" />
    <PackageReference Include="SendGrid" Version="9.29.3" />
    <PackageReference Include="UglyToad.PdfPig" Version="1.7.0-custom-5" />
    <PackageReference Include="OllamaSharp" Version="3.0.8" />
    <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.0.1-preview.1.24570.5" />
    <PackageReference Include="Serilog" Version="4.2.0" />
    <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
    <PackageReference Include="Serilog.Enrichers.Process" Version="3.0.0" />
    <PackageReference Include="Serilog.Enrichers.Thread" Version="4.0.0" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RecruiterBot.Infrastructure\RecruiterBot.Infrastructure.csproj" />
    <ProjectReference Include="..\RecruiterBot.Core\RecruiterBot.Core.csproj" />
  </ItemGroup>

</Project>
