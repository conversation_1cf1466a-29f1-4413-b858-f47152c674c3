using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class CreateModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ICandidateService _candidateService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(IBotConfigurationService botConfigService, ICandidateService candidateService, IRoleManagementService roleManagementService, ILogger<CreateModel> logger)
        {
            _botConfigService = botConfigService;
            _candidateService = candidateService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public List<SelectListItem> AvailableCandidates { get; set; } = new List<SelectListItem>();

        public class InputModel
        {
            [Required]
            [Display(Name = "Search Query")]
            public string Query { get; set; }

            [Required]
            [Display(Name = "Candidate Search Type")]
            public CandidateSearchType CandidateSearch { get; set; } = CandidateSearchType.AI;

            [Display(Name = "LinkedIn Username")]
            public string LinkedInUsername { get; set; }

            [Display(Name = "LinkedIn Password")]
            [DataType(DataType.Password)]
            public string LinkedInPassword { get; set; }

            [Display(Name = "Is Active")]
            public bool IsActive { get; set; } = true;

            [Display(Name = "Select Candidates")]
            public List<string> SelectedCandidates { get; set; } = new List<string>();
        }

        public async Task<IActionResult> OnGetAsync()
        {
            await LoadCandidatesAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadCandidatesAsync();
                return Page();
            }

            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    ModelState.AddModelError(string.Empty, "User not found.");
                    return Page();
                }

                var configuration = new BotConfiguration
                {
                    Query = Input.Query,
                    CandidateSearch = Input.CandidateSearch,
                    IsActive = Input.IsActive,
                    // Tenant will be set by the service based on Corp Admin
                    LinkedInUsername = Input.LinkedInUsername,
                    LinkedInPassword = Input.LinkedInPassword,
                    AssignedCandidates = Input.CandidateSearch == CandidateSearchType.Manual ? Input.SelectedCandidates : new List<string>()
                };

                var createdConfig = await _botConfigService.CreateConfigurationAsync(configuration, userId);

                // If Manual search type and candidates are selected, update candidate records
                if (Input.CandidateSearch == CandidateSearchType.Manual && Input.SelectedCandidates?.Any() == true)
                {
                    await UpdateCandidateBotAssociationsAsync(Input.SelectedCandidates, createdConfig.Id, createdConfig.Query, userId);
                }
                
                TempData["SuccessMessage"] = "Bot configuration created successfully.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating bot configuration");
                ModelState.AddModelError(string.Empty, "An error occurred while creating the bot configuration.");
                await LoadCandidatesAsync();
                return Page();
            }
        }

        private async Task LoadCandidatesAsync()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);
                    List<Candidate> candidates;

                    if (userRole == UserRoles.CorpAdmin)
                    {
                        // Corp Admin can see all team candidates
                        candidates = await _candidateService.GetTeamCandidatesAsync(userId, 0, 100, false);
                    }
                    else if (userRole == UserRoles.User)
                    {
                        // Standard users can only see their own candidates
                        candidates = await _candidateService.GetUserOwnCandidatesAsync(userId, 0, 100, false);
                    }
                    else
                    {
                        // Admin or other roles see their own candidates
                        candidates = await _candidateService.GetUserOwnCandidatesAsync(userId, 0, 100, false);
                    }

                    AvailableCandidates = candidates.Select(c => new SelectListItem
                    {
                        Value = c.Id,
                        Text = $"{c.FullName} ({c.Email})"
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading candidates");
                AvailableCandidates = new List<SelectListItem>();
            }
        }

        private async Task UpdateCandidateBotAssociationsAsync(List<string> candidateIds, string botId, string botQuery, string userId)
        {
            try
            {
                foreach (var candidateId in candidateIds)
                {
                    var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                    if (candidate != null)
                    {
                        if (!candidate.AssignedBotIds.Contains(botId))
                        {
                            candidate.AssignedBotIds.Add(botId);
                            candidate.AssignedBotQueries.Add(botQuery);
                            await _candidateService.UpdateCandidateAsync(candidate, userId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate bot associations");
            }
        }
    }
}
