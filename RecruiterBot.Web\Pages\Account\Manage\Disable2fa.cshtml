@page
@model Disable2faModel
@{
    ViewData["Title"] = "Disable two-factor authentication (2FA)";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <div class="alert alert-warning">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <label><strong>This action only disables 2FA.</strong></label>
        </div>
    </div>
    
    <div class="card bg-base-200">
        <div class="card-body">
            <p class="mb-4">
                Disabling 2FA does not change the keys used in authenticator apps. If you wish to change the key
                used in an authenticator app you should <a asp-page="./ResetAuthenticator" class="link">reset your authenticator keys.</a>
            </p>
            <div>
                <form method="post" class="form-group">
                    <button class="btn btn-error" type="submit">Disable 2FA</button>
                </form>
            </div>
        </div>
    </div>
</div>
