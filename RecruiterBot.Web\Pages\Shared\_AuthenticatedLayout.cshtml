@using Microsoft.AspNetCore.Identity
@inject SignInManager<User> SignInManager
@inject UserManager<User> UserManager

<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - RecruiterBot</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sidebars.css" asp-append-version="true" />
    @await RenderSectionAsync("Styles", required: false)
</head>
<body class="d-flex">
    <!-- Sidebar -->
    <div class="sidebar bg-dark text-white" id="sidebar">
        <div class="sidebar-content p-3">
            @await Component.InvokeAsync("Sidebar")
        </div>
    </div>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Top Navbar (only shown on mobile) -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-dark d-lg-none">
            <div class="container-fluid">
                <button class="btn btn-link text-white" type="button" id="mobileSidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
                <partial name="_LoginPartial" />
            </div>
        </nav>

        <!-- Page Content -->
        <main role="main" class="p-4">
            @RenderBody()
        </main>
    </div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/sidebars.js" asp-append-version="true"></script>
    
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
