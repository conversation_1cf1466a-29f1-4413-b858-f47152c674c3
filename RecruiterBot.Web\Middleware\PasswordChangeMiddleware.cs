using Microsoft.AspNetCore.Identity;
using RecruiterBot.Core.Models;

namespace RecruiterBot.Web.Middleware
{
    public class PasswordChangeMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<PasswordChangeMiddleware> _logger;

        public PasswordChangeMiddleware(RequestDelegate next, ILogger<PasswordChangeMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, UserManager<User> userManager)
        {
            // Skip middleware for certain paths
            var path = context.Request.Path.Value?.ToLower();
            var skipPaths = new[]
            {
                "/account/login",
                "/account/logout",
                "/account/changepassword",
                "/account/activateaccount",
                "/account/setupaccount",
                "/account/lockout",
                "/account/accessdenied",
                "/api/",
                "/css/",
                "/js/",
                "/lib/",
                "/images/",
                "/favicon.ico"
            };

            if (skipPaths.Any(skipPath => path?.StartsWith(skipPath) == true))
            {
                await _next(context);
                return;
            }

            // Check if user is authenticated
            if (context.User.Identity?.IsAuthenticated == true)
            {
                try
                {
                    var user = await userManager.GetUserAsync(context.User);
                    if (user != null && (user.MustChangePassword || user.IsTemporaryPassword))
                    {
                        // User must change password, redirect to change password page
                        var returnUrl = context.Request.Path + context.Request.QueryString;
                        var changePasswordUrl = $"/Account/ChangePassword?returnUrl={Uri.EscapeDataString(returnUrl)}&forced=true";
                        
                        _logger.LogInformation("User {UserId} must change password, redirecting from {Path}", user.Id, path);
                        context.Response.Redirect(changePasswordUrl);
                        return;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking password change requirement for user");
                }
            }

            await _next(context);
        }
    }

    public static class PasswordChangeMiddlewareExtensions
    {
        public static IApplicationBuilder UsePasswordChangeMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<PasswordChangeMiddleware>();
        }
    }
}
