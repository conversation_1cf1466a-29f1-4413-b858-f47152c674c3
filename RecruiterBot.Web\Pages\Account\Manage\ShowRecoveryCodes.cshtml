@page
@model ShowRecoveryCodesModel
@{
    ViewData["Title"] = "Recovery codes";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <div class="alert alert-warning">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <label>Put these codes in a safe place.</label>
        </div>
    </div>
    
    <div class="card bg-base-200">
        <div class="card-body">
            <p class="mb-4">
                If you lose your device and don't have the recovery codes you will lose access to your account.
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                @for (var row = 0; row < Model.RecoveryCodes.Length; row += 2)
                {
                    <code class="text-center">@Model.RecoveryCodes[row]</code>
                    @if (row + 1 < Model.RecoveryCodes.Length)
                    {
                        <code class="text-center">@Model.RecoveryCodes[row + 1]</code>
                    }
                }
            </div>
        </div>
    </div>
    
    <div class="alert alert-info">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <label>You can generate new recovery codes by clicking the button below.</label>
        </div>
    </div>
    
    <div>
        <a asp-page="./GenerateRecoveryCodes" class="btn btn-primary">Generate new recovery codes</a>
    </div>
</div>
