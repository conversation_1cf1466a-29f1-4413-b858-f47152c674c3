using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Constants;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages
{
    [Authorize]
    public class DashboardModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<DashboardModel> _logger;

        public DashboardModel(IBotConfigurationService botConfigService, IRoleManagementService roleManagementService, ILogger<DashboardModel> logger)
        {
            _botConfigService = botConfigService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        public DashboardStats Stats { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return RedirectToPage("/Account/Login");
                }

                var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);

                if (userRole == UserRoles.Admin)
                {
                    // Admin dashboard - show Corp Admin statistics
                    var corpAdminUsers = await _roleManagementService.GetUsersByRoleAsync(UserRoles.CorpAdmin);
                    var activeCorpAdmins = corpAdminUsers.Where(u => u.IsActive);

                    Stats = new DashboardStats
                    {
                        TotalCorpAdmins = corpAdminUsers.Count(),
                        ActiveCorpAdmins = activeCorpAdmins.Count(),
                        LastUpdated = DateTime.UtcNow
                    };
                }
                else
                {
                    // Corp Admin and User dashboard - show bot statistics
                    var allBots = await _botConfigService.GetAllConfigurationsAsync(userId);
                    var activeBots = await _botConfigService.GetActiveConfigurationsAsync(userId);

                    Stats = new DashboardStats
                    {
                        TotalBots = allBots?.Count() ?? 0,
                        ActiveBots = activeBots?.Count() ?? 0,
                        InactiveBots = (allBots?.Count() ?? 0) - (activeBots?.Count() ?? 0),
                        AiSearchBots = allBots?.Count(b => b.CandidateSearch == CandidateSearchType.AI) ?? 0,
                        ManualSearchBots = allBots?.Count(b => b.CandidateSearch == CandidateSearchType.Manual) ?? 0,
                        LastUpdated = DateTime.UtcNow
                    };
                }

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard stats");
                ModelState.AddModelError("", "An error occurred while loading dashboard stats.");
                return Page();
            }
        }
    }
}
