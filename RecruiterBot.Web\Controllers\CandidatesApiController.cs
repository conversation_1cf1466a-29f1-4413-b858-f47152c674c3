using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RecruiterBot.Infrastructure.Services;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Controllers
{
    [Authorize]
    [ApiController]
    [Route("api/candidates")]
    public class CandidatesApiController : ControllerBase
    {
        private readonly ICandidateService _candidateService;
        private readonly ILogger<CandidatesApiController> _logger;

        public CandidatesApiController(
            ICandidateService candidateService,
            ILogger<CandidatesApiController> logger)
        {
            _candidateService = candidateService;
            _logger = logger;
        }

        [HttpPost("activate")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ActivateCandidate([FromForm] string candidateId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return BadRequest(new { success = false, message = "User not authenticated." });
            }

            if (string.IsNullOrEmpty(candidateId))
            {
                return BadRequest(new { success = false, message = "Candidate ID is required." });
            }

            try
            {
                // Get candidate name for the success message
                var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                if (candidate == null)
                {
                    return NotFound(new { success = false, message = "Candidate not found." });
                }

                // Activate the candidate
                var activated = await _candidateService.ActivateCandidateAsync(candidateId, userId);

                if (activated)
                {
                    _logger.LogInformation("Candidate {CandidateId} activated by user {UserId}", candidateId, userId);
                    return Ok(new { success = true, message = $"Candidate '{candidate.FullName}' has been activated successfully." });
                }
                else
                {
                    return BadRequest(new { success = false, message = "Candidate not found or already active." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error activating candidate {CandidateId} for user {UserId}", candidateId, userId);
                return StatusCode(500, new { success = false, message = "An error occurred while activating the candidate." });
            }
        }

        [HttpPost("deactivate")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeactivateCandidate([FromForm] string candidateId)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return BadRequest(new { success = false, message = "User not authenticated." });
            }

            if (string.IsNullOrEmpty(candidateId))
            {
                return BadRequest(new { success = false, message = "Candidate ID is required." });
            }

            try
            {
                // Get candidate name for the success message
                var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                if (candidate == null)
                {
                    return NotFound(new { success = false, message = "Candidate not found." });
                }

                // Deactivate the candidate (without reason)
                var deactivated = await _candidateService.DeactivateCandidateAsync(candidateId, userId, null);

                if (deactivated)
                {
                    _logger.LogInformation("Candidate {CandidateId} deactivated by user {UserId}", candidateId, userId);
                    return Ok(new { success = true, message = $"Candidate '{candidate.FullName}' has been deactivated successfully." });
                }
                else
                {
                    return BadRequest(new { success = false, message = "Failed to deactivate candidate. It may have already been deactivated." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deactivating candidate {CandidateId} for user {UserId}", candidateId, userId);
                return StatusCode(500, new { success = false, message = "An error occurred while deactivating the candidate." });
            }
        }
    }
}
