using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using RecruiterBot.Web.Models;

namespace RecruiterBot.Web.Data;

public class ApplicationDbContext : IdentityDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
        : base(options)
    {
    }

    public DbSet<JobRun> JobRuns { get; set; }
    public DbSet<BotConfiguration> BotConfigurations { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Configure the BotConfiguration entity
        modelBuilder.Entity<BotConfiguration>()
            .<PERSON><PERSON>any(b => b.JobRuns)
            .WithOne(j => j.<PERSON>t)
            .HasForeignKey(j => j.BotId)
            .OnDelete(DeleteBehavior.Cascade);
            
        // Configure the JobRun entity
        modelBuilder.Entity<JobRun>()
            .HasOne(j => j.<PERSON><PERSON>)
            .WithMany(b => b.JobRuns)
            .HasForeignKey(j => j.BotId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
