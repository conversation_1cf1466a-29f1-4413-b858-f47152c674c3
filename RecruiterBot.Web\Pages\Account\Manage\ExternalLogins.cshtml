@page
@model ExternalLoginsModel
@{
    ViewData["Title"] = "Manage your external logins";
    ViewData["ActivePage"] = ManageNavPages.ExternalLogins;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <partial name="_StatusMessage" for="StatusMessage" />
    
    @if (Model.CurrentLogins?.Count > 0)
    {
        <div class="card bg-base-100 shadow">
            <div class="card-body">
                <h4 class="card-title">Registered Logins</h4>
                <table class="table w-full">
                    <tbody>
                        @foreach (var login in Model.CurrentLogins)
                        {
                            <tr>
                                <td>@login.ProviderDisplayName</td>
                                <td>
                                    @if (Model.ShowRemoveButton)
                                    {
                                        <form id="remove-login" asp-page-handler="RemoveLogin" method="post">
                                            <div>
                                                <input asp-for="@login.LoginProvider" name="LoginProvider" type="hidden" />
                                                <input asp-for="@login.ProviderKey" name="ProviderKey" type="hidden" />
                                                <button type="submit" class="btn btn-sm btn-error" 
                                                        title="Remove this @login.ProviderDisplayName login from your account">
                                                    Remove
                                                </button>
                                            </div>
                                        </form>
                                    }
                                    else
                                    {
                                        @: &nbsp;
                                    }
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }
    
    @if (Model.OtherLogins?.Count > 0)
    {
        <div class="card bg-base-100 shadow mt-6">
            <div class="card-body">
                <h4 class="card-title">Add another service to log in</h4>
                <form id="link-login-form" asp-page-handler="LinkLogin" method="post" class="form-horizontal">
                    <div id="socialLoginList">
                        <p>
                            @foreach (var provider in Model.OtherLogins)
                            {
                                <button id="link-login-button" type="submit" class="btn btn-outline" name="provider" value="@provider.Name" 
                                        title="Log in using your @provider.DisplayName account">
                                    @provider.DisplayName
                                </button>
                            }
                        </p>
                    </div>
                </form>
            </div>
        </div>
    }
</div>
