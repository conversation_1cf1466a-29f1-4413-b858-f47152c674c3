@page
@model RecruiterBot.Web.Pages.Candidates.CreateModel
@{
    ViewData["Title"] = "Add New Candidate";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-person-plus me-2"></i>
                        Add New Candidate
                    </h3>
                </div>
                <div class="card-body">
                    <!-- Upload Resume Section -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-upload me-2"></i>
                                        Option 1: Upload Resume (Recommended)
                                    </h5>
                                    <p class="card-text text-muted">
                                        Upload a resume file and let our AI extract candidate information automatically.
                                    </p>
                                    
                                    <form method="post" enctype="multipart/form-data" asp-page-handler="Upload">
                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group">
                                                    <label asp-for="ResumeFile" class="form-label">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i>
                                                        Resume File
                                                    </label>
                                                    <input asp-for="ResumeFile" 
                                                           class="form-control" 
                                                           type="file" 
                                                           accept=".pdf,.doc,.docx,.txt">
                                                    <span asp-validation-for="ResumeFile" class="text-danger"></span>
                                                    <small class="form-text text-muted">
                                                        Supported formats: PDF, DOC, DOCX, TXT (Max size: 10MB)
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="submit" class="btn btn-primary" id="uploadBtn">
                                                    <i class="bi bi-upload me-2"></i>
                                                    Upload & Parse
                                                </button>
                                            </div>
                                        </div>

                                        @if (!Model.IsOllamaAvailable)
                                        {
                                            <div class="alert alert-warning mt-3">
                                                <i class="bi bi-exclamation-triangle me-2"></i>
                                                <strong>AI Service Unavailable:</strong>
                                                The Ollama AI service is not currently available. You can still upload resumes, but automatic parsing will not be available.
                                            </div>
                                        }

                                        <!-- Progress indicator (hidden by default) -->
                                        <div class="mt-3" id="uploadProgressContainer" style="display: none;">
                                            <div class="alert alert-info">
                                                <div class="d-flex align-items-center">
                                                    <div class="spinner-border spinner-border-sm me-3" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <div>
                                                        <strong id="uploadProgressText">Uploading and parsing resume...</strong>
                                                        <br><small class="text-muted">This may take a few moments.</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Divider -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="text-center">
                                <span class="badge bg-secondary">OR</span>
                            </div>
                            <hr class="my-4">
                        </div>
                    </div>

                    <!-- Manual Entry Section -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title">
                                        <i class="bi bi-pencil-square me-2"></i>
                                        Option 2: Manual Entry
                                    </h5>
                                    <p class="card-text text-muted">
                                        Enter candidate information manually without uploading a resume.
                                    </p>
                                    
                                    <form method="post" asp-page-handler="Manual">
                                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                                        <!-- Personal Information -->
                                        <div class="row">
                                            <div class="col-12">
                                                <h6 class="text-primary mb-3">
                                                    <i class="bi bi-person me-2"></i>
                                                    Personal Information
                                                </h6>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.FirstName" class="form-label"></label>
                                                    <input asp-for="Input.FirstName" class="form-control" />
                                                    <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.LastName" class="form-label"></label>
                                                    <input asp-for="Input.LastName" class="form-control" />
                                                    <span asp-validation-for="Input.LastName" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.Email" class="form-label"></label>
                                                    <input asp-for="Input.Email" class="form-control" type="email" />
                                                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.Phone" class="form-label"></label>
                                                    <input asp-for="Input.Phone" class="form-control" />
                                                    <span asp-validation-for="Input.Phone" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.CurrentTitle" class="form-label"></label>
                                                    <input asp-for="Input.CurrentTitle" class="form-control" />
                                                    <span asp-validation-for="Input.CurrentTitle" class="text-danger"></span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.CurrentCompany" class="form-label"></label>
                                                    <input asp-for="Input.CurrentCompany" class="form-control" />
                                                    <span asp-validation-for="Input.CurrentCompany" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-12">
                                                <div class="form-group mb-3">
                                                    <label asp-for="Input.Summary" class="form-label"></label>
                                                    <textarea asp-for="Input.Summary" class="form-control" rows="3" 
                                                              placeholder="Brief professional summary..."></textarea>
                                                    <span asp-validation-for="Input.Summary" class="text-danger"></span>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Action Buttons -->
                                        <div class="row mt-4">
                                            <div class="col-12">
                                                <button type="submit" class="btn btn-success" id="createBtn">
                                                    <i class="bi bi-person-plus me-2"></i>
                                                    Create Candidate
                                                </button>
                                                <a asp-page="./Index" class="btn btn-secondary ms-2">
                                                    <i class="bi bi-arrow-left me-2"></i>
                                                    Back to Candidates
                                                </a>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        $(document).ready(function() {
            var originalUploadButtonHtml = $('#uploadBtn').html();
            var originalCreateButtonHtml = $('#createBtn').html();

            // Handle upload form submission
            $('form[asp-page-handler="Upload"]').on('submit', function() {
                var $btn = $('#uploadBtn');
                var $progressContainer = $('#uploadProgressContainer');
                var $progressText = $('#uploadProgressText');

                // Disable button and show spinner
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Processing...');

                // Show progress container
                $progressContainer.show();

                // Update progress text based on file type
                var fileInput = $('input[type="file"]')[0];
                if (fileInput && fileInput.files.length > 0) {
                    var fileName = fileInput.files[0].name;
                    var fileExt = fileName.split('.').pop().toLowerCase();

                    if (fileExt === 'pdf' || fileExt === 'txt') {
                        $progressText.text('Uploading and parsing resume with AI...');
                    } else {
                        $progressText.text('Uploading resume...');
                    }
                }
            });

            // Handle manual form submission
            $('form[asp-page-handler="Manual"]').on('submit', function() {
                var $btn = $('#createBtn');
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Creating...');
            });

            // Reset button states if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $uploadBtn = $('#uploadBtn');
                var $createBtn = $('#createBtn');
                var $progressContainer = $('#uploadProgressContainer');

                $uploadBtn.prop('disabled', false);
                $uploadBtn.html(originalUploadButtonHtml);
                $createBtn.prop('disabled', false);
                $createBtn.html(originalCreateButtonHtml);
                $progressContainer.hide();
            }

            // File input validation
            $('input[type="file"]').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var fileSize = file.size / 1024 / 1024; // Convert to MB
                    var maxSize = 10; // 10MB

                    if (fileSize > maxSize) {
                        alert('File size must be less than ' + maxSize + 'MB');
                        $(this).val('');
                        return;
                    }

                    var allowedTypes = ['application/pdf', 'application/msword',
                                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                      'text/plain'];

                    if (!allowedTypes.includes(file.type)) {
                        alert('Please select a valid file type (PDF, DOC, DOCX, or TXT)');
                        $(this).val('');
                        return;
                    }
                }
            });
        });
    </script>
}
