using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Candidates
{
    [Authorize]
    public class DetailsModel : PageModel
    {
        private readonly ICandidateService _candidateService;
        private readonly ILogger<DetailsModel> _logger;

        public DetailsModel(
            ICandidateService candidateService,
            ILogger<DetailsModel> logger)
        {
            _candidateService = candidateService;
            _logger = logger;
        }

        public Candidate Candidate { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                Candidate = await _candidateService.GetCandidateAsync(id, userId);
                if (Candidate == null)
                {
                    return NotFound();
                }

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidate {CandidateId} for user {UserId}", id, userId);
                TempData["ErrorMessage"] = "Error loading candidate information.";
                return RedirectToPage("./Index");
            }
        }
    }
}
