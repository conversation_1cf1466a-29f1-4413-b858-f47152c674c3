using Microsoft.AspNetCore.Mvc;

namespace RecruiterBot.Web.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class TestController : ControllerBase
    {
        private readonly ILogger<TestController> _logger;

        public TestController(ILogger<TestController> logger)
        {
            _logger = logger;
        }

        [HttpGet("logging")]
        public IActionResult TestLogging()
        {
            _logger.LogInformation("Test logging endpoint called at {Timestamp} from IP {RemoteIpAddress}", 
                DateTime.UtcNow, 
                HttpContext.Connection.RemoteIpAddress?.ToString() ?? "Unknown");
            
            _logger.LogWarning("This is a test warning log");
            _logger.LogError("This is a test error log");
            
            try
            {
                throw new InvalidOperationException("Test exception for logging");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Test exception caught and logged");
            }

            return Ok(new { 
                message = "Logging test completed", 
                timestamp = DateTime.UtcNow,
                logFile = "Check logs/recruiterbot-{date}.log for entries"
            });
        }
    }
}
