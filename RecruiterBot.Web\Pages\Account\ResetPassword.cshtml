@page
@model RecruiterBot.Web.Pages.Account.ResetPasswordModel
@{
    ViewData["Title"] = "Reset Password";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-shield-lock" style="font-size: 3rem; color: #007bff;"></i>
                        <h2 class="mt-3">Reset Password</h2>
                        <p class="text-muted">Enter your new password below.</p>
                    </div>

                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <input asp-for="Input.UserId" type="hidden" />
                        <input asp-for="Input.Token" type="hidden" />
                        
                        <div class="form-group mb-3">
                            <label asp-for="Input.Password" class="form-label">New Password</label>
                            <input asp-for="Input.Password" class="form-control" autocomplete="new-password" />
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>

                        <div class="form-group mb-3">
                            <label asp-for="Input.ConfirmPassword" class="form-label">Confirm New Password</label>
                            <input asp-for="Input.ConfirmPassword" class="form-control" autocomplete="new-password" />
                            <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="resetBtn">
                                <i class="bi bi-shield-check me-2"></i>
                                Reset Password
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a asp-page="./Login" class="text-decoration-none">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#resetBtn').html();
            
            // Show loading state when form is submitted
            $('form').on('submit', function() {
                var $btn = $('#resetBtn');
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Resetting...');
            });
            
            // Reset button state if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $btn = $('#resetBtn');
                $btn.prop('disabled', false);
                $btn.html(originalButtonHtml);
            }
        });
    </script>
}
