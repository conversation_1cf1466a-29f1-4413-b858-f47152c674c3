using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IConsultantRegistrationService
    {
        /// <summary>
        /// Automatically registers a candidate as a consultant user in the system
        /// </summary>
        /// <param name="candidate">The candidate to register as a consultant</param>
        /// <param name="createdByUserId">The ID of the user who created the candidate</param>
        /// <returns>The created consultant user, or null if registration failed or user already exists</returns>
        Task<User?> RegisterCandidateAsConsultantAsync(Candidate candidate, string createdByUserId);

        /// <summary>
        /// Checks if a user with the given email already exists in the system
        /// </summary>
        /// <param name="email">Email to check</param>
        /// <returns>True if user exists, false otherwise</returns>
        Task<bool> UserExistsAsync(string email);

        /// <summary>
        /// Gets the consultant user associated with a candidate
        /// </summary>
        /// <param name="candidateEmail">The candidate's email address</param>
        /// <returns>The consultant user if found, null otherwise</returns>
        Task<User?> GetConsultantByEmailAsync(string candidateEmail);
    }
}
