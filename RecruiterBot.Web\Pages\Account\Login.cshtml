@page
@model RecruiterBot.Web.Pages.Account.LoginModel
@{
    ViewData["Title"] = "Welcome Back | RecruiterBot";
    Layout = "_PlainLayout";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth.css" />
}

<div class="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Success/Error Messages -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-check-circle-fill text-green-500 mr-3"></i>
                    <p class="text-green-800 text-sm">@TempData["SuccessMessage"]</p>
                </div>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle-fill text-red-500 mr-3"></i>
                    <p class="text-red-800 text-sm">@TempData["ErrorMessage"]</p>
                </div>
            </div>
        }

        <!-- Login Card -->
        <div class="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
            <!-- Header -->
            <div class="bg-gradient-to-r from-indigo-600 to-purple-600 px-8 py-10 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="bi bi-robot text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Welcome Back</h1>
                <p class="text-indigo-100">Sign in to your RecruiterBot account</p>
            </div>

            <!-- Form Body -->
            <div class="px-8 py-8">
                <form method="post" id="loginForm">
                    @Html.AntiForgeryToken()

                    <!-- Validation Summary -->
                    <div asp-validation-summary="ModelOnly" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm" id="validationSummary"></div>

                    <!-- Email Field -->
                    <div class="mb-6">
                        <label asp-for="Input.Email" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="bi bi-envelope mr-2 text-gray-400"></i>Email Address
                        </label>
                        <input asp-for="Input.Email"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               placeholder="Enter your email address"
                               autocomplete="username"
                               required />
                        <span asp-validation-for="Input.Email" class="text-red-500 text-xs mt-1 block"></span>
                    </div>
                
                    <!-- Password Field -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <label asp-for="Input.Password" class="block text-sm font-medium text-gray-700">
                                <i class="bi bi-lock mr-2 text-gray-400"></i>Password
                            </label>
                            <a asp-page="./ForgotPassword" class="text-sm font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                                Forgot password?
                            </a>
                        </div>
                        <div class="relative">
                            <input asp-for="Input.Password"
                                   type="password"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   placeholder="Enter your password"
                                   autocomplete="current-password"
                                   required />
                            <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword()">
                                <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="toggleIcon"></i>
                            </button>
                        </div>
                        <span asp-validation-for="Input.Password" class="text-red-500 text-xs mt-1 block"></span>
                    </div>

                    <!-- Remember Me -->
                    <div class="flex items-center mb-6">
                        <input asp-for="Input.RememberMe"
                               type="checkbox"
                               class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" />
                        <label asp-for="Input.RememberMe" class="ml-2 block text-sm text-gray-700">
                            Remember me for 30 days
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-indigo-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                            id="loginButton">
                        <span id="loginButtonText">
                            <i class="bi bi-box-arrow-in-right mr-2"></i>Sign In
                        </span>
                        <span id="loginButtonLoading" class="hidden">
                            <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>Signing in...
                        </span>
                    </button>
                </form>

                @if (ViewData["ShowResendLink"] != null && (bool)ViewData["ShowResendLink"])
                {
                    <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-center justify-center">
                            <i class="bi bi-info-circle text-blue-500 mr-2"></i>
                            <span class="text-blue-800 text-sm mr-3">Need to confirm your email?</span>
                            <a asp-page="./ResendEmailConfirmation"
                               class="inline-flex items-center px-3 py-1 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50 transition-colors duration-200">
                                <i class="bi bi-envelope mr-1"></i>
                                Resend Email
                            </a>
                        </div>
                    </div>
                }
            </div>

            <!-- Footer -->
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-100 text-center">
                <p class="text-sm text-gray-600">
                    Don't have an account?
                    <a asp-page="./Register" class="font-medium text-indigo-600 hover:text-indigo-500 transition-colors duration-200">
                        Create one now
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Password toggle functionality
        function togglePassword() {
            const passwordInput = document.querySelector('input[name="Input.Password"]');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash text-gray-400 hover:text-gray-600 transition-colors duration-200';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeLoginForm();
        });

        function initializeLoginForm() {
            const form = document.getElementById('loginForm');
            const submitButton = document.getElementById('loginButton');
            const buttonText = document.getElementById('loginButtonText');
            const buttonLoading = document.getElementById('loginButtonLoading');
            const validationSummary = document.getElementById('validationSummary');
            let isSubmitting = false;

            // Show validation summary if there are errors
            if (validationSummary) {
                const hasErrors = validationSummary.querySelector('ul') && validationSummary.querySelector('ul').children.length > 0;
                if (hasErrors) {
                    validationSummary.style.display = 'block';
                } else {
                    validationSummary.style.display = 'none';
                }
            }

            if (form && submitButton) {
                // Remove any existing event listeners
                const newForm = form.cloneNode(true);
                form.parentNode.replaceChild(newForm, form);

                newForm.addEventListener('submit', function(e) {
                    // Check if form is valid before preventing default
                    if (!newForm.checkValidity()) {
                        return; // Let the browser handle validation
                    }

                    e.preventDefault();

                    if (isSubmitting) return;
                    isSubmitting = true;

                    // Clear previous validation errors
                    if (validationSummary) {
                        validationSummary.style.display = 'none';
                    }

                    // Show loading state
                    const currentButton = document.getElementById('loginButton');
                    const currentButtonText = document.getElementById('loginButtonText');
                    const currentButtonLoading = document.getElementById('loginButtonLoading');

                    if (currentButton) {
                        currentButton.disabled = true;
                        if (currentButtonText) currentButtonText.classList.add('hidden');
                        if (currentButtonLoading) currentButtonLoading.classList.remove('hidden');
                    }

                    // Submit the form normally (let server handle validation)
                    setTimeout(() => {
                        newForm.submit();
                    }, 100);
                });
            }

            // Auto-focus email field
            const emailInput = document.querySelector('input[name="Input.Email"]');
            if (emailInput && !emailInput.value) {
                emailInput.focus();
            }
        }
    </script>
}
