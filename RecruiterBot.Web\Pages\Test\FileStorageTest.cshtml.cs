using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Pages.Test
{
    [Authorize(Roles = UserRoles.Admin)]
    public class FileStorageTestModel : PageModel
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<FileStorageTestModel> _logger;

        public FileStorageTestModel(IFileStorageService fileStorageService, ILogger<FileStorageTestModel> logger)
        {
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        [BindProperty]
        public IFormFile? TestFile { get; set; }

        public string TestResult { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string FileUrl { get; set; } = string.Empty;
        public string StorageProvider { get; set; } = string.Empty;

        public void OnGet()
        {
            StorageProvider = _fileStorageService.GetType().Name;
        }

        public async Task<IActionResult> OnPostAsync()
        {
            StorageProvider = _fileStorageService.GetType().Name;

            if (TestFile == null || TestFile.Length == 0)
            {
                TestResult = "Please select a file to upload.";
                Success = false;
                return Page();
            }

            try
            {
                // Test file upload
                FileUrl = await _fileStorageService.UploadFileAsync(TestFile, "test");
                
                // Test file existence
                var exists = await _fileStorageService.FileExistsAsync(FileUrl);
                
                if (exists)
                {
                    TestResult = $"✅ File uploaded successfully! File exists check: {exists}";
                    Success = true;
                }
                else
                {
                    TestResult = "❌ File uploaded but existence check failed.";
                    Success = false;
                }

                _logger.LogInformation("File storage test completed. File: {FileName}, URL: {FileUrl}, Exists: {Exists}", 
                    TestFile.FileName, FileUrl, exists);
            }
            catch (Exception ex)
            {
                TestResult = $"❌ Error during file upload: {ex.Message}";
                Success = false;
                _logger.LogError(ex, "File storage test failed for file: {FileName}", TestFile?.FileName);
            }

            return Page();
        }
    }
}
