<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - RecruiterBot</title>
    
    <!-- Favicon -->
    <link rel="icon" href="~/favicon.ico" sizes="any" />
    <link rel="icon" href="~/favicon.svg" type="image/svg+xml" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom styles -->
    @await RenderSectionAsync("Styles", required: false)
    
    <style>
        :root {
            --primary: #4f46e5;
            --primary-hover: #4338ca;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            @@apply bg-gray-50 text-gray-900;
        }
        
        /* Animation for form elements */
        .form-control {
            transition: all 0.2s ease-in-out;
        }
        
        .form-control:focus {
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.25);
            border-color: var(--primary);
        }
        
        /* Button hover effect */
        .btn-primary {
            background: linear-gradient(135deg, var(--primary) 0%, #7c3aed 100%);
            transition: all 0.2s ease-in-out;
        }
        
        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
            background: linear-gradient(135deg, var(--primary-hover) 0%, #6d28d9 100%);
        }
        
        /* Animation for form appearance */
        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .auth-card {
            animation: fadeIn 0.3s ease-out forwards;
        }
    </style>
</head>
<body class="min-h-screen">
    <div class="relative">
        <!-- Background decoration -->
        <div class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-purple-50 opacity-75"></div>
        
        <!-- Main content -->
        <main role="main" class="relative z-10">
            @RenderBody()
        </main>
        
        <!-- Footer -->
        <footer class="fixed bottom-0 left-0 right-0 py-4 text-center text-sm text-gray-500">
            &copy; @DateTime.Now.Year - RecruiterBot. All rights reserved.
        </footer>
    </div>

    <!-- Scripts -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="~/lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    
    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
