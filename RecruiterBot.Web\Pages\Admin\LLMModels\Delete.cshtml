@page "{id}"
@model RecruiterBot.Web.Pages.Admin.LLMModels.DeleteModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = $"Delete {Model.Model?.DisplayName ?? "LLM Model"}";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-danger mb-1">
                        <i class="bi bi-trash me-2"></i>
                        Delete LLM Model
                    </h2>
                    <p class="text-muted mb-0">Permanently remove this model from the platform</p>
                </div>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Models
                </a>
            </div>

            <!-- Confirmation -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    @if (Model.Model != null)
                    {
                        <div class="card border-danger">
                            <div class="card-header bg-danger text-white">
                                <h5 class="mb-0">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    Confirm Deletion
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning" role="alert">
                                    <i class="bi bi-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> This action cannot be undone. The model configuration will be permanently deleted.
                                </div>

                                <!-- Model Details -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h6 class="text-muted mb-3">Model Details</h6>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Display Name</small>
                                                <div class="fw-bold">@Model.Model.DisplayName</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Model Name</small>
                                                <div class="fw-bold">@Model.Model.ModelName</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Provider</small>
                                                <div class="fw-bold">
                                                    <span class="badge bg-info">@Model.Model.ProviderDisplayName</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Status</small>
                                                <div class="fw-bold">
                                                    <span class="@Model.Model.StatusBadgeClass">@Model.Model.StatusDisplayName</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    @if (!string.IsNullOrEmpty(Model.Model.Description))
                                    {
                                        <div class="col-12 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body py-2">
                                                    <small class="text-muted">Description</small>
                                                    <div>@Model.Model.Description</div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Type</small>
                                                <div class="fw-bold">@Model.Model.TypeDisplayName</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Max Tokens</small>
                                                <div class="fw-bold">@Model.Model.MaxTokens.ToString("N0")</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card bg-light">
                                            <div class="card-body py-2">
                                                <small class="text-muted">Created</small>
                                                <div>@Model.Model.CreatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</div>
                                            </div>
                                        </div>
                                    </div>
                                    @if (Model.Model.UpdatedAt > Model.Model.CreatedAt)
                                    {
                                        <div class="col-md-6 mb-3">
                                            <div class="card bg-light">
                                                <div class="card-body py-2">
                                                    <small class="text-muted">Last Updated</small>
                                                    <div>@Model.Model.UpdatedAt.ToString("MMM dd, yyyy 'at' h:mm tt")</div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>

                                <!-- Confirmation Form -->
                                <form method="post">
                                    @Html.AntiForgeryToken()
                                    <input asp-for="ModelId" type="hidden" />
                                    
                                    <div class="mb-4">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="confirmDelete" required>
                                            <label class="form-check-label" for="confirmDelete">
                                                I understand that this action cannot be undone and want to permanently delete this model.
                                            </label>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-end gap-2">
                                        <a asp-page="./Index" class="btn btn-secondary">
                                            <i class="bi bi-x-circle me-2"></i>Cancel
                                        </a>
                                        <button type="submit" class="btn btn-danger" id="deleteBtn" disabled>
                                            <i class="bi bi-trash me-2"></i>
                                            <span class="btn-text">Delete Model</span>
                                            <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="card border-warning">
                            <div class="card-body text-center py-5">
                                <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
                                <h4 class="mt-3">Model Not Found</h4>
                                <p class="text-muted">The requested model could not be found or may have already been deleted.</p>
                                <a asp-page="./Index" class="btn btn-primary">
                                    <i class="bi bi-arrow-left me-2"></i>Back to Models
                                </a>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Enable/disable delete button based on confirmation checkbox
        document.getElementById('confirmDelete').addEventListener('change', function() {
            const deleteBtn = document.getElementById('deleteBtn');
            deleteBtn.disabled = !this.checked;
        });

        // Form submission with loading state
        document.querySelector('form')?.addEventListener('submit', function() {
            const deleteBtn = document.getElementById('deleteBtn');
            const btnText = deleteBtn.querySelector('.btn-text');
            const spinner = deleteBtn.querySelector('.spinner-border');
            
            deleteBtn.disabled = true;
            btnText.textContent = 'Deleting...';
            spinner.classList.remove('d-none');
        });
    </script>
}
