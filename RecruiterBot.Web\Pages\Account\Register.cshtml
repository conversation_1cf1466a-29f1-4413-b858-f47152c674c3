@page
@model RegisterModel
@{
    ViewData["Title"] = "Register as Corp Admin | RecruiterBot";
    Layout = "_PlainLayout";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth.css" />
}

<div class="min-h-screen bg-gradient-to-br from-purple-50 via-white to-pink-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Success/Error Messages -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-check-circle-fill text-green-500 mr-3"></i>
                    <p class="text-green-800 text-sm">@TempData["SuccessMessage"]</p>
                </div>
            </div>
        }

        @if (TempData["ErrorMessage"] != null)
        {
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle-fill text-red-500 mr-3"></i>
                    <p class="text-red-800 text-sm">@TempData["ErrorMessage"]</p>
                </div>
            </div>
        }

        <!-- Registration Card -->
        <div class="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
            <!-- Header -->
            <div class="bg-gradient-to-r from-purple-600 to-pink-600 px-8 py-10 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="bi bi-building text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">Register as Corp Admin</h1>
                <p class="text-purple-100">Create your corporate administrator account</p>
            </div>

            <!-- Form Body -->
            <div class="px-8 py-8">
                <!-- Info Notice -->
                @* <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="flex items-start">
                        <i class="bi bi-info-circle text-blue-500 mr-3 mt-0.5"></i>
                        <div>
                            <h3 class="text-blue-800 font-medium mb-1">Corporate Administrator Account</h3>
                            <p class="text-blue-700 text-sm">
                                As a Corp Admin, you'll be able to create and manage standard user accounts, configure recruitment bots, and access team analytics.
                            </p>
                        </div>
                    </div>
                </div> *@
                <form id="registerForm" method="post">
                    @Html.AntiForgeryToken()

                    <!-- Validation Summary -->
                    <div asp-validation-summary="ModelOnly" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm"></div>

                    <!-- Name Fields -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div>
                            <label asp-for="Input.FirstName" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-person mr-2 text-gray-400"></i>First Name
                            </label>
                            <input asp-for="Input.FirstName"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
                                   placeholder="Enter your first name"
                                   autocomplete="given-name"
                                   required />
                            <span asp-validation-for="Input.FirstName" class="text-red-500 text-xs mt-1 block"></span>
                        </div>

                        <div>
                            <label asp-for="Input.LastName" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-person mr-2 text-gray-400"></i>Last Name
                            </label>
                            <input asp-for="Input.LastName"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
                                   placeholder="Enter your last name"
                                   autocomplete="family-name"
                                   required />
                            <span asp-validation-for="Input.LastName" class="text-red-500 text-xs mt-1 block"></span>
                        </div>
                    </div>

                    <!-- Email Field -->
                    <div class="mb-6">
                        <label asp-for="Input.Email" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="bi bi-envelope mr-2 text-gray-400"></i>Corporate Email Address
                        </label>
                        <input asp-for="Input.Email"
                               type="email"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors duration-200"
                               placeholder="<EMAIL>"
                               autocomplete="email"
                               required />
                        <span asp-validation-for="Input.Email" class="text-red-500 text-xs mt-1 block"></span>
                        <p class="text-xs text-gray-500 mt-1">Use your corporate email address</p>
                    </div>

                    <!-- Information Notice -->
                    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="bi bi-info-circle text-blue-500 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-blue-800 font-medium mb-1">Account Setup Process</h3>
                                <p class="text-blue-700 text-sm">
                                    After registration, you'll receive an email with a temporary password and setup instructions.
                                    You'll set your permanent password during the account setup process.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mb-6">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input id="terms" name="terms" type="checkbox"
                                       class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                                       required>
                            </div>
                            <div class="ml-3 text-sm">
                                <label for="terms" class="font-medium text-gray-700">
                                    I agree to the <a href="#" class="text-purple-600 hover:text-purple-500 transition-colors duration-200">Terms of Service</a>
                                    and <a href="#" class="text-purple-600 hover:text-purple-500 transition-colors duration-200">Privacy Policy</a>
                                </label>
                                <p class="text-red-500 text-xs mt-1" id="terms-error"></p>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <button type="submit"
                            class="w-full bg-gradient-to-r from-purple-600 to-pink-600 text-white py-3 px-4 rounded-lg font-medium hover:from-purple-700 hover:to-pink-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                            id="registerButton">
                        <span id="registerButtonText">
                            <i class="bi bi-building mr-2"></i>Register as Corp Admin
                        </span>
                        <span id="registerButtonLoading" class="hidden">
                            <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>Creating account...
                        </span>
                    </button>
                </form>

                <!-- Help Text -->
                <div class="mt-6 text-center">
                    <p class="text-sm text-gray-500">
                        Need help? Contact our support team for assistance.
                    </p>
                </div>
            </div>

            <!-- Footer -->
            <div class="px-8 py-6 bg-gray-50 border-t border-gray-100 text-center">
                <p class="text-sm text-gray-600">
                    Already have an account?
                    <a asp-page="./Login" class="font-medium text-purple-600 hover:text-purple-500 transition-colors duration-200">
                        Sign in here
                    </a>
                </p>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>


        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registerForm');
            const submitButton = document.getElementById('registerButton');
            const buttonText = document.getElementById('registerButtonText');
            const buttonLoading = document.getElementById('registerButtonLoading');
            const termsCheckbox = document.getElementById('terms');
            const termsError = document.getElementById('terms-error');
            let isSubmitting = false;

            // Show validation summary if there are errors
            const validationSummary = document.querySelector('[asp-validation-summary]');
            if (validationSummary) {
                const hasErrors = validationSummary.querySelector('ul') && validationSummary.querySelector('ul').children.length > 0;
                if (!hasErrors) {
                    validationSummary.style.display = 'none';
                }
            }

            if (form) {
                form.addEventListener('submit', function(e) {
                    // Validate terms checkbox
                    if (!termsCheckbox.checked) {
                        e.preventDefault();
                        termsError.textContent = 'You must accept the terms and conditions';
                        return;
                    } else {
                        termsError.textContent = '';
                    }

                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }

                    // Check if form is valid
                    if (!this.checkValidity()) {
                        return true;
                    }

                    const hasValidationErrors = Array.from(form.querySelectorAll('.text-red-500')).some(el => el.textContent.trim() !== '');
                    if (hasValidationErrors) {
                        return true;
                    }

                    isSubmitting = true;

                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('hidden');
                    buttonLoading.classList.remove('hidden');

                    setTimeout(function() {
                        if (submitButton.disabled) {
                            submitButton.disabled = false;
                            buttonText.classList.remove('hidden');
                            buttonLoading.classList.add('hidden');
                            isSubmitting = false;
                        }
                    }, 30000);
                });
            }

            // Terms checkbox validation
            if (termsCheckbox) {
                termsCheckbox.addEventListener('change', function() {
                    if (this.checked) {
                        termsError.textContent = '';
                    }
                });
            }

            // Auto-focus first name field
            const firstNameInput = document.querySelector('input[name="Input.FirstName"]');
            if (firstNameInput) {
                firstNameInput.focus();
            }
        });
    </script>
}
