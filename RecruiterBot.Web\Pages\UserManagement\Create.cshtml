@page
@model RecruiterBot.Web.Pages.UserManagement.CreateModel
@{
    var pageTitle = Model.CurrentUserRole == "Admin" ? "Create Corporate User" : "Create User";
    ViewData["Title"] = pageTitle;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-person-plus me-2"></i>
                        @pageTitle
                    </h2>
                    <p class="text-muted mb-0">
                        @if (Model.CurrentUserRole == "Admin")
                        {
                            <text>Create a new corporate user account</text>
                        }
                        else
                        {
                            <text>Create a new user account</text>
                        }
                    </p>
                </div>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Users
                </a>
            </div>

            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-person-badge me-2"></i>
                                @if (Model.CurrentUserRole == "Admin")
                                {
                                    <text>Corporate User Information</text>
                                }
                                else
                                {
                                    <text>User Information</text>
                                }
                            </h5>
                        </div>
                        <div class="card-body">
                            <form method="post">
                                <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label asp-for="Input.FirstName" class="form-label"></label>
                                            <input asp-for="Input.FirstName" class="form-control" />
                                            <span asp-validation-for="Input.FirstName" class="text-danger"></span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label asp-for="Input.LastName" class="form-label"></label>
                                            <input asp-for="Input.LastName" class="form-control" />
                                            <span asp-validation-for="Input.LastName" class="text-danger"></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Input.Email" class="form-label"></label>
                                    <input asp-for="Input.Email" class="form-control" type="email" />
                                    <span asp-validation-for="Input.Email" class="text-danger"></span>
                                </div>

                                <div class="form-group mb-3">
                                    <label asp-for="Input.Role" class="form-label"></label>
                                    <select asp-for="Input.Role" class="form-select">
                                        <option value="">Select a role...</option>
                                        @foreach (var role in Model.AvailableRoles)
                                        {
                                            <option value="@role">@RecruiterBot.Core.Constants.UserRoles.GetDisplayName(role)</option>
                                        }
                                    </select>
                                    <span asp-validation-for="Input.Role" class="text-danger"></span>
                                    <div class="form-text">
                                        @if (Model.Input?.Role != null)
                                        {
                                            @RecruiterBot.Core.Constants.UserRoles.GetDescription(Model.Input.Role)
                                        }
                                    </div>
                                </div>

                                <hr class="my-4">

                                <h6 class="text-primary mb-3">
                                    <i class="bi bi-envelope me-2"></i>
                                    Account Activation
                                </h6>

                                <div class="form-check mb-3">
                                    <input asp-for="Input.SendActivationEmail" class="form-check-input" type="checkbox" />
                                    <label asp-for="Input.SendActivationEmail" class="form-check-label">
                                        Send activation email with temporary credentials
                                    </label>
                                    <div class="form-text">
                                        The user will receive an email with temporary login credentials and an activation link. They must activate their account and change their password on first login.
                                    </div>
                                </div>

                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Security Notice:</strong> A secure temporary password will be automatically generated. The user will be required to change it upon first login for security purposes.
                                </div>

                                <div class="d-flex justify-content-end gap-2">
                                    <a asp-page="./Index" class="btn btn-secondary">
                                        <i class="bi bi-x-circle me-2"></i>
                                        Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary" id="createBtn">
                                        <i class="bi bi-person-plus me-2"></i>
                                        Create User
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#createBtn').html();
            
            // Show loading state when form is submitted (only if validation passes)
            $('form').on('submit', function(e) {
                var $form = $(this);
                var $btn = $('#createBtn');
                
                // Check if form is valid using HTML5 validation
                if (!this.checkValidity()) {
                    return true; // Let the browser handle validation display
                }
                
                // Check for any existing validation errors from server-side validation
                var hasValidationErrors = $form.find('.text-danger').filter(function() {
                    return $(this).text().trim() !== '';
                }).length > 0;
                
                if (hasValidationErrors) {
                    return true;
                }
                
                // Form appears to be valid, show loading state
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Creating...');
                
                // Set a timeout to reset the button if something goes wrong
                setTimeout(function() {
                    if ($btn.prop('disabled')) {
                        $btn.prop('disabled', false);
                        $btn.html(originalButtonHtml);
                    }
                }, 30000);
            });

            // Update role description when role changes
            $('#Input_Role').on('change', function() {
                var selectedRole = $(this).val();
                var $description = $(this).siblings('.form-text');
                
                if (selectedRole) {
                    // You can add role descriptions here
                    var descriptions = {
                        'Admin': 'Full system access, can create Corporate users',
                        'Corp': 'Can create and manage Standard users',
                        'User': 'Standard user access'
                    };
                    $description.text(descriptions[selectedRole] || '');
                } else {
                    $description.text('');
                }
            });
        });
    </script>
}
