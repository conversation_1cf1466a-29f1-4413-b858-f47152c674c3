using Microsoft.AspNetCore.Identity;
using RecruiterBot.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IRoleManagementService
    {
        Task<bool> InitializeRolesAsync();
        Task<IEnumerable<User>> GetUsersByRoleAsync(string role);
        Task<bool> AssignRoleToUserAsync(string userId, string role);
        Task<bool> RemoveRoleFromUserAsync(string userId, string role);
        Task<bool> CanUserCreateRole(string currentUserId, string targetRole);
        Task<IEnumerable<string>> GetCreatableRolesForUserAsync(string userId);
        Task<string> GetUserPrimaryRoleAsync(string userId);
        Task<bool> IsUserInRoleAsync(string userId, string role);
        Task<IEnumerable<User>> GetUsersCreatedByAsync(string creatorId);
        Task<bool> CanUserManageUserAsync(string managerId, string targetUserId);
        Task<IEnumerable<User>> GetTeamMembersAsync(string corpAdminId);
        Task<string> GetCorpAdminForUserAsync(string userId);
        Task<User?> GetCorpAdminByDomainAsync(string emailDomain);
        Task<bool> CanCreateCorpAdminForDomainAsync(string email);
        Task<IEnumerable<string>> GetUserRolesAsync(string userId);
        Task<string> GetCorpAdminIdAsync(string userId);
        Task<User?> GetUserByIdAsync(string userId);
        Task<IEnumerable<User>> GetUsersInTenantByRoleAsync(string tenantId, string role);
    }
}
