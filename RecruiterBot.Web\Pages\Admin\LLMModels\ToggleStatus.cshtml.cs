using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Admin.LLMModels
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class ToggleStatusModel : PageModel
    {
        private readonly ILLMModelService _llmModelService;
        private readonly ILogger<ToggleStatusModel> _logger;

        public ToggleStatusModel(ILLMModelService llmModelService, ILogger<ToggleStatusModel> logger)
        {
            _llmModelService = llmModelService;
            _logger = logger;
        }

        public async Task<IActionResult> OnPostAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return BadRequest("Model ID is required");
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized();
                }

                var success = await _llmModelService.ToggleModelStatusAsync(id, userId);

                if (success)
                {
                    return new JsonResult(new { success = true, message = "Model status updated successfully" });
                }
                else
                {
                    return BadRequest("Failed to update model status");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling model status for {ModelId}", id);
                return StatusCode(500, "An error occurred while updating the model status");
            }
        }
    }
}
