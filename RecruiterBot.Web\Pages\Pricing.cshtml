@page "/pricing"
@model PricingModel
@{
    ViewData["Title"] = "Pricing - RecruiterBot";
}

@section Styles {
    <style>
        .pricing-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .pricing-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            border-radius: 20px;
            overflow: hidden;
        }
        
        .pricing-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .pricing-card.featured {
            border: 3px solid #667eea;
            transform: scale(1.05);
        }
        
        .pricing-card.featured:hover {
            transform: scale(1.05) translateY(-10px);
        }
        
        .price-amount {
            font-size: 3.5rem;
            font-weight: bold;
            line-height: 1;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .feature-list li:last-child {
            border-bottom: none;
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .faq-item {
            border: none;
            border-bottom: 1px solid #e9ecef;
        }
        
        .faq-item:last-child {
            border-bottom: none;
        }
    </style>
}

<!-- Hero Section -->
<section class="pricing-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4">Simple, Transparent Pricing</h1>
                <p class="lead mb-4">
                    Choose the perfect plan for your hiring needs. All plans include our core AI features 
                    with no hidden fees or setup costs.
                </p>
                <div class="d-flex justify-content-center align-items-center gap-3 mb-4">
                    <span>Monthly</span>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="billingToggle">
                        <label class="form-check-label" for="billingToggle">Annual (Save 20%)</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Cards -->
<section class="py-5">
    <div class="container">
        <div class="row g-4 justify-content-center">
            <!-- Starter Plan -->
            <div class="col-lg-4 col-md-6">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-5">
                        <h3 class="fw-bold mb-3">Starter</h3>
                        <p class="text-muted mb-4">Perfect for small teams getting started</p>
                        <div class="mb-4">
                            <span class="price-amount text-primary" data-monthly="99" data-annual="79">$99</span>
                            <span class="text-muted fs-5">/month</span>
                        </div>
                        <ul class="feature-list mb-4">
                            <li><i class="bi bi-check-circle text-success me-2"></i>Up to 50 candidates/month</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Basic AI resume parsing</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Standard matching algorithm</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Email support</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Basic analytics dashboard</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>1 user account</li>
                        </ul>
                        <a href="/Account/Register?plan=starter" class="btn btn-outline-primary btn-lg w-100">
                            Start Free Trial
                        </a>
                    </div>
                </div>
            </div>

            <!-- Professional Plan (Featured) -->
            <div class="col-lg-4 col-md-6">
                <div class="card pricing-card featured h-100">
                    <div class="card-header bg-primary text-white text-center py-3">
                        <span class="badge bg-warning text-dark fs-6">Most Popular</span>
                    </div>
                    <div class="card-body text-center p-5">
                        <h3 class="fw-bold mb-3">Professional</h3>
                        <p class="text-muted mb-4">Ideal for growing companies</p>
                        <div class="mb-4">
                            <span class="price-amount text-primary" data-monthly="299" data-annual="239">$299</span>
                            <span class="text-muted fs-5">/month</span>
                        </div>
                        <ul class="feature-list mb-4">
                            <li><i class="bi bi-check-circle text-success me-2"></i>Up to 200 candidates/month</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Advanced AI parsing & analysis</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Smart matching with ML</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Priority support</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Advanced analytics & reports</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Up to 5 user accounts</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Team collaboration tools</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Custom integrations</li>
                        </ul>
                        <a href="/Account/Register?plan=professional" class="btn btn-gradient btn-lg w-100">
                            Start Free Trial
                        </a>
                    </div>
                </div>
            </div>

            <!-- Enterprise Plan -->
            <div class="col-lg-4 col-md-6">
                <div class="card pricing-card h-100">
                    <div class="card-body text-center p-5">
                        <h3 class="fw-bold mb-3">Enterprise</h3>
                        <p class="text-muted mb-4">For large organizations with custom needs</p>
                        <div class="mb-4">
                            <span class="price-amount text-primary">Custom</span>
                        </div>
                        <ul class="feature-list mb-4">
                            <li><i class="bi bi-check-circle text-success me-2"></i>Unlimited candidates</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Custom AI model training</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>White-label solution</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Dedicated account manager</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Custom analytics & reporting</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Unlimited user accounts</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>Advanced security features</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>SLA guarantee</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>On-premise deployment option</li>
                        </ul>
                        <a href="/contact?inquiry=enterprise" class="btn btn-outline-primary btn-lg w-100">
                            Contact Sales
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Comparison -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="text-center fw-bold mb-5">Feature Comparison</h2>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>Feature</th>
                                <th class="text-center">Starter</th>
                                <th class="text-center">Professional</th>
                                <th class="text-center">Enterprise</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Monthly Candidates</td>
                                <td class="text-center">50</td>
                                <td class="text-center">200</td>
                                <td class="text-center">Unlimited</td>
                            </tr>
                            <tr>
                                <td>AI Resume Parsing</td>
                                <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                                <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                                <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Smart Matching</td>
                                <td class="text-center">Basic</td>
                                <td class="text-center">Advanced</td>
                                <td class="text-center">Custom</td>
                            </tr>
                            <tr>
                                <td>User Accounts</td>
                                <td class="text-center">1</td>
                                <td class="text-center">5</td>
                                <td class="text-center">Unlimited</td>
                            </tr>
                            <tr>
                                <td>Analytics & Reporting</td>
                                <td class="text-center">Basic</td>
                                <td class="text-center">Advanced</td>
                                <td class="text-center">Custom</td>
                            </tr>
                            <tr>
                                <td>API Access</td>
                                <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                                <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                                <td class="text-center"><i class="bi bi-check-circle text-success"></i></td>
                            </tr>
                            <tr>
                                <td>Custom Integrations</td>
                                <td class="text-center"><i class="bi bi-x-circle text-danger"></i></td>
                                <td class="text-center">Limited</td>
                                <td class="text-center">Unlimited</td>
                            </tr>
                            <tr>
                                <td>Support</td>
                                <td class="text-center">Email</td>
                                <td class="text-center">Priority</td>
                                <td class="text-center">Dedicated</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const billingToggle = document.getElementById('billingToggle');
            const priceElements = document.querySelectorAll('.price-amount');
            
            billingToggle.addEventListener('change', function() {
                priceElements.forEach(element => {
                    if (element.dataset.monthly && element.dataset.annual) {
                        if (this.checked) {
                            element.textContent = '$' + element.dataset.annual;
                        } else {
                            element.textContent = '$' + element.dataset.monthly;
                        }
                    }
                });
            });
        });
    </script>
}
