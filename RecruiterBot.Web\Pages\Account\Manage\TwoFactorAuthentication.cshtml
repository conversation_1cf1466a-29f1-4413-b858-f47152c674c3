@page
@model TwoFactorAuthenticationModel
@{
    ViewData["Title"] = "Two-factor authentication";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <partial name="_StatusMessage" for="StatusMessage" />
    
    @if (Model.Is2faEnabled)
    {
        if (Model.RecoveryCodesLeft == 0)
        {
            <div class="alert alert-error">
                <div class="flex-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <label><strong>You have no recovery codes left.</strong> You must <a asp-page="./GenerateRecoveryCodes">generate a new set of recovery codes</a> before you can log in with a recovery code.</label>
                </div>
            </div>
        }
        else if (Model.RecoveryCodesLeft == 1)
        {
            <div class="alert alert-warning">
                <div class="flex-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <label><strong>You have 1 recovery code left.</strong> You can <a asp-page="./GenerateRecoveryCodes">generate a new set of recovery codes</a>.</label>
                </div>
            </div>
        }
        else if (Model.RecoveryCodesLeft <= 3)
        {
            <div class="alert alert-warning">
                <div class="flex-1">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <label><strong>You have @Model.RecoveryCodesLeft recovery codes left.</strong> You should <a asp-page="./GenerateRecoveryCodes">generate a new set of recovery codes</a>.</label>
                </div>
            </div>
        }

        if (Model.IsMachineRemembered)
        {
            <form method="post" style="display: inline-block">
                <button type="submit" class="btn btn-sm btn-outline">Forget this browser</button>
            </form>
        }

        <a asp-page="./Disable2fa" class="btn btn-error">Disable 2FA</a>
        <a asp-page="./GenerateRecoveryCodes" class="btn btn-warning">Reset recovery codes</a>
    }

    <h4 class="text-md font-medium mt-6">Authenticator app</h4>
    
    @if (!Model.HasAuthenticator)
    {
        <a id="enable-authenticator" asp-page="./EnableAuthenticator" class="btn btn-primary">Add authenticator app</a>
    }
    else
    {
        <a id="enable-authenticator" asp-page="./EnableAuthenticator" class="btn btn-primary">Set up authenticator app</a>
        <a id="reset-authenticator" asp-page="./ResetAuthenticator" class="btn btn-error">Reset authenticator app</a>
    }
</div>
