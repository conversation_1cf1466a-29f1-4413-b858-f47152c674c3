@page
@model RecruiterBot.Web.Pages.Account.SetupAccountModel
@{
    ViewData["Title"] = Model.IsRegistration ? "Complete Registration | RecruiterBot" : "Account Setup | RecruiterBot";
    Layout = "_PlainLayout";
}

@section Styles {
    <link rel="stylesheet" href="~/css/auth.css" />
}

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
    <div class="w-full max-w-md">
        <!-- Success/Error Messages -->
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-check-circle-fill text-green-500 mr-3"></i>
                    <p class="text-green-800 text-sm">@TempData["SuccessMessage"]</p>
                </div>
            </div>
        }
        
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle-fill text-red-500 mr-3"></i>
                    <p class="text-red-800 text-sm">@TempData["ErrorMessage"]</p>
                </div>
            </div>
        }

        <!-- Setup Card -->
        <div class="bg-white shadow-2xl rounded-2xl overflow-hidden border border-gray-100">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-10 text-center">
                <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="bi bi-person-gear text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-white mb-2">
                    @if (Model.IsRegistration)
                    {
                        <text>Complete Registration</text>
                    }
                    else
                    {
                        <text>Account Setup</text>
                    }
                </h1>
                <p class="text-blue-100">
                    @if (Model.IsRegistration)
                    {
                        <text>Set your password to complete registration</text>
                    }
                    else
                    {
                        <text>Activate your account and set a secure password</text>
                    }
                </p>
            </div>
            
            <!-- Form Body -->
            <div class="px-8 py-8">
                @if (Model.IsValidToken)
                {
                    <!-- Welcome Message -->
                    <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex items-start">
                            <i class="bi bi-info-circle text-blue-500 mr-3 mt-0.5"></i>
                            <div>
                                <h3 class="text-blue-800 font-medium mb-1">Welcome, @Model.User.FirstName!</h3>
                                <p class="text-blue-700 text-sm">
                                    @if (Model.IsRegistration)
                                    {
                                        <text>Complete your registration by setting a secure password. You'll then be able to access your RecruiterBot account.</text>
                                    }
                                    else
                                    {
                                        <text>Complete your account setup by entering your temporary password and setting a new secure password.</text>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <form method="post" id="setupForm">
                        <input type="hidden" name="userId" value="@Model.UserId" />
                        <input type="hidden" name="token" value="@Model.Token" />
                        <input type="hidden" name="registration" value="@Model.IsRegistration.ToString().ToLower()" />
                        
                        <!-- Validation Summary -->
                        <div asp-validation-summary="ModelOnly" class="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg text-red-800 text-sm" style="display: none;"></div>
                        
                        <!-- Account Details -->
                        <div class="mb-6">
                            <h3 class="text-lg font-medium text-gray-900 mb-3 flex items-center">
                                <i class="bi bi-person-circle text-blue-600 mr-2"></i>
                                Account Details
                            </h3>
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Name:</span>
                                    <span class="text-sm text-gray-900">@Model.User.FirstName @Model.User.LastName</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm font-medium text-gray-600">Email:</span>
                                    <span class="text-sm text-gray-900">@Model.User.Email</span>
                                </div>
                            </div>
                        </div>

                        @if (!Model.IsLoggedIn)
                        {
                            <!-- Temporary Password -->
                            <div class="mb-6">
                                <label asp-for="Input.TemporaryPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="bi bi-key mr-2 text-gray-400"></i>
                                    @if (Model.IsRegistration)
                                    {
                                        <text>Registration Code</text>
                                    }
                                    else
                                    {
                                        <text>Temporary Password</text>
                                    }
                                </label>
                                <div class="relative">
                                    <input asp-for="Input.TemporaryPassword"
                                           type="password"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200"
                                           placeholder="@(Model.IsRegistration ? "Enter your registration code" : "Enter your temporary password")"
                                           autocomplete="current-password"
                                           required />
                                    <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.TemporaryPassword', 'tempToggleIcon')">
                                        <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="tempToggleIcon"></i>
                                    </button>
                                </div>
                                <span asp-validation-for="Input.TemporaryPassword" class="text-red-500 text-xs mt-1 block"></span>
                                <p class="text-xs text-gray-500 mt-1">
                                    @if (Model.IsRegistration)
                                    {
                                        <text>Enter the registration code sent to your email</text>
                                    }
                                    else
                                    {
                                        <text>Enter the temporary password sent to your email</text>
                                    }
                                </p>
                            </div>
                        }
                        else
                        {
                            <!-- Already logged in message -->
                            <div class="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                                <div class="flex items-start">
                                    <i class="bi bi-check-circle text-green-500 mr-3 mt-0.5"></i>
                                    <div>
                                        <h3 class="text-green-800 font-medium mb-1">Authentication Verified</h3>
                                        <p class="text-green-700 text-sm">
                                            You're already logged in. Please set your permanent password to complete the account setup.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- New Password -->
                        <div class="mb-6">
                            <label asp-for="Input.NewPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-shield-lock mr-2 text-gray-400"></i>New Password
                            </label>
                            <div class="relative">
                                <input asp-for="Input.NewPassword" 
                                       type="password"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" 
                                       placeholder="Enter your new password" 
                                       autocomplete="new-password" 
                                       required />
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.NewPassword', 'newToggleIcon')">
                                    <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="newToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Input.NewPassword" class="text-red-500 text-xs mt-1 block"></span>
                            <p class="text-xs text-gray-500 mt-1">Must be at least 8 characters long</p>
                        </div>

                        <!-- Confirm Password -->
                        <div class="mb-6">
                            <label asp-for="Input.ConfirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                <i class="bi bi-shield-check mr-2 text-gray-400"></i>Confirm New Password
                            </label>
                            <div class="relative">
                                <input asp-for="Input.ConfirmPassword" 
                                       type="password"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200" 
                                       placeholder="Confirm your new password" 
                                       autocomplete="new-password" 
                                       required />
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('Input.ConfirmPassword', 'confirmToggleIcon')">
                                    <i class="bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200" id="confirmToggleIcon"></i>
                                </button>
                            </div>
                            <span asp-validation-for="Input.ConfirmPassword" class="text-red-500 text-xs mt-1 block"></span>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-3 px-4 rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none" 
                                id="setupBtn">
                            <span id="setupButtonText">
                                <i class="bi bi-person-check mr-2"></i>
                                @if (Model.IsRegistration)
                                {
                                    <text>Complete Registration</text>
                                }
                                else
                                {
                                    <text>Setup Account</text>
                                }
                            </span>
                            <span id="setupButtonLoading" class="hidden">
                                <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>Setting up...
                            </span>
                        </button>
                    </form>

                    <!-- Help Text -->
                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            Having trouble? Contact your administrator for assistance.
                        </p>
                    </div>
                }
                else
                {
                    <!-- Error State -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="bi bi-exclamation-triangle text-red-600 text-2xl"></i>
                        </div>
                        
                        <h3 class="text-lg font-medium text-gray-900 mb-2">Invalid Setup Link</h3>
                        <p class="text-gray-600 mb-6">
                            This setup link is invalid or has expired. Please contact your administrator for a new setup link.
                        </p>

                        <a href="/Account/Login" 
                           class="inline-flex items-center px-6 py-3 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                            <i class="bi bi-arrow-left mr-2"></i>
                            Back to Login
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Password toggle functionality
        function togglePassword(inputName, iconId) {
            const passwordInput = document.querySelector(`input[name="${inputName}"]`);
            const toggleIcon = document.getElementById(iconId);
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash text-gray-400 hover:text-gray-600 transition-colors duration-200';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'bi bi-eye text-gray-400 hover:text-gray-600 transition-colors duration-200';
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('setupForm');
            const submitButton = document.getElementById('setupBtn');
            const buttonText = document.getElementById('setupButtonText');
            const buttonLoading = document.getElementById('setupButtonLoading');
            let isSubmitting = false;

            // Show validation summary if there are errors
            const validationSummary = document.querySelector('[asp-validation-summary]');
            if (validationSummary && validationSummary.innerHTML.trim()) {
                validationSummary.style.display = 'block';
            }

            if (form) {
                form.addEventListener('submit', function(e) {
                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }
                    
                    // Check if form is valid
                    if (!this.checkValidity()) {
                        return true;
                    }
                    
                    const hasValidationErrors = Array.from(form.querySelectorAll('.text-red-500')).some(el => el.textContent.trim() !== '');
                    if (hasValidationErrors) {
                        return true;
                    }
                    
                    isSubmitting = true;
                    
                    // Show loading state
                    submitButton.disabled = true;
                    buttonText.classList.add('hidden');
                    buttonLoading.classList.remove('hidden');
                    
                    setTimeout(function() {
                        if (submitButton.disabled) {
                            submitButton.disabled = false;
                            buttonText.classList.remove('hidden');
                            buttonLoading.classList.add('hidden');
                            isSubmitting = false;
                        }
                    }, 30000);
                });
            }

            // Auto-focus first input field
            const tempPasswordInput = document.querySelector('input[name="Input.TemporaryPassword"]');
            const newPasswordInput = document.querySelector('input[name="Input.NewPassword"]');

            if (tempPasswordInput) {
                tempPasswordInput.focus();
            } else if (newPasswordInput) {
                newPasswordInput.focus();
            }
        });
    </script>
}
