using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Options;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Configuration;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Pages.Test
{
    [Authorize(Roles = UserRoles.Admin)]
    public class StorageConfigModel : PageModel
    {
        private readonly IFileStorageService _fileStorageService;
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _environment;
        private readonly FileStorageOptions _storageOptions;

        public StorageConfigModel(
            IFileStorageService fileStorageService, 
            IConfiguration configuration,
            IWebHostEnvironment environment,
            IOptions<FileStorageOptions> storageOptions)
        {
            _fileStorageService = fileStorageService;
            _configuration = configuration;
            _environment = environment;
            _storageOptions = storageOptions.Value;
        }

        public string CurrentProvider { get; set; } = string.Empty;
        public bool IsGcpEnabled { get; set; }
        public string BucketName { get; set; } = string.Empty;
        public string Environment { get; set; } = string.Empty;
        public string ProjectId { get; set; } = string.Empty;

        public void OnGet()
        {
            CurrentProvider = _fileStorageService.GetType().Name;
            IsGcpEnabled = _storageOptions.Enabled;
            BucketName = _storageOptions.BucketName;
            Environment = _environment.EnvironmentName;
            ProjectId = _configuration["GCP:ProjectId"] ?? "Not configured";
        }
    }
}
