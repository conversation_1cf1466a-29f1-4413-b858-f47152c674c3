using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IInterviewProfileService
    {
        /// <summary>
        /// Gets all interview profiles for a specific consultant (tenant-based isolation)
        /// </summary>
        /// <param name="consultantId">The consultant's user ID</param>
        /// <returns>Collection of interview profiles for the consultant</returns>
        Task<IEnumerable<InterviewProfile>> GetProfilesByConsultantAsync(string consultantId);

        /// <summary>
        /// Gets an interview profile by ID with tenant validation
        /// </summary>
        /// <param name="id">Profile ID</param>
        /// <param name="consultantId">The consultant's user ID for validation</param>
        /// <returns>Interview profile if found and belongs to consultant, null otherwise</returns>
        Task<InterviewProfile?> GetProfileByIdAsync(string id, string consultantId);

        /// <summary>
        /// Creates a new interview profile for a consultant
        /// </summary>
        /// <param name="profile">Profile to create</param>
        /// <param name="consultantId">The consultant's user ID</param>
        /// <returns>Created profile</returns>
        Task<InterviewProfile> CreateProfileAsync(InterviewProfile profile, string consultantId);

        /// <summary>
        /// Updates an existing interview profile
        /// </summary>
        /// <param name="profile">Profile to update</param>
        /// <param name="consultantId">The consultant's user ID for validation</param>
        /// <returns>Updated profile if successful, null if not found or unauthorized</returns>
        Task<InterviewProfile?> UpdateProfileAsync(InterviewProfile profile, string consultantId);

        /// <summary>
        /// Deletes an interview profile
        /// </summary>
        /// <param name="id">ID of the profile to delete</param>
        /// <param name="consultantId">The consultant's user ID for validation</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        Task<bool> DeleteProfileAsync(string id, string consultantId);

        /// <summary>
        /// Searches interview profiles by text content
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="consultantId">The consultant's user ID</param>
        /// <returns>Collection of matching profiles</returns>
        Task<IEnumerable<InterviewProfile>> SearchProfilesAsync(string searchTerm, string consultantId);

        /// <summary>
        /// Gets the count of profiles for a consultant
        /// </summary>
        /// <param name="consultantId">The consultant's user ID</param>
        /// <returns>Number of profiles</returns>
        Task<int> GetProfileCountAsync(string consultantId);

        /// <summary>
        /// Validates that a consultant can access a specific profile
        /// </summary>
        /// <param name="profileId">Profile ID</param>
        /// <param name="consultantId">The consultant's user ID</param>
        /// <returns>True if consultant can access the profile, false otherwise</returns>
        Task<bool> CanConsultantAccessProfileAsync(string profileId, string consultantId);

        /// <summary>
        /// Gets a formatted profile string for AI context
        /// </summary>
        /// <param name="profileId">Profile ID</param>
        /// <param name="consultantId">The consultant's user ID for validation</param>
        /// <returns>Formatted profile string for AI prompts, null if not found or unauthorized</returns>
        Task<string?> GetFormattedProfileForAIAsync(string profileId, string consultantId);
    }
}
