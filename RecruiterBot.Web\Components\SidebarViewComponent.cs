using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using RecruiterBot.Core.Models;
using RecruiterBot.Web.Data;

namespace RecruiterBot.Web.Components
{
    public class SidebarViewComponent : ViewComponent
    {
        private readonly UserManager<User> _userManager;
        private readonly SignInManager<User> _signInManager;

        public SidebarViewComponent(UserManager<User> userManager, SignInManager<User> signInManager)
        {
            _userManager = userManager;
            _signInManager = signInManager;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            var user = await _userManager.GetUserAsync(HttpContext.User);
            return View(user);
        }
    }
}
