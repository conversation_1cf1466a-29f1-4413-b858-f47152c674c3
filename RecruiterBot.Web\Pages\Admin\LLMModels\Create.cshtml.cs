using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Admin.LLMModels
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class CreateModel : PageModel
    {
        private readonly ILLMModelService _llmModelService;
        private readonly ILogger<CreateModel> _logger;

        public CreateModel(ILLMModelService llmModelService, ILogger<CreateModel> logger)
        {
            _llmModelService = llmModelService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public SelectList ProviderOptions { get; set; } = new(new List<SelectListItem>());
        public SelectList TypeOptions { get; set; } = new(new List<SelectListItem>());

        public class InputModel
        {
            [Required(ErrorMessage = "Model provider is required")]
            [Display(Name = "Model Provider")]
            public ModelProvider ModelProvider { get; set; }

            [Required(ErrorMessage = "Model name is required")]
            [StringLength(100, ErrorMessage = "Model name cannot exceed 100 characters")]
            [Display(Name = "Model Name")]
            public string ModelName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Display name is required")]
            [StringLength(150, ErrorMessage = "Display name cannot exceed 150 characters")]
            [Display(Name = "Display Name")]
            public string DisplayName { get; set; } = string.Empty;

            [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
            [Display(Name = "Description")]
            public string? Description { get; set; }

            [Display(Name = "API Key")]
            [DataType(DataType.Password)]
            public string? ApiKey { get; set; }

            [Display(Name = "Model Type")]
            public ModelType ModelType { get; set; } = ModelType.Free;

            [Display(Name = "Is Active")]
            public bool IsActive { get; set; } = true;

            [Required(ErrorMessage = "Max tokens is required")]
            [Range(1, 1000000, ErrorMessage = "Max tokens must be between 1 and 1,000,000")]
            [Display(Name = "Max Tokens")]
            public int MaxTokens { get; set; } = 4096;
        }

        public IActionResult OnGet()
        {
            try
            {
                LoadSelectLists();
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading create LLM model page");
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the page." });
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    LoadSelectLists();
                    return Page();
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Create the LLM model
                var model = new LLMModel
                {
                    ModelProvider = Input.ModelProvider,
                    ModelName = Input.ModelName.Trim(),
                    DisplayName = Input.DisplayName.Trim(),
                    Description = Input.Description?.Trim(),
                    ApiKey = Input.ApiKey?.Trim(),
                    ModelType = Input.ModelType,
                    IsActive = Input.IsActive,
                    MaxTokens = Input.MaxTokens
                };

                var createdModel = await _llmModelService.CreateModelAsync(model, userId);

                _logger.LogInformation("Admin user {UserId} created LLM model {ModelId} ({ModelName})", 
                    userId, createdModel.Id, createdModel.ModelName);

                return RedirectToPage("./Index", new { SuccessMessage = $"Model '{createdModel.DisplayName}' created successfully." });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error creating LLM model");
                ModelState.AddModelError(string.Empty, ex.Message);
                LoadSelectLists();
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating LLM model");
                ModelState.AddModelError(string.Empty, "An error occurred while creating the model. Please try again.");
                LoadSelectLists();
                return Page();
            }
        }

        private void LoadSelectLists()
        {
            // Load provider options
            var providerItems = Enum.GetValues<ModelProvider>()
                .Select(p => new SelectListItem
                {
                    Value = p.ToString(),
                    Text = p.ToString()
                })
                .ToList();
            ProviderOptions = new SelectList(providerItems, "Value", "Text");

            // Load type options
            var typeItems = Enum.GetValues<ModelType>()
                .Select(t => new SelectListItem
                {
                    Value = t.ToString(),
                    Text = t.ToString()
                })
                .ToList();
            TypeOptions = new SelectList(typeItems, "Value", "Text");
        }
    }
}
