@page
@model ResetAuthenticatorModel
@{
    ViewData["Title"] = "Reset authenticator key";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <div class="alert alert-warning">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
            <label><strong>If you reset your authenticator key your authenticator app will not work until you reconfigure it.</strong></label>
        </div>
    </div>
    
    <div class="card bg-base-200">
        <div class="card-body">
            <p class="mb-4">
                This process disables 2FA until you verify your authenticator app.
                If you do not complete your authenticator app configuration you may lose access to your account.
            </p>
            <div>
                <form id="reset-authenticator-form" method="post" class="form-group">
                    <button id="reset-authenticator-button" class="btn btn-error" type="submit">Reset authenticator key</button>
                </form>
            </div>
        </div>
    </div>
</div>
