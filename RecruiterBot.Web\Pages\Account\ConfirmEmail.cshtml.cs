using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Account
{
    [AllowAnonymous]
    public class ConfirmEmailModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly ILogger<ConfirmEmailModel> _logger;

        public ConfirmEmailModel(UserManager<User> userManager, ILogger<ConfirmEmailModel> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        public bool IsSuccess { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;

        public async Task<IActionResult> OnGetAsync(string userId, string token)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                IsSuccess = false;
                ErrorMessage = "Invalid email confirmation link. Please check the link in your email or request a new confirmation email.";
                return Page();
            }

            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                {
                    IsSuccess = false;
                    ErrorMessage = "User not found. The confirmation link may be invalid or expired.";
                    _logger.LogWarning("Email confirmation attempted for non-existent user ID: {UserId}", userId);
                    return Page();
                }

                if (user.EmailConfirmed)
                {
                    IsSuccess = true;
                    _logger.LogInformation("Email already confirmed for user {UserId}", userId);
                    return Page();
                }

                var result = await _userManager.ConfirmEmailAsync(user, token);
                if (result.Succeeded)
                {
                    IsSuccess = true;
                    _logger.LogInformation("Email confirmed successfully for user {UserId}", userId);
                }
                else
                {
                    IsSuccess = false;
                    ErrorMessage = "Failed to confirm email. The confirmation link may be invalid or expired. Please request a new confirmation email.";
                    _logger.LogWarning("Email confirmation failed for user {UserId}. Errors: {Errors}", 
                        userId, string.Join(", ", result.Errors.Select(e => e.Description)));
                }
            }
            catch (Exception ex)
            {
                IsSuccess = false;
                ErrorMessage = "An error occurred while confirming your email. Please try again or contact support.";
                _logger.LogError(ex, "Error confirming email for user {UserId}", userId);
            }

            return Page();
        }
    }
}
