using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class EditModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ICandidateService _candidateService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<EditModel> _logger;

        public EditModel(IBotConfigurationService botConfigService, ICandidateService candidateService, IRoleManagementService roleManagementService, ILogger<EditModel> logger)
        {
            _botConfigService = botConfigService;
            _candidateService = candidateService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        [BindProperty]
        public string Id { get; set; }

        [BindProperty]
        public InputModel Input { get; set; }

        public List<SelectListItem> AvailableCandidates { get; set; } = new List<SelectListItem>();

        public class InputModel
        {
            [Required]
            [Display(Name = "Search Query")]
            public string Query { get; set; }

            [Required]
            [Display(Name = "Candidate Search Type")]
            public CandidateSearchType CandidateSearch { get; set; } = CandidateSearchType.AI;

            [Display(Name = "LinkedIn Username")]
            public string LinkedInUsername { get; set; }

            [Display(Name = "LinkedIn Password")]
            [DataType(DataType.Password)]
            public string LinkedInPassword { get; set; }

            [Display(Name = "Is Active")]
            public bool IsActive { get; set; }

            [Display(Name = "Select Candidates")]
            public List<string> SelectedCandidates { get; set; } = new List<string>();
        }


        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            var configuration = await _botConfigService.GetConfigurationAsync(id, userId);
            if (configuration == null)
            {
                return NotFound();
            }

            Id = id;
            Input = new InputModel
            {
                Query = configuration.Query,
                CandidateSearch = configuration.CandidateSearch,
                LinkedInUsername = configuration.LinkedInUsername,
                LinkedInPassword = configuration.LinkedInPassword,
                IsActive = configuration.IsActive,
                SelectedCandidates = configuration.AssignedCandidates ?? new List<string>()
            };

            await LoadCandidatesAsync();
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                await LoadCandidatesAsync();
                return Page();
            }

            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                var existingConfig = await _botConfigService.GetConfigurationAsync(Id, userId);
                if (existingConfig == null)
                {
                    return NotFound();
                }

                // Store old assignments for cleanup
                var oldAssignedCandidates = existingConfig.AssignedCandidates ?? new List<string>();

                // Update the existing configuration
                existingConfig.Query = Input.Query;
                existingConfig.CandidateSearch = Input.CandidateSearch;
                existingConfig.IsActive = Input.IsActive;
                existingConfig.AssignedCandidates = Input.CandidateSearch == CandidateSearchType.Manual ? Input.SelectedCandidates : new List<string>();

                // Only update credentials if provided
                if (!string.IsNullOrEmpty(Input.LinkedInUsername))
                {
                    existingConfig.EncryptedLinkedInUsername = Input.LinkedInUsername;
                }
                if (!string.IsNullOrEmpty(Input.LinkedInPassword))
                {
                    existingConfig.EncryptedLinkedInPassword = Input.LinkedInPassword;
                }

                var result = await _botConfigService.UpdateConfigurationAsync(Id, existingConfig, userId);

                if (result)
                {
                    // Update candidate-bot associations
                    await UpdateCandidateBotAssociationsAsync(oldAssignedCandidates, Input.SelectedCandidates, Id, Input.Query, userId);
                }
                if (!result)
                {
                    ModelState.AddModelError("", "Failed to update the configuration.");
                    await LoadCandidatesAsync();
                    return Page();
                }

                TempData["SuccessMessage"] = "Bot configuration updated successfully.";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating bot configuration");
                ModelState.AddModelError("", "An error occurred while updating the bot configuration.");
                await LoadCandidatesAsync();
                return Page();
            }
        }

        private async Task LoadCandidatesAsync()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (!string.IsNullOrEmpty(userId))
                {
                    var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);
                    List<Candidate> candidates;

                    if (userRole == UserRoles.CorpAdmin)
                    {
                        // Corp Admin can see all team candidates
                        candidates = await _candidateService.GetTeamCandidatesAsync(userId, 0, 100, false);
                    }
                    else if (userRole == UserRoles.User)
                    {
                        // Standard users can only see their own candidates
                        candidates = await _candidateService.GetUserOwnCandidatesAsync(userId, 0, 100, false);
                    }
                    else
                    {
                        // Admin or other roles see their own candidates
                        candidates = await _candidateService.GetUserOwnCandidatesAsync(userId, 0, 100, false);
                    }

                    AvailableCandidates = candidates.Select(c => new SelectListItem
                    {
                        Value = c.Id,
                        Text = $"{c.FullName} ({c.Email})"
                    }).ToList();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading candidates");
                AvailableCandidates = new List<SelectListItem>();
            }
        }

        private async Task UpdateCandidateBotAssociationsAsync(List<string> oldCandidateIds, List<string> newCandidateIds, string botId, string botQuery, string userId)
        {
            try
            {
                // Remove bot association from candidates that are no longer assigned
                var candidatesToRemove = oldCandidateIds.Except(newCandidateIds ?? new List<string>()).ToList();
                foreach (var candidateId in candidatesToRemove)
                {
                    var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                    if (candidate != null)
                    {
                        var botIndex = candidate.AssignedBotIds.IndexOf(botId);
                        if (botIndex >= 0)
                        {
                            candidate.AssignedBotIds.RemoveAt(botIndex);
                            if (botIndex < candidate.AssignedBotQueries.Count)
                            {
                                candidate.AssignedBotQueries.RemoveAt(botIndex);
                            }
                            await _candidateService.UpdateCandidateAsync(candidate, userId);
                        }
                    }
                }

                // Add bot association to newly assigned candidates
                var candidatesToAdd = (newCandidateIds ?? new List<string>()).Except(oldCandidateIds).ToList();
                foreach (var candidateId in candidatesToAdd)
                {
                    var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                    if (candidate != null)
                    {
                        if (!candidate.AssignedBotIds.Contains(botId))
                        {
                            candidate.AssignedBotIds.Add(botId);
                            candidate.AssignedBotQueries.Add(botQuery);
                            await _candidateService.UpdateCandidateAsync(candidate, userId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate bot associations");
            }
        }
    }
}
