using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.UserManagement
{
    [Authorize(Policy = AuthorizationPolicies.CanManageUsers)]
    public class IndexModel : PageModel
    {
        private readonly IRoleManagementService _roleManagementService;
        private readonly UserManager<User> _userManager;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(
            IRoleManagementService roleManagementService,
            UserManager<User> userManager,
            ILogger<IndexModel> logger)
        {
            _roleManagementService = roleManagementService;
            _userManager = userManager;
            _logger = logger;
        }

        public IEnumerable<UserViewModel> Users { get; set; } = new List<UserViewModel>();
        public string CurrentUserRole { get; set; }
        public IEnumerable<string> CreatableRoles { get; set; } = new List<string>();

        public class UserViewModel
        {
            public string Id { get; set; }
            public string FirstName { get; set; }
            public string LastName { get; set; }
            public string Email { get; set; }
            public string Role { get; set; }
            public string RoleDisplayName { get; set; }
            public DateTime CreatedAt { get; set; }
            public bool IsActive { get; set; }
            public bool CanManage { get; set; }
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return Challenge();
            }

            CurrentUserRole = await _roleManagementService.GetUserPrimaryRoleAsync(currentUserId);
            CreatableRoles = await _roleManagementService.GetCreatableRolesForUserAsync(currentUserId);

            var users = new List<User>();

            if (CurrentUserRole == UserRoles.Admin)
            {
                // Admin can see all Corp Admin users (both created by Admin and registered via registration page)
                var corpAdminUsers = await _roleManagementService.GetUsersByRoleAsync(UserRoles.CorpAdmin);
                users.AddRange(corpAdminUsers);
            }
            else if (CurrentUserRole == UserRoles.CorpAdmin)
            {
                // Corp Admin can see users they created
                var createdUsers = await _roleManagementService.GetUsersCreatedByAsync(currentUserId);
                users.AddRange(createdUsers);
            }

            Users = users.Select(u => new UserViewModel
            {
                Id = u.Id,
                FirstName = u.FirstName,
                LastName = u.LastName,
                Email = u.Email,
                Role = _roleManagementService.GetUserPrimaryRoleAsync(u.Id).Result,
                RoleDisplayName = UserRoles.GetDisplayName(_roleManagementService.GetUserPrimaryRoleAsync(u.Id).Result),
                CreatedAt = u.CreatedAt,
                IsActive = u.IsActive,
                CanManage = _roleManagementService.CanUserManageUserAsync(currentUserId, u.Id).Result
            }).ToList();

            return Page();
        }

        public async Task<IActionResult> OnPostToggleActiveAsync(string userId)
        {
            var currentUserId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(currentUserId))
            {
                return Challenge();
            }

            if (!await _roleManagementService.CanUserManageUserAsync(currentUserId, userId))
            {
                return Forbid();
            }

            var user = await _userManager.FindByIdAsync(userId);
            if (user == null)
            {
                return NotFound();
            }

            user.IsActive = !user.IsActive;
            user.UpdatedAt = DateTime.UtcNow;
            
            var result = await _userManager.UpdateAsync(user);
            if (result.Succeeded)
            {
                TempData["SuccessMessage"] = $"User {user.Email} has been {(user.IsActive ? "activated" : "deactivated")}.";
            }
            else
            {
                TempData["ErrorMessage"] = "Failed to update user status.";
            }

            return RedirectToPage();
        }
    }
}
