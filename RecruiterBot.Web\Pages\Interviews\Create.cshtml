@page
@model RecruiterBot.Web.Pages.Interviews.CreateModel
@{
    ViewData["Title"] = "Schedule New Interview";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-calendar-plus me-2"></i>
                        Schedule New Interview
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <!-- Job Description -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label asp-for="Input.JobDescription" class="form-label">
                                    <i class="bi bi-briefcase me-1"></i>
                                    Job Description
                                </label>
                                <textarea asp-for="Input.JobDescription" 
                                          class="form-control" 
                                          rows="6" 
                                          placeholder="Enter detailed job requirements, responsibilities, and qualifications..."></textarea>
                                <span asp-validation-for="Input.JobDescription" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Provide a comprehensive job description that will help the consultant understand the role requirements.
                                </small>
                            </div>
                        </div>

                        <!-- Interview Date and Time -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="Input.InterviewDateTime" class="form-label">
                                    <i class="bi bi-calendar-event me-1"></i>
                                    Interview Date & Time
                                </label>
                                <input asp-for="Input.InterviewDateTime" 
                                       type="datetime-local" 
                                       class="form-control" 
                                       min="@DateTime.Now.ToString("yyyy-MM-ddTHH:mm")" />
                                <span asp-validation-for="Input.InterviewDateTime" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Select the date and time for the interview (your local timezone).
                                </small>
                            </div>
                        </div>

                        <!-- Candidate Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="Input.CandidateId" class="form-label">
                                    <i class="bi bi-person me-1"></i>
                                    Candidate
                                </label>
                                <select asp-for="Input.CandidateId" 
                                        asp-items="Model.CandidateOptions" 
                                        class="form-select">
                                    <option value="">-- Select a candidate --</option>
                                </select>
                                <span asp-validation-for="Input.CandidateId" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Choose the candidate to be interviewed.
                                </small>
                            </div>
                        </div>

                        <!-- Consultant Assignment -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="Input.ConsultantId" class="form-label">
                                    <i class="bi bi-person-badge me-1"></i>
                                    Consultant
                                </label>
                                <select asp-for="Input.ConsultantId" 
                                        asp-items="Model.ConsultantOptions" 
                                        class="form-select">
                                    <option value="">-- Select a consultant --</option>
                                </select>
                                <span asp-validation-for="Input.ConsultantId" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Assign a consultant to conduct the interview.
                                </small>
                            </div>
                        </div>

                        <!-- LLM Model Selection -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label asp-for="Input.LLMModelId" class="form-label">
                                    <i class="bi bi-cpu me-1"></i>
                                    LLM Model
                                </label>
                                <select asp-for="Input.LLMModelId" 
                                        asp-items="Model.LLMModelOptions" 
                                        class="form-select">
                                    <option value="">-- Select an LLM model --</option>
                                </select>
                                <span asp-validation-for="Input.LLMModelId" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Choose the AI model to assist with the interview process.
                                </small>
                            </div>
                        </div>

                        <!-- Notes -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <label asp-for="Input.Notes" class="form-label">
                                    <i class="bi bi-sticky me-1"></i>
                                    Notes (Optional)
                                </label>
                                <textarea asp-for="Input.Notes" 
                                          class="form-control" 
                                          rows="3" 
                                          placeholder="Add any additional notes or special instructions..."></textarea>
                                <span asp-validation-for="Input.Notes" class="text-danger"></span>
                                <small class="form-text text-muted">
                                    Optional notes or special instructions for the consultant.
                                </small>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary" id="submitBtn">
                                        <span class="spinner-border spinner-border-sm d-none" role="status" aria-hidden="true"></span>
                                        <i class="bi bi-calendar-plus me-1"></i>
                                        Schedule Interview
                                    </button>
                                    <a href="/Interviews" class="btn btn-secondary">
                                        <i class="bi bi-arrow-left me-1"></i>
                                        Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const submitBtn = document.getElementById('submitBtn');
            const spinner = submitBtn.querySelector('.spinner-border');
            
            form.addEventListener('submit', function(e) {
                // Check if form is valid before showing loading state
                if (form.checkValidity()) {
                    submitBtn.disabled = true;
                    spinner.classList.remove('d-none');
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>Scheduling...';
                }
            });
        });
    </script>
}
