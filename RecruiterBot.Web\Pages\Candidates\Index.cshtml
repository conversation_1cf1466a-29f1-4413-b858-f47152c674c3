@page
@model RecruiterBot.Web.Pages.Candidates.IndexModel
@{
    ViewData["Title"] = "Candidates";
}

@* Add CSRF token for AJAX requests *@
<form style="display: none;">
    @Html.AntiForgeryToken()
</form>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-people me-2"></i>
                        Candidates
                        <span class="badge bg-secondary ms-2">@Model.TotalCount</span>
                    </h3>
                    <div class="card-tools">
                        <div class="btn-group me-2" role="group">
                            <input type="radio" class="btn-check" name="statusFilter" id="activeOnly" value="active" checked>
                            <label class="btn btn-outline-primary btn-sm" for="activeOnly">Active Only</label>

                            <input type="radio" class="btn-check" name="statusFilter" id="showAll" value="all">
                            <label class="btn btn-outline-secondary btn-sm" for="showAll">Show All</label>
                        </div>
                        <a asp-page="./Create" class="btn btn-primary btn-sm">
                            <i class="bi bi-person-plus me-2"></i>
                            Add Candidate
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (!string.IsNullOrEmpty(TempData["SuccessMessage"] as string))
                    {
                        <div class="alert alert-success alert-dismissible fade show">
                            <i class="bi bi-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(TempData["WarningMessage"] as string))
                    {
                        <div class="alert alert-warning alert-dismissible fade show">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            @TempData["WarningMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (!string.IsNullOrEmpty(TempData["ErrorMessage"] as string))
                    {
                        <div class="alert alert-danger alert-dismissible fade show">
                            <i class="bi bi-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    }

                    @if (Model.Candidates.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Email</th>
                                        <th>Current Position</th>
                                        <th>Skills</th>
                                        <th>Parse Status</th>
                                        <th>Status</th>
                                        <th>Uploaded</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var candidate in Model.Candidates)
                                    {
                                        <tr>
                                            <td>
                                                <strong>@candidate.FullName</strong>
                                                @if (!string.IsNullOrEmpty(candidate.Phone))
                                                {
                                                    <br><small class="text-muted">@candidate.Phone</small>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(candidate.Email))
                                                {
                                                    <a href="mailto:@candidate.Email">@candidate.Email</a>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(candidate.CurrentTitle))
                                                {
                                                    <strong>@candidate.CurrentTitle</strong>
                                                    @if (!string.IsNullOrEmpty(candidate.CurrentCompany))
                                                    {
                                                        <br><small class="text-muted">@candidate.CurrentCompany</small>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @if (candidate.Skills != null && candidate.Skills.Any())
                                                {
                                                    var displaySkills = candidate.Skills.Take(3).ToList();
                                                    @foreach (var skill in displaySkills)
                                                    {
                                                        <span class="badge bg-secondary me-1">@skill</span>
                                                    }
                                                    @if (candidate.Skills.Count > 3)
                                                    {
                                                        <span class="badge bg-light text-dark">+@(candidate.Skills.Count - 3) more</span>
                                                    }
                                                }
                                                else
                                                {
                                                    <span class="text-muted">-</span>
                                                }
                                            </td>
                                            <td>
                                                @switch (candidate.ParsingStatus)
                                                {
                                                    case RecruiterBot.Core.Models.ParsingStatus.Completed:
                                                        <span class="badge bg-success">
                                                            <i class="bi bi-check-circle me-1"></i>
                                                            Parsed
                                                        </span>
                                                        break;
                                                    case RecruiterBot.Core.Models.ParsingStatus.Failed:
                                                        <span class="badge bg-warning text-dark">
                                                            <i class="bi bi-exclamation-triangle me-1"></i>
                                                            Manual
                                                        </span>
                                                        break;
                                                    case RecruiterBot.Core.Models.ParsingStatus.InProgress:
                                                        <span class="badge bg-info">
                                                            <i class="spinner-border spinner-border-sm me-1"></i>
                                                            Processing
                                                        </span>
                                                        break;
                                                    case RecruiterBot.Core.Models.ParsingStatus.Manual:
                                                        <span class="badge bg-primary">
                                                            <i class="bi bi-pencil me-1"></i>
                                                            Manual Entry
                                                        </span>
                                                        break;
                                                    default:
                                                        <span class="badge bg-secondary">
                                                            <i class="bi bi-clock me-1"></i>
                                                            Pending
                                                        </span>
                                                        break;
                                                }
                                            </td>
                                            <td>
                                                @if (candidate.IsActive)
                                                {
                                                    <button type="button" class="btn btn-outline-warning btn-sm"
                                                            onclick="showDeactivateModal('@candidate.Id', '@Html.Raw(candidate.FullName.Replace("'", "\\'"))')"
                                                            title="Deactivate Candidate">
                                                        <i class="bi bi-pause-circle me-1"></i>
                                                        Deactivate
                                                    </button>
                                                }
                                                else
                                                {
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                            onclick="showActivateModal('@candidate.Id', '@Html.Raw(candidate.FullName.Replace("'", "\\'"))')"
                                                            title="Activate Candidate">
                                                        <i class="bi bi-play-circle me-1"></i>
                                                        Activate
                                                    </button>
                                                }
                                            </td>
                                            <td>
                                                <small class="text-muted">
                                                    @candidate.CreatedDateUtc.ToString("MMM dd, yyyy")
                                                    <br>@candidate.CreatedDateUtc.ToString("HH:mm")
                                                </small>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a asp-page="./Details" asp-route-id="@candidate.Id"
                                                       class="btn btn-outline-info" title="View Details">
                                                        <i class="bi bi-eye"></i>
                                                    </a>
                                                    <a asp-page="./Edit" asp-route-id="@candidate.Id"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                    @if (!string.IsNullOrEmpty(candidate.ResumeFilePath))
                                                    {
                                                        <a href="@candidate.ResumeFilePath" target="_blank"
                                                           class="btn btn-outline-secondary" title="View Resume">
                                                            <i class="bi bi-file-earmark-pdf"></i>
                                                        </a>
                                                    }
                                                    <a asp-page="./Delete" asp-route-id="@candidate.Id"
                                                       class="btn btn-outline-danger" title="Delete">
                                                        <i class="bi bi-trash"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        @if (Model.TotalCount > Model.Candidates.Count)
                        {
                            <div class="row mt-3">
                                <div class="col-12">
                                    <nav aria-label="Candidates pagination">
                                        <ul class="pagination justify-content-center">
                                            @if (Model.CurrentPage > 1)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" asp-page="./Index" asp-route-page="@(Model.CurrentPage - 1)" asp-route-includeInactive="@Model.IncludeInactive">Previous</a>
                                                </li>
                                            }

                                            @for (int i = Math.Max(1, Model.CurrentPage - 2); i <= Math.Min(Model.TotalPages, Model.CurrentPage + 2); i++)
                                            {
                                                <li class="page-item @(i == Model.CurrentPage ? "active" : "")">
                                                    <a class="page-link" asp-page="./Index" asp-route-page="@i" asp-route-includeInactive="@Model.IncludeInactive">@i</a>
                                                </li>
                                            }

                                            @if (Model.CurrentPage < Model.TotalPages)
                                            {
                                                <li class="page-item">
                                                    <a class="page-link" asp-page="./Index" asp-route-page="@(Model.CurrentPage + 1)" asp-route-includeInactive="@Model.IncludeInactive">Next</a>
                                                </li>
                                            }
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        }
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-people" style="font-size: 3rem;" class="text-muted mb-3"></i>
                            <h5 class="text-muted">No candidates found</h5>
                            <p class="text-muted">Add your first candidate to get started!</p>
                            <a asp-page="./Create" class="btn btn-primary">
                                <i class="bi bi-person-plus me-2"></i>
                                Add Candidate
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Activate Candidate Modal -->
<div class="modal fade" id="activateModal" tabindex="-1" aria-labelledby="activateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="activateModalLabel">
                    <i class="bi bi-play-circle me-2"></i>
                    Activate Candidate
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-question-circle-fill text-success me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-1">Are you sure you want to activate this candidate?</p>
                        <strong id="activateCandidateName"></strong>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>
                    This candidate will be visible in the main candidate list and available for recruitment activities.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Cancel
                </button>
                <button type="button" class="btn btn-success" id="confirmActivateBtn">
                    <i class="bi bi-play-circle me-1"></i>
                    Yes, Activate
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Deactivate Candidate Modal -->
<div class="modal fade" id="deactivateModal" tabindex="-1" aria-labelledby="deactivateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-warning text-dark">
                <h5 class="modal-title" id="deactivateModalLabel">
                    <i class="bi bi-pause-circle me-2"></i>
                    Deactivate Candidate
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center mb-3">
                    <i class="bi bi-exclamation-triangle-fill text-warning me-3" style="font-size: 2rem;"></i>
                    <div>
                        <p class="mb-1">Are you sure you want to deactivate this candidate?</p>
                        <strong id="deactivateCandidateName"></strong>
                    </div>
                </div>
                <div class="alert alert-warning">
                    <i class="bi bi-info-circle me-2"></i>
                    This candidate will be hidden from the main candidate list but can be reactivated later if needed.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>
                    Cancel
                </button>
                <button type="button" class="btn btn-warning" id="confirmDeactivateBtn">
                    <i class="bi bi-pause-circle me-1"></i>
                    Yes, Deactivate
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Message Modal -->
<div class="modal fade" id="messageModal" tabindex="-1" aria-labelledby="messageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" id="messageModalHeader">
                <h5 class="modal-title" id="messageModalLabel"></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="d-flex align-items-center">
                    <i id="messageModalIcon" class="me-3" style="font-size: 2rem;"></i>
                    <div id="messageModalText"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        var currentCandidateId = null;

        function showActivateModal(candidateId, candidateName) {
            currentCandidateId = candidateId;
            $('#activateCandidateName').text(candidateName);
            $('#activateModal').modal('show');
        }

        function showDeactivateModal(candidateId, candidateName) {
            currentCandidateId = candidateId;
            $('#deactivateCandidateName').text(candidateName);
            $('#deactivateModal').modal('show');
        }

        function showMessageModal(isSuccess, title, message) {
            var $header = $('#messageModalHeader');
            var $icon = $('#messageModalIcon');
            var $title = $('#messageModalLabel');
            var $text = $('#messageModalText');

            if (isSuccess) {
                $header.removeClass('bg-danger text-white').addClass('bg-success text-white');
                $icon.removeClass('bi-x-circle-fill text-danger').addClass('bi-check-circle-fill text-success');
            } else {
                $header.removeClass('bg-success text-white').addClass('bg-danger text-white');
                $icon.removeClass('bi-check-circle-fill text-success').addClass('bi-x-circle-fill text-danger');
            }

            $title.text(title);
            $text.text(message);
            $('#messageModal').modal('show');
        }

        function performActivation(candidateId) {
            var $btn = $('#confirmActivateBtn');
            var originalHtml = $btn.html();

            // Show loading state
            $btn.prop('disabled', true);
            $btn.html('<i class="spinner-border spinner-border-sm me-1"></i>Activating...');

            // Get CSRF token
            var token = $('input[name="__RequestVerificationToken"]').val();

            // Make AJAX call
            $.ajax({
                url: '/api/candidates/activate',
                type: 'POST',
                headers: {
                    'RequestVerificationToken': token
                },
                data: {
                    candidateId: candidateId,
                    __RequestVerificationToken: token
                },
                success: function(response) {
                    $('#activateModal').modal('hide');
                    if (response.success) {
                        showMessageModal(true, 'Success', response.message || 'Candidate activated successfully.');
                        // Reload page after modal is closed
                        $('#messageModal').on('hidden.bs.modal', function() {
                            location.reload();
                        });
                    } else {
                        showMessageModal(false, 'Error', response.message || 'Failed to activate candidate.');
                    }
                },
                error: function(xhr) {
                    $('#activateModal').modal('hide');
                    var errorMessage = 'An error occurred while activating the candidate.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showMessageModal(false, 'Error', errorMessage);
                },
                complete: function() {
                    // Reset button state
                    $btn.prop('disabled', false);
                    $btn.html(originalHtml);
                }
            });
        }

        function performDeactivation(candidateId) {
            var $btn = $('#confirmDeactivateBtn');
            var originalHtml = $btn.html();

            // Show loading state
            $btn.prop('disabled', true);
            $btn.html('<i class="spinner-border spinner-border-sm me-1"></i>Deactivating...');

            // Get CSRF token
            var token = $('input[name="__RequestVerificationToken"]').val();

            // Make AJAX call
            $.ajax({
                url: '/api/candidates/deactivate',
                type: 'POST',
                headers: {
                    'RequestVerificationToken': token
                },
                data: {
                    candidateId: candidateId,
                    __RequestVerificationToken: token
                },
                success: function(response) {
                    $('#deactivateModal').modal('hide');
                    if (response.success) {
                        showMessageModal(true, 'Success', response.message || 'Candidate deactivated successfully.');
                        // Reload page after modal is closed
                        $('#messageModal').on('hidden.bs.modal', function() {
                            location.reload();
                        });
                    } else {
                        showMessageModal(false, 'Error', response.message || 'Failed to deactivate candidate.');
                    }
                },
                error: function(xhr) {
                    $('#deactivateModal').modal('hide');
                    var errorMessage = 'An error occurred while deactivating the candidate.';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMessage = xhr.responseJSON.message;
                    }
                    showMessageModal(false, 'Error', errorMessage);
                },
                complete: function() {
                    // Reset button state
                    $btn.prop('disabled', false);
                    $btn.html(originalHtml);
                }
            });
        }

        $(document).ready(function() {
            // Handle modal button clicks
            $('#confirmActivateBtn').on('click', function() {
                if (currentCandidateId) {
                    performActivation(currentCandidateId);
                }
            });

            $('#confirmDeactivateBtn').on('click', function() {
                if (currentCandidateId) {
                    performDeactivation(currentCandidateId);
                }
            });

            // Reset currentCandidateId when modals are hidden
            $('#activateModal, #deactivateModal').on('hidden.bs.modal', function() {
                currentCandidateId = null;
            });

            // Handle status filter changes
            $('input[name="statusFilter"]').change(function() {
                var filterValue = $(this).val();
                var currentUrl = new URL(window.location);

                if (filterValue === 'all') {
                    currentUrl.searchParams.set('includeInactive', 'true');
                } else {
                    currentUrl.searchParams.delete('includeInactive');
                }

                // Reset to first page when changing filter
                currentUrl.searchParams.delete('page');

                window.location.href = currentUrl.toString();
            });

            // Set the correct filter based on URL parameters
            var urlParams = new URLSearchParams(window.location.search);
            if (urlParams.get('includeInactive') === 'true') {
                $('#showAll').prop('checked', true);
            } else {
                $('#activeOnly').prop('checked', true);
            }
        });
    </script>
}
