@model string
@if (!string.IsNullOrEmpty(Model))
{
    var alertClass = Model.Contains("Error") || Model.Contains("error") ? "alert-danger" : "alert-success";
    <div class="alert @alertClass d-flex align-items-center mb-4">
        @if (alertClass == "alert-success")
        {
            <i class="bi bi-check-circle-fill me-2"></i>
        }
        else
        {
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
        }
        @Model
    </div>
}
