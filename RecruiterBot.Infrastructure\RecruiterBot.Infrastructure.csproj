﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <EnableNETAnalyzers>true</EnableNETAnalyzers>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity" Version="2.3.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.5" />
    <PackageReference Include="MongoDB.Bson" Version="3.4.0" />
    <PackageReference Include="MongoDB.Driver" Version="3.4.0" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.5" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.5" />
    <PackageReference Include="OllamaSharp" Version="3.0.8" />
    <PackageReference Include="Microsoft.Extensions.AI.OpenAI" Version="9.0.1-preview.1.24570.5" />
    <PackageReference Include="Google.Cloud.Storage.V1" Version="4.10.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\RecruiterBot.Core\RecruiterBot.Core.csproj" />
  </ItemGroup>

</Project>
