using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Models;
using RecruiterBot.Core.Constants;
using RecruiterBot.Infrastructure.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Candidates
{
    [Authorize(Policy = RecruiterBot.Core.Constants.AuthorizationPolicies.AllRoles)]
    public class IndexModel : PageModel
    {
        private readonly ICandidateService _candidateService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<IndexModel> _logger;
        private const int PageSize = 20;

        public IndexModel(
            ICandidateService candidateService,
            IRoleManagementService roleManagementService,
            ILogger<IndexModel> logger)
        {
            _candidateService = candidateService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        public List<Candidate> Candidates { get; set; } = new List<Candidate>();
        public long TotalCount { get; set; }
        public int CurrentPage { get; set; } = 1;
        public int TotalPages { get; set; }
        public bool IncludeInactive { get; set; }

        public async Task<IActionResult> OnGetAsync(int page = 1, bool includeInactive = false)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                CurrentPage = Math.Max(1, page);
                IncludeInactive = includeInactive;
                var skip = (CurrentPage - 1) * PageSize;

                var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);

                // Admin users should not access Candidates
                if (userRole == UserRoles.Admin)
                {
                    TempData["ErrorMessage"] = "Access denied. Administrators cannot access Candidates.";
                    return RedirectToPage("/Dashboard");
                }

                if (userRole == UserRoles.CorpAdmin)
                {
                    // Corp Admin can see all team candidates
                    var candidatesTask = _candidateService.GetTeamCandidatesAsync(userId, skip, PageSize, includeInactive);
                    var countTask = _candidateService.GetTeamCandidateCountAsync(userId, includeInactive);

                    await Task.WhenAll(candidatesTask, countTask);

                    Candidates = candidatesTask.Result;
                    TotalCount = countTask.Result;
                }
                else if (userRole == UserRoles.User)
                {
                    // Standard users can only see their own candidates
                    var candidatesTask = _candidateService.GetUserOwnCandidatesAsync(userId, skip, PageSize, includeInactive);
                    var countTask = _candidateService.GetUserOwnCandidateCountAsync(userId, includeInactive);

                    await Task.WhenAll(candidatesTask, countTask);

                    Candidates = candidatesTask.Result;
                    TotalCount = countTask.Result;
                }
                else
                {
                    // Other roles see their own candidates
                    var candidatesTask = _candidateService.GetCandidatesAsync(userId, skip, PageSize, includeInactive);
                    var countTask = _candidateService.GetCandidateCountAsync(userId, includeInactive);

                    await Task.WhenAll(candidatesTask, countTask);

                    Candidates = candidatesTask.Result;
                    TotalCount = countTask.Result;
                }

                TotalPages = (int)Math.Ceiling((double)TotalCount / PageSize);
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidates for user {UserId}", userId);
                TempData["ErrorMessage"] = "Error loading candidates.";
                return Page();
            }
        }
    }
}
