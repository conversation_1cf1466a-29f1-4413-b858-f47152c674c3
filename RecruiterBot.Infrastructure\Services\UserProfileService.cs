using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Processing;
using SixLabors.ImageSharp.Formats.Jpeg;
using SixLabors.ImageSharp.Formats.Png;
using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public class UserProfileService
    {
        private readonly UserManager<User> _userManager;
        private readonly IWebHostEnvironment _environment;
        private readonly ILogger<UserProfileService> _logger;

        public UserProfileService(
            UserManager<User> userManager,
            IWebHostEnvironment environment,
            ILogger<UserProfileService> logger)
        {
            _userManager = userManager ?? throw new ArgumentNullException(nameof(userManager));
            _environment = environment ?? throw new ArgumentNullException(nameof(environment));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<string> GetUserAvatarUrlAsync(string userId)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                return !string.IsNullOrEmpty(user?.ProfileImageUrl) 
                    ? user.ProfileImageUrl 
                    : "/images/default-avatar.svg";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user avatar URL for user {UserId}", userId);
                return "/images/default-avatar.svg";
            }
        }

        public async Task<(bool Success, string ErrorMessage)> UpdateProfileAsync(string userId, string firstName, string lastName, string email, IFormFile profileImage = null)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    return (false, "User not found");

                // Update basic user info
                user.FirstName = firstName?.Trim();
                user.LastName = lastName?.Trim();
                user.Email = email?.Trim();
                user.UserName = email?.Trim();
                user.NormalizedEmail = email?.ToUpper();
                user.NormalizedUserName = email?.ToUpper();
                user.UpdatedAt = DateTime.UtcNow;

                // Handle profile image upload
                if (profileImage != null && profileImage.Length > 0)
                {
                    var (success, error, imagePath) = await ProcessAndSaveImageAsync(profileImage, user.Id);
                    if (!success)
                        return (false, error);

                    // Delete old profile image if exists and it's not the default one
                    if (!string.IsNullOrEmpty(user.ProfileImageUrl) && 
                        !user.ProfileImageUrl.Equals("/images/default-avatar.svg", StringComparison.OrdinalIgnoreCase) &&
                        user.ProfileImageUrl.StartsWith("/uploads/avatars/"))
                    {
                        try
                        {
                            var oldFilePath = Path.Combine(_environment.WebRootPath, user.ProfileImageUrl.TrimStart('/'));
                            if (File.Exists(oldFilePath))
                            {
                                File.Delete(oldFilePath);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error deleting old profile image for user {UserId}", user.Id);
                            // Continue even if deletion of old image fails
                        }
                    }

                    user.ProfileImageUrl = imagePath;
                }

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("Failed to update user {UserId}: {Errors}", user.Id, errors);
                    return (false, $"Failed to update profile: {errors}");
                }

                return (true, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating profile for user {UserId}", userId);
                return (false, "An error occurred while updating the profile. Please try again later.");
            }
        }

        public async Task<(bool Success, string ErrorMessage)> ChangePasswordAsync(string userId, string currentPassword, string newPassword)
        {
            try
            {
                var user = await _userManager.FindByIdAsync(userId);
                if (user == null)
                    return (false, "User not found");

                var result = await _userManager.ChangePasswordAsync(user, currentPassword, newPassword);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    _logger.LogWarning("Failed to change password for user {UserId}: {Errors}", user.Id, errors);
                    return (false, $"Failed to change password: {errors}");
                }

                return (true, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error changing password for user {UserId}", userId);
                return (false, "An error occurred while changing the password. Please try again later.");
            }
        }

        private async Task<(bool Success, string Error, string ImagePath)> ProcessAndSaveImageAsync(IFormFile imageFile, string userId)
        {
            try
            {
                // Validate file type
                var allowedExtensions = new[] { ".jpg", ".jpeg", ".png", ".gif" };
                var extension = Path.GetExtension(imageFile.FileName).ToLowerInvariant();
                if (string.IsNullOrEmpty(extension) || !allowedExtensions.Contains(extension))
                {
                    return (false, "Invalid file type. Only JPG, JPEG, PNG, and GIF are allowed.", null);
                }

                // Validate file size (max 5MB)
                const int maxFileSize = 5 * 1024 * 1024; // 5MB
                if (imageFile.Length > maxFileSize)
                {
                    return (false, "Image size must be less than 5MB.", null);
                }

                // Create uploads directory if it doesn't exist
                var uploadsFolder = Path.Combine(_environment.WebRootPath, "uploads", "avatars");
                if (!Directory.Exists(uploadsFolder))
                {
                    Directory.CreateDirectory(uploadsFolder);
                }

                // Generate unique filename
                var uniqueFileName = $"{userId}_{DateTime.UtcNow:yyyyMMddHHmmss}{extension}";
                var filePath = Path.Combine(uploadsFolder, uniqueFileName);

                // Process and save the image
                using (var image = await Image.LoadAsync(imageFile.OpenReadStream()))
                {
                    // Resize the image to a maximum of 400x400 while maintaining aspect ratio
                    var options = new ResizeOptions
                    {
                        Size = new Size(400, 400),
                        Mode = ResizeMode.Max
                    };

                    image.Mutate(x => x.Resize(options));

                    // Save as JPEG with 90% quality
                    await using var fileStream = new FileStream(filePath, FileMode.Create);
                    
                    // Determine the correct encoder based on file extension
                    if (extension == ".png")
                    {
                        await image.SaveAsPngAsync(fileStream, new PngEncoder { CompressionLevel = PngCompressionLevel.BestCompression });
                    }
                    else if (extension == ".gif")
                    {
                        // For GIFs, we'll save as is to preserve animation
                        await imageFile.CopyToAsync(fileStream);
                    }
                    else
                    {
                        // Default to JPEG for .jpg and .jpeg
                        await image.SaveAsJpegAsync(fileStream, new JpegEncoder { Quality = 90 });
                    }
                }

                return (true, null, $"/uploads/avatars/{uniqueFileName}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing profile image for user {UserId}", userId);
                return (false, "An error occurred while processing the image. Please try again.", null);
            }
        }
    }
}
