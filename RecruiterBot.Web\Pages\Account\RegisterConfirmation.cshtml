@page
@model RecruiterBot.Web.Pages.Account.RegisterConfirmationModel
@{
    ViewData["Title"] = "Registration Successful";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                        <h2 class="mt-3 text-success">Registration Successful!</h2>
                        <p class="text-muted">Welcome to RecruiterBot! Your account has been created successfully.</p>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-envelope me-2"></i>
                        <strong>Account Setup Email Sent</strong>
                        <p class="mb-0 mt-2">
                            We've sent account setup instructions to <strong>@Model.Email</strong>.
                            Please check your email for your temporary password and setup link.
                        </p>
                    </div>

                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="bi bi-info-circle me-2"></i>
                                What's next?
                            </h6>
                            <ol class="mb-0">
                                <li>Check your email inbox (and spam folder)</li>
                                <li>Use the temporary password to log in</li>
                                <li>You'll be automatically redirected to set your permanent password</li>
                                <li>Complete the account setup process</li>
                            </ol>
                        </div>
                    </div>

                    <div class="text-center mt-4">
                        <a asp-page="./Login" class="btn btn-primary">
                            <i class="bi bi-box-arrow-in-right me-2"></i>
                            Go to Login
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Didn't receive the email?
                            <a asp-page="./ResendEmailConfirmation" class="text-decoration-none">Resend setup email</a>
                        </small>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            The setup link will expire in 24 hours for security reasons.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
