@page
@model RecruiterBot.Web.Pages.BotConfigurations.IndexModel
@{
    ViewData["Title"] = "Bot Configurations";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">AI Recruiters</h1>
        <div class="btn-group">
            <a asp-page="./JobRuns" class="btn btn-outline-primary">
                <i class="bi bi-clock-history me-2"></i>View All Job Runs
            </a>
            <a asp-page="Create" class="btn btn-primary">
                <i class="bi bi-plus-lg me-2"></i>New Configuration
            </a>
        </div>
    </div>
    
    <partial name="_Alerts/_SuccessAlert" />

    <div class="card shadow mb-4">
        <div class="card-body">
            @if (Model.Configurations.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Query</th>
                                <th>Candidate Search</th>
                                <th>Status</th>
                                <th>Last Run</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var config in Model.Configurations)
                            {
                                <tr>
                                    <td>@config.Query</td>
                                    <td>@config.CandidateSearch</td>
                                    <td>
                                        @if (config.IsActive)
                                        {
                                            <span class="badge bg-success">Active</span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-secondary">Inactive</span>
                                        }
                                    </td>
                                    <td>@(config.LastRunUtc?.ToString("g") ?? "Never")</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a asp-page="./Edit" asp-route-id="@config.Id" class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="bi bi-pencil-square"></i>
                                            </a>
                                            <a asp-page="./Details" asp-route-id="@config.Id" class="btn btn-sm btn-outline-info" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="modal" data-bs-target="#deleteModal"
                                                    data-config-id="@config.Id" data-config-name="@config.Query">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-robot text-gray-300 mb-4" style="font-size: 3rem;"></i>
                    <h5>No bot configurations found</h5>
                    <p class="text-muted">Get started by creating a new bot configuration.</p>
                    <a asp-page="Create" class="btn btn-primary mt-3">
                        <i class="bi bi-plus-lg me-2"></i>Create Configuration
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the configuration "<span id="configName"></span>"?</p>
                <p class="text-muted">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="post" asp-page-handler="Delete">
                    <input type="hidden" name="id" id="configId" />
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var deleteModal = document.getElementById('deleteModal');
            
            deleteModal.addEventListener('show.bs.modal', function (event) {
                var button = event.relatedTarget;
                var configId = button.getAttribute('data-config-id');
                var configName = button.getAttribute('data-config-name');
                
                document.getElementById('configId').value = configId;
                document.getElementById('configName').textContent = configName;
                document.getElementById('deleteForm').action = '/BotConfigurations/Delete?id=' + configId;
            });
        });
    </script>
}
