using Microsoft.AspNetCore.Http;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IFileStorageService
    {
        /// <summary>
        /// Uploads a file to the storage service
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="folder">The folder/prefix to store the file under</param>
        /// <returns>The public URL or path to access the uploaded file</returns>
        Task<string> UploadFileAsync(IFormFile file, string folder);

        /// <summary>
        /// Uploads a file from a local path to the storage service
        /// </summary>
        /// <param name="localFilePath">The local file path</param>
        /// <param name="fileName">The desired file name in storage</param>
        /// <param name="folder">The folder/prefix to store the file under</param>
        /// <returns>The public URL or path to access the uploaded file</returns>
        Task<string> UploadFileAsync(string localFilePath, string fileName, string folder);

        /// <summary>
        /// Deletes a file from the storage service
        /// </summary>
        /// <param name="fileUrl">The file URL or path to delete</param>
        /// <returns>True if the file was deleted successfully</returns>
        Task<bool> DeleteFileAsync(string fileUrl);

        /// <summary>
        /// Gets a signed URL for temporary access to a private file
        /// </summary>
        /// <param name="fileUrl">The file URL or path</param>
        /// <param name="expirationMinutes">How long the URL should be valid (default: 60 minutes)</param>
        /// <returns>A signed URL for temporary access</returns>
        Task<string> GetSignedUrlAsync(string fileUrl, int expirationMinutes = 60);

        /// <summary>
        /// Checks if a file exists in the storage service
        /// </summary>
        /// <param name="fileUrl">The file URL or path to check</param>
        /// <returns>True if the file exists</returns>
        Task<bool> FileExistsAsync(string fileUrl);
    }
}
