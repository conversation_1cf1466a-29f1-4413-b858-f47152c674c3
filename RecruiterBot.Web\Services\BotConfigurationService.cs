using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using RecruiterBot.Web.Models;
using RecruiterBot.Core.Constants;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Services
{
    public class BotConfigurationService : IBotConfigurationService
    {
        private readonly IMongoCollection<BotConfiguration> _configurations;
        private readonly IEncryptionService _encryptionService;
        private readonly ILogger<BotConfigurationService> _logger;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ICandidateService _candidateService;
        private const string CollectionName = "botconfigurations";
        private const string JobRunCollectionName = "linkedin_job_run";

        public BotConfigurationService(
            IOptions<MongoDBSettings> settings,
            IEncryptionService encryptionService,
            ILogger<BotConfigurationService> logger,
            IRoleManagementService roleManagementService,
            ICandidateService candidateService)
        {
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _roleManagementService = roleManagementService ?? throw new ArgumentNullException(nameof(roleManagementService));
            _candidateService = candidateService ?? throw new ArgumentNullException(nameof(candidateService));
            if (settings?.Value == null)
                throw new ArgumentNullException(nameof(settings));
                
            var client = new MongoClient(settings.Value.ConnectionString);
            var database = client.GetDatabase(settings.Value.DatabaseName);
            _configurations = database.GetCollection<BotConfiguration>(CollectionName);
            
            // Create indexes
            CreateIndexes();
        }

        private void CreateIndexes()
        {
            // Index for user-specific queries
            var userIndexKeys = Builders<BotConfiguration>.IndexKeys.Ascending(x => x.CreatedBy);
            var userIndexOptions = new CreateIndexOptions { Background = true };
            var userIndexModel = new CreateIndexModel<BotConfiguration>(userIndexKeys, userIndexOptions);
            _configurations.Indexes.CreateOne(userIndexModel);

            // Compound index for ID and CreatedBy for faster lookups
            var compoundIndexKeys = Builders<BotConfiguration>
                .IndexKeys
                .Ascending(x => x.Id)
                .Ascending(x => x.CreatedBy);
            var compoundIndexOptions = new CreateIndexOptions { Background = true };
            var compoundIndexModel = new CreateIndexModel<BotConfiguration>(compoundIndexKeys, compoundIndexOptions);
            _configurations.Indexes.CreateOne(compoundIndexModel);

            // Index for active configurations
            var activeIndexKeys = Builders<BotConfiguration>.IndexKeys.Ascending(x => x.IsActive);
            var activeIndexOptions = new CreateIndexOptions { Background = true };
            var activeIndexModel = new CreateIndexModel<BotConfiguration>(activeIndexKeys, activeIndexOptions);
            _configurations.Indexes.CreateOne(activeIndexModel);
        }

        public async Task<IEnumerable<BotConfiguration>> GetAllConfigurationsAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // Check if user is Admin - Admin users don't have access to Bot Configurations
            var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);
            if (userRole == UserRoles.Admin)
            {
                _logger.LogWarning("Admin user {UserId} attempted to access Bot Configurations", userId);
                return Enumerable.Empty<BotConfiguration>();
            }

            // Get the Corp Admin ID for this user
            var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

            var configs = await _configurations
                .Find(c => c.Tenant == corpAdminId)
                .SortByDescending(c => c.CreatedDateUtc)
                .ToListAsync();

            // Decrypt credentials for each config
            foreach (var config in configs)
            {
                try
                {
                    config.DecryptCredentials(_encryptionService);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error decrypting credentials for configuration {ConfigId}", config.Id);
                    // Continue without decrypted credentials
                }
            }

            return configs;
        }

        public async Task<BotConfiguration> GetConfigurationAsync(string id, string userId)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Configuration ID cannot be empty", nameof(id));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // Get the Corp Admin ID for this user
            var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

            var config = await _configurations
                .Find(x => x.Id == id && x.Tenant == corpAdminId)
                .FirstOrDefaultAsync();
                
            if (config != null)
            {
                try
                {
                    config.DecryptCredentials(_encryptionService);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error decrypting credentials for configuration {ConfigId}", id);
                    // Continue without decrypted credentials
                }
            }
            
            return config;
        }

        public async Task<BotConfiguration> CreateConfigurationAsync(BotConfiguration configuration, string userId)
        {
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // Get the Corp Admin ID for this user
            var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

            // Encrypt sensitive data before saving
            configuration.EncryptCredentials(_encryptionService);

            configuration.CreatedBy = userId;
            configuration.CreatedDateUtc = DateTime.UtcNow;
            configuration.Tenant = corpAdminId; // Set tenant to Corp Admin ID

            await _configurations.InsertOneAsync(configuration);
            return configuration;
        }

        public async Task<bool> UpdateConfigurationAsync(string id, BotConfiguration configuration, string userId)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Configuration ID cannot be empty", nameof(id));
                
            if (configuration == null)
                throw new ArgumentNullException(nameof(configuration));
                
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));
                
            // Ensure we're only updating the user's own configuration
            var existingConfig = await GetConfigurationAsync(id, userId);
            if (existingConfig == null)
                return false;
                
            // Encrypt sensitive data before saving
            configuration.EncryptCredentials(_encryptionService);
            
            // Preserve created date and created by
            configuration.CreatedBy = existingConfig.CreatedBy;
            configuration.CreatedDateUtc = existingConfig.CreatedDateUtc;
            configuration.Tenant = existingConfig.Tenant; // Preserve original tenant (Corp Admin ID)
            
            // Set last updated timestamp
            configuration.LastRunUtc = DateTime.UtcNow;
            
            // Get the Corp Admin ID for this user to verify access
            var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

            var result = await _configurations.ReplaceOneAsync(
                c => c.Id == id && c.Tenant == corpAdminId,
                configuration,
                new ReplaceOptions { IsUpsert = false });
                
            return result.ModifiedCount > 0;
        }

        public async Task<bool> DeleteConfigurationAsync(string id, string userId)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Configuration ID cannot be empty", nameof(id));

            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // Get the Corp Admin ID for this user
            var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new InvalidOperationException($"Could not determine Corp Admin for user {userId}");

            // Get the configuration first to clean up candidate associations
            var config = await _configurations
                .Find(x => x.Id == id && x.Tenant == corpAdminId)
                .FirstOrDefaultAsync();

            if (config != null && config.AssignedCandidates?.Any() == true)
            {
                // Clean up candidate associations before deleting the bot
                await CleanupCandidateBotAssociationsAsync(config.AssignedCandidates, id, userId);
            }

            var result = await _configurations.DeleteOneAsync(
                config => config.Id == id && config.Tenant == corpAdminId);

            return result.DeletedCount > 0;
        }
        
        public async Task<bool> UpdateLastRunAsync(string id, string userId, DateTime lastRunUtc)
        {
            if (string.IsNullOrWhiteSpace(id))
                throw new ArgumentException("Configuration ID cannot be empty", nameof(id));
                
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));
            
            var update = Builders<BotConfiguration>.Update
                .Set(c => c.LastRunUtc, lastRunUtc);
                
            var result = await _configurations.UpdateOneAsync(
                c => c.Id == id && c.Tenant == userId,
                update);
                
            return result.ModifiedCount > 0;
        }
        
        public async Task<IEnumerable<BotConfiguration>> GetActiveConfigurationsAsync(string userId = null)
        {
            var filter = Builders<BotConfiguration>.Filter.Eq(x => x.IsActive, true);
            
            if (!string.IsNullOrWhiteSpace(userId))
            {
                filter &= Builders<BotConfiguration>.Filter.Eq(x => x.Tenant, userId);
            }

            var configs = await _configurations
                .Find(filter)
                .ToListAsync();
                
            // Decrypt credentials for each config
            foreach (var config in configs)
            {
                try
                {
                    config.DecryptCredentials(_encryptionService);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error decrypting credentials for configuration {ConfigId}", config.Id);
                    // Continue without decrypted credentials
                }
            }
            
            return configs;
        }

        public async Task<IEnumerable<JobRun>> GetJobRunsAsync(string botId, string userId)
        {
            if (string.IsNullOrWhiteSpace(botId))
                throw new ArgumentException("Bot ID cannot be empty", nameof(botId));
                
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // First verify the bot belongs to the user
            var bot = await _configurations
                .Find(x => x.Id == botId && x.Tenant == userId)
                .FirstOrDefaultAsync();
                
            if (bot == null)
            {
                _logger.LogWarning("Bot {BotId} not found or access denied for user {UserId}", botId, userId);
                return new List<JobRun>();
            }

            // Get the job runs collection
            var database = _configurations.Database;
            var jobRunsCollection = database.GetCollection<JobRun>(JobRunCollectionName);
            
            // Get job runs for this bot, sorted by run date (newest first)
            return await jobRunsCollection
                .Find(x => x.BotId == botId)
                .SortByDescending(x => x.RunDate)
                .ToListAsync();
        }

        public async Task<JobRun> GetJobRunAsync(string jobRunId, string userId)
        {
            if (string.IsNullOrWhiteSpace(jobRunId))
                throw new ArgumentException("Job run ID cannot be empty", nameof(jobRunId));
                
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            // Get the job runs collection
            var database = _configurations.Database;
            var jobRunsCollection = database.GetCollection<JobRun>(JobRunCollectionName);
            
            // Get the job run
            var jobRun = await jobRunsCollection
                .Find(x => x.Id == jobRunId)
                .FirstOrDefaultAsync();
                
            if (jobRun == null)
                return null;
                
            // Verify the user has access to the bot associated with this job run
            //var bot = await _configurations
            //    .Find(x => x.Id == jobRun.BotId && x.UserId == userId)
            //    .FirstOrDefaultAsync();
                
            //if (bot == null)
            //{
            //    _logger.LogWarning("Access denied for job run {JobRunId}. User {UserId} does not have access to bot {BotId}", 
            //        jobRunId, userId, jobRun.BotId);
            //    return null;
            //}
            
            // Load the bot configuration into the navigation property
            //jobRun.Bot = bot;
            return jobRun;
        }

        public async Task<Dictionary<BotConfiguration, List<JobRun>>> GetAllJobRunsGroupedByBotAsync(string userId)
        {
            if (string.IsNullOrWhiteSpace(userId))
                throw new ArgumentException("User ID cannot be empty", nameof(userId));

            var result = new Dictionary<BotConfiguration, List<JobRun>>();

            try
            {
                // Get all bot configurations for the user
                var botConfigurations = await GetAllConfigurationsAsync(userId);

                // Get the job runs collection
                var database = _configurations.Database;
                var jobRunsCollection = database.GetCollection<JobRun>(JobRunCollectionName);

                foreach (var bot in botConfigurations)
                {
                    // Get job runs for this bot
                    var jobRuns = await jobRunsCollection
                        .Find(x => x.BotId == bot.Id)
                        .SortByDescending(x => x.RunDate)
                        .ToListAsync();

                    result[bot] = jobRuns;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all job runs for user {UserId}", userId);
                throw;
            }
        }

        public async Task<IEnumerable<BotConfiguration>> GetTeamConfigurationsAsync(string corpAdminId)
        {
            if (string.IsNullOrWhiteSpace(corpAdminId))
                throw new ArgumentException("Corp Admin ID cannot be empty", nameof(corpAdminId));

            try
            {
                // Since tenant field now contains Corp Admin ID, simply query by Corp Admin ID
                var configurations = await _configurations
                    .Find(x => x.Tenant == corpAdminId)
                    .SortByDescending(x => x.CreatedDateUtc)
                    .ToListAsync();

                // Decrypt credentials for each configuration
                foreach (var config in configurations)
                {
                    try
                    {
                        config.DecryptCredentials(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting credentials for configuration {ConfigId}", config.Id);
                        // Continue without decrypted credentials
                    }
                }

                _logger.LogDebug("Retrieved {Count} team configurations for Corp Admin {CorpAdminId}",
                    configurations.Count, corpAdminId);
                return configurations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving team configurations for Corp Admin {CorpAdminId}", corpAdminId);
                throw;
            }
        }

        public async Task<IEnumerable<BotConfiguration>> GetConfigurationsForUsersAsync(IEnumerable<string> userIds)
        {
            if (userIds == null || !userIds.Any())
                return Enumerable.Empty<BotConfiguration>();

            try
            {
                var userIdList = userIds.ToList();
                var configurations = await _configurations
                    .Find(x => userIdList.Contains(x.Tenant))
                    .SortByDescending(x => x.CreatedDateUtc)
                    .ToListAsync();

                // Decrypt credentials for each configuration
                foreach (var config in configurations)
                {
                    try
                    {
                        config.DecryptCredentials(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting credentials for configuration {ConfigId}", config.Id);
                        // Continue without decrypted credentials
                    }
                }

                _logger.LogDebug("Retrieved {Count} configurations for {UserCount} users", configurations.Count, userIdList.Count);
                return configurations;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving configurations for users");
                throw;
            }
        }

        private async Task CleanupCandidateBotAssociationsAsync(List<string> candidateIds, string botId, string userId)
        {
            try
            {
                foreach (var candidateId in candidateIds)
                {
                    var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                    if (candidate != null)
                    {
                        var botIndex = candidate.AssignedBotIds.IndexOf(botId);
                        if (botIndex >= 0)
                        {
                            candidate.AssignedBotIds.RemoveAt(botIndex);
                            if (botIndex < candidate.AssignedBotQueries.Count)
                            {
                                candidate.AssignedBotQueries.RemoveAt(botIndex);
                            }
                            await _candidateService.UpdateCandidateAsync(candidate, userId);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up candidate bot associations for bot {BotId}", botId);
            }
        }
    }
}
