using System.Text.Json.Serialization;

namespace RecruiterBot.Infrastructure.Services
{
    // Helper classes for JSON deserialization
    public class OllamaResponse
    {
        [JsonPropertyName("response")]
        public string? Response { get; set; }
    }

    public class CandidateParseResult
    {
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? Email { get; set; }
        public string? Phone { get; set; }
        public string? Address { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? ZipCode { get; set; }
        public string? Country { get; set; }
        public string? LinkedInUrl { get; set; }
        public string? PortfolioUrl { get; set; }
        public string? CurrentTitle { get; set; }
        public string? CurrentCompany { get; set; }
        public int? YearsOfExperience { get; set; }
        public List<string>? Skills { get; set; }
        public string? Summary { get; set; }
        public List<WorkExperienceParseResult>? WorkExperience { get; set; }
        public List<EducationParseResult>? Education { get; set; }
        public List<string>? Certifications { get; set; }
        public double? AiConfidenceScore { get; set; }
    }

    public class WorkExperienceParseResult
    {
        public string? Company { get; set; }
        public string? Title { get; set; }
        public string? StartDate { get; set; }
        public string? EndDate { get; set; }
        public string? Description { get; set; }
        public string? Location { get; set; }
    }

    public class EducationParseResult
    {
        public string? Institution { get; set; }
        public string? Degree { get; set; }
        public string? FieldOfStudy { get; set; }
        public string? GraduationYear { get; set; }
        public string? GPA { get; set; }
    }
}
