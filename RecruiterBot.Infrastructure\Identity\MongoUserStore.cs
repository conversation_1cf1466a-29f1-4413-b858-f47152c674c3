using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.Bson;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;

namespace RecruiterBot.Infrastructure.Identity
{
    public class MongoUserStore :
        IUserStore<User>,
        IUserPasswordStore<User>,
        IUserEmailStore<User>,
        IUserRoleStore<User>,
        IUserPhoneNumberStore<User>,
        IUserSecurityStampStore<User>,
        IUserTwoFactorStore<User>
    {
        private readonly IMongoCollection<User> _usersCollection;
        private readonly ILogger<MongoUserStore> _logger;
        private readonly MongoDbContext _context;

        public MongoUserStore(MongoDbContext context, ILogger<MongoUserStore> logger)
        {
            _context = context;
            _usersCollection = context.Users;
            _logger = logger;
        }

        #region IUserStore Implementation

        public async Task<IdentityResult> CreateAsync(User user, CancellationToken cancellationToken)
        {
            try
            {
                if (user == null) throw new ArgumentNullException(nameof(user));
                
                // Ensure user has an ID
                if (string.IsNullOrEmpty(user.Id))
                {
                    user.Id = ObjectId.GenerateNewId().ToString();
                }
                
                // Set timestamps
                user.CreatedAt = DateTime.UtcNow;
                user.UpdatedAt = DateTime.UtcNow;
                
                await _usersCollection.InsertOneAsync(user, cancellationToken: cancellationToken);
                return IdentityResult.Success;
            }
            catch (MongoWriteException ex) when (ex.WriteError.Category == ServerErrorCategory.DuplicateKey)
            {
                _logger.LogError(ex, "Error creating user: Duplicate username or email");
                return IdentityResult.Failed(new IdentityError { Description = "A user with this username or email already exists." });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user");
                return IdentityResult.Failed(new IdentityError { Description = ex.Message });
            }
        }

        public async Task<IdentityResult> DeleteAsync(User user, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _usersCollection.DeleteOneAsync(u => u.Id == user.Id, cancellationToken);
                return result.DeletedCount > 0 
                    ? IdentityResult.Success 
                    : IdentityResult.Failed(new IdentityError { Description = "User not found" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user");
                return IdentityResult.Failed(new IdentityError { Description = ex.Message });
            }
        }

        public void Dispose()
        {
            // Nothing to dispose
        }

        public async Task<User> FindByIdAsync(string userId, CancellationToken cancellationToken)
        {
            try
            {
                return await _usersCollection
                    .Find(u => u.Id == userId)
                    .FirstOrDefaultAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding user by ID");
                return null;
            }
        }

        public async Task<User> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
        {
            try
            {
                if (string.IsNullOrEmpty(normalizedUserName))
                    throw new ArgumentNullException(nameof(normalizedUserName));
                    
                return await _usersCollection
                    .Find(u => u.NormalizedUserName == normalizedUserName)
                    .FirstOrDefaultAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding user by name");
                throw;
            }
        }

        public Task<string> GetNormalizedUserNameAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.NormalizedUserName);
        }

        public Task<string> GetUserIdAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Id);
        }

        public Task<string> GetUserNameAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.UserName);
        }

        public Task SetNormalizedUserNameAsync(User user, string normalizedName, CancellationToken cancellationToken)
        {
            user.NormalizedUserName = normalizedName;
            return Task.CompletedTask;
        }

        public Task SetUserNameAsync(User user, string userName, CancellationToken cancellationToken)
        {
            user.UserName = userName;
            return Task.CompletedTask;
        }

        public async Task<IdentityResult> UpdateAsync(User user, CancellationToken cancellationToken)
        {
            try
            {
                user.UpdatedAt = DateTime.UtcNow;
                var result = await _usersCollection.ReplaceOneAsync(
                    u => u.Id == user.Id, 
                    user, 
                    new ReplaceOptions { IsUpsert = false },
                    cancellationToken);
                
                return result.ModifiedCount > 0 
                    ? IdentityResult.Success 
                    : IdentityResult.Failed(new IdentityError { Description = "User not found or not modified" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user");
                return IdentityResult.Failed(new IdentityError { Description = ex.Message });
            }
        }

        #endregion

        #region IUserPasswordStore Implementation

        public Task<string> GetPasswordHashAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.PasswordHash);
        }

        public Task<bool> HasPasswordAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(!string.IsNullOrEmpty(user.PasswordHash));
        }

        public Task SetPasswordHashAsync(User user, string passwordHash, CancellationToken cancellationToken)
        {
            user.PasswordHash = passwordHash;
            return Task.CompletedTask;
        }

        #endregion

        #region IUserEmailStore Implementation

        public async Task<User> FindByEmailAsync(string normalizedEmail, CancellationToken cancellationToken)
        {
            try
            {
                return await _usersCollection
                    .Find(u => u.NormalizedEmail == normalizedEmail)
                    .FirstOrDefaultAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error finding user by email");
                return null;
            }
        }

        public Task<string> GetEmailAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Email);
        }

        public Task<bool> GetEmailConfirmedAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.EmailConfirmed);
        }

        public Task<string> GetNormalizedEmailAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.NormalizedEmail);
        }

        public Task SetEmailAsync(User user, string email, CancellationToken cancellationToken)
        {
            user.Email = email;
            return Task.CompletedTask;
        }

        public Task SetEmailConfirmedAsync(User user, bool confirmed, CancellationToken cancellationToken)
        {
            user.EmailConfirmed = confirmed;
            return Task.CompletedTask;
        }

        public Task SetNormalizedEmailAsync(User user, string normalizedEmail, CancellationToken cancellationToken)
        {
            user.NormalizedEmail = normalizedEmail;
            return Task.CompletedTask;
        }

        #endregion

        #region IUserPhoneNumberStore Implementation

        public Task SetPhoneNumberAsync(User user, string? phoneNumber, CancellationToken cancellationToken)
        {
            user.PhoneNumber = phoneNumber;
            return Task.CompletedTask;
        }

        public Task<string?> GetPhoneNumberAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.PhoneNumber);
        }

        public Task<bool> GetPhoneNumberConfirmedAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(true); // Assuming phone number is always confirmed if it exists
        }

        public Task SetPhoneNumberConfirmedAsync(User user, bool confirmed, CancellationToken cancellationToken)
        {
            // No action needed as we're not implementing phone number confirmation
            return Task.CompletedTask;
        }

        #endregion

        #region IUserRoleStore Implementation

        public async Task AddToRoleAsync(User user, string roleName, CancellationToken cancellationToken)
        {
            if (user.Roles == null)
            {
                user.Roles = new List<string>();
            }

            if (!user.Roles.Contains(roleName))
            {
                user.Roles.Add(roleName);
                await UpdateAsync(user, cancellationToken);
            }
        }

        public async Task RemoveFromRoleAsync(User user, string roleName, CancellationToken cancellationToken)
        {
            if (user.Roles != null && user.Roles.Contains(roleName))
            {
                user.Roles.Remove(roleName);
                await UpdateAsync(user, cancellationToken);
            }
        }

        public Task<IList<string>> GetRolesAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult<IList<string>>(user.Roles?.ToList() ?? new List<string>());
        }

        public Task<bool> IsInRoleAsync(User user, string roleName, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Roles?.Contains(roleName) ?? false);
        }

        public async Task<IList<User>> GetUsersInRoleAsync(string roleName, CancellationToken cancellationToken)
        {
            try
            {
                return await _usersCollection
                    .Find(u => u.Roles.Contains(roleName))
                    .ToListAsync(cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting users in role");
                return new List<User>();
            }
        }

        #endregion

        #region IUserSecurityStampStore Implementation

        public Task<string> GetSecurityStampAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.SecurityStamp ?? string.Empty);
        }

        public Task SetSecurityStampAsync(User user, string stamp, CancellationToken cancellationToken)
        {
            user.SecurityStamp = stamp;
            return Task.CompletedTask;
        }

        #endregion

        #region IUserTwoFactorStore Implementation

        public Task<bool> GetTwoFactorEnabledAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.TwoFactorEnabled);
        }

        public Task SetTwoFactorEnabledAsync(User user, bool enabled, CancellationToken cancellationToken)
        {
            user.TwoFactorEnabled = enabled;
            return Task.CompletedTask;
        }

        #endregion
    }
}
