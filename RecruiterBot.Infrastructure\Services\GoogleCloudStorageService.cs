using Google;
using Google.Cloud.Storage.V1;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using RecruiterBot.Infrastructure.Configuration;
using SharpCompress.Common;
using System.Text.RegularExpressions;

namespace RecruiterBot.Infrastructure.Services
{
    public class GoogleCloudStorageService : IFileStorageService
    {
        private readonly StorageClient _storageClient;
        private readonly ILogger<GoogleCloudStorageService> _logger;
        private readonly FileStorageOptions _options;
        private readonly string _bucketName;

        public GoogleCloudStorageService(IOptions<FileStorageOptions> options, ILogger<GoogleCloudStorageService> logger)
        {
            _logger = logger;
            _options = options.Value;
            _bucketName = _options.BucketName;

            try
            {
                // Initialize the storage client
                _storageClient = StorageClient.Create();
                _logger.LogInformation("GCP Storage client initialized for bucket: {BucketName}", _bucketName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize GCP Storage client. Make sure you have proper GCP credentials configured.");
                _logger.LogError(ex, "You can set up credentials by: 1) Setting GOOGLE_APPLICATION_CREDENTIALS environment variable, 2) Running 'gcloud auth application-default login', or 3) Running on GCP with service account");
                throw;
            }
        }

        public async Task<string> UploadFileAsync(IFormFile file, string folder)
        {
            if (file == null || file.Length == 0)
                throw new ArgumentException("File is null or empty");

            var fileName = GenerateUniqueFileName(file.FileName);
            var objectName = string.IsNullOrEmpty(folder) ? fileName : $"{folder.TrimEnd('/')}/{fileName}";

            try
            {
                var contentType = file.ContentType ?? GetContentType(file.FileName);
                // Upload the object
                using (var fileStream = file.OpenReadStream())
                {
                    // Upload the file
                    await _storageClient.UploadObjectAsync(_bucketName, objectName, contentType, fileStream); // Assuming text file
                }

                
                var publicUrl = $"https://storage.googleapis.com/{_bucketName}/{objectName}";
                _logger.LogInformation("File uploaded to GCP Storage: {FileName} -> {ObjectName}", file.FileName, objectName);
                
                return publicUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {FileName} to GCP Storage", file.FileName);
                throw;
            }
        }

        public async Task<string> UploadFileAsync(string localFilePath, string fileName, string folder)
        {
            if (!File.Exists(localFilePath))
                throw new FileNotFoundException($"Local file not found: {localFilePath}");

            var uniqueFileName = GenerateUniqueFileName(fileName);
            var objectName = string.IsNullOrEmpty(folder) ? uniqueFileName : $"{folder.TrimEnd('/')}/{uniqueFileName}";

            try
            {
                using var fileStream = File.OpenRead(localFilePath);
                var contentType = GetContentType(fileName);

                    // Upload the file
                await _storageClient.UploadObjectAsync(_bucketName, objectName, contentType, fileStream); 

                var publicUrl = $"https://storage.googleapis.com/{_bucketName}/{objectName}";
                _logger.LogInformation("File uploaded to GCP Storage: {LocalPath} -> {ObjectName}", localFilePath, objectName);
                
                return publicUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to upload file {LocalPath} to GCP Storage", localFilePath);
                throw;
            }
        }

        public async Task<bool> DeleteFileAsync(string fileUrl)
        {
            try
            {
                var objectName = ExtractObjectNameFromUrl(fileUrl);
                if (string.IsNullOrEmpty(objectName))
                {
                    _logger.LogWarning("Could not extract object name from URL: {FileUrl}", fileUrl);
                    return false;
                }

                await _storageClient.DeleteObjectAsync(_bucketName, objectName);
                _logger.LogInformation("File deleted from GCP Storage: {ObjectName}", objectName);
                return true;
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                _logger.LogWarning("File not found for deletion: {FileUrl}", fileUrl);
                return true; // Consider it successful if file doesn't exist
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to delete file from GCP Storage: {FileUrl}", fileUrl);
                return false;
            }
        }

        public async Task<string> GetSignedUrlAsync(string fileUrl, int expirationMinutes = 60)
        {
            try
            {
                var objectName = ExtractObjectNameFromUrl(fileUrl);
                if (string.IsNullOrEmpty(objectName))
                    throw new ArgumentException($"Could not extract object name from URL: {fileUrl}");

                // For now, return the public URL since signed URLs require service account setup
                // In production, you would configure proper signed URL generation
                _logger.LogDebug("Returning public URL for {ObjectName} (signed URLs not configured)", objectName);
                return fileUrl;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate signed URL for: {FileUrl}", fileUrl);
                return fileUrl;
            }
        }

        public async Task<bool> FileExistsAsync(string fileUrl)
        {
            try
            {
                var objectName = ExtractObjectNameFromUrl(fileUrl);
                if (string.IsNullOrEmpty(objectName))
                    return false;

                var obj = await _storageClient.GetObjectAsync(_bucketName, objectName);
                return obj != null;
            }
            catch (GoogleApiException ex) when (ex.HttpStatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists in GCP Storage: {FileUrl}", fileUrl);
                return false;
            }
        }

        private static string GenerateUniqueFileName(string originalFileName)
        {
            var extension = Path.GetExtension(originalFileName);
            var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
            var sanitizedName = Regex.Replace(nameWithoutExtension, @"[^a-zA-Z0-9\-_]", "_");
            return $"{Guid.NewGuid()}_{sanitizedName}{extension}";
        }

        private string ExtractObjectNameFromUrl(string fileUrl)
        {
            if (string.IsNullOrEmpty(fileUrl))
                return string.Empty;

            // Handle GCS URLs: https://storage.googleapis.com/bucket-name/object-name
            var gcsPattern = $@"https://storage\.googleapis\.com/{Regex.Escape(_bucketName)}/(.+)";
            var match = Regex.Match(fileUrl, gcsPattern);
            if (match.Success)
                return match.Groups[1].Value;

            // Handle gs:// URLs: gs://bucket-name/object-name
            var gsPattern = $@"gs://{Regex.Escape(_bucketName)}/(.+)";
            match = Regex.Match(fileUrl, gsPattern);
            if (match.Success)
                return match.Groups[1].Value;

            // If it's just an object name without URL
            if (!fileUrl.Contains("://"))
                return fileUrl;

            return string.Empty;
        }

        private static string GetContentType(string fileName)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();
            return extension switch
            {
                ".pdf" => "application/pdf",
                ".doc" => "application/msword",
                ".docx" => "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                ".txt" => "text/plain",
                ".rtf" => "application/rtf",
                _ => "application/octet-stream"
            };
        }
    }
}
