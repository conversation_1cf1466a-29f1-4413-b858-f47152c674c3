using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Admin.LLMModels
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class DeleteModel : PageModel
    {
        private readonly ILLMModelService _llmModelService;
        private readonly ILogger<DeleteModel> _logger;

        public DeleteModel(ILLMModelService llmModelService, ILogger<DeleteModel> logger)
        {
            _llmModelService = llmModelService;
            _logger = logger;
        }

        [BindProperty]
        public string ModelId { get; set; } = string.Empty;

        public LLMModel? Model { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model ID is required." });
                }

                ModelId = id;
                Model = await _llmModelService.GetModelByIdAsync(id);

                if (Model == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model not found." });
                }

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading delete LLM model page for ID {ModelId}", id);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the model." });
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(ModelId))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model ID is required." });
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Get the model details for logging before deletion
                var model = await _llmModelService.GetModelByIdAsync(ModelId);
                if (model == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model not found." });
                }

                var success = await _llmModelService.DeleteModelAsync(ModelId);

                if (success)
                {
                    _logger.LogInformation("Admin user {UserId} deleted LLM model {ModelId} ({ModelName})", 
                        userId, ModelId, model.ModelName);

                    return RedirectToPage("./Index", new { SuccessMessage = $"Model '{model.DisplayName}' deleted successfully." });
                }
                else
                {
                    _logger.LogWarning("Failed to delete LLM model {ModelId} for user {UserId}", ModelId, userId);
                    return RedirectToPage("./Index", new { ErrorMessage = "Failed to delete the model. It may have already been deleted." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting LLM model {ModelId}", ModelId);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while deleting the model. Please try again." });
            }
        }
    }
}
