using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class DetailsModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ICandidateService _candidateService;

        public DetailsModel(IBotConfigurationService botConfigService, ICandidateService candidateService)
        {
            _botConfigService = botConfigService;
            _candidateService = candidateService;
        }

        public BotConfiguration Configuration { get; set; }
        public List<Candidate> AssignedCandidates { get; set; } = new List<Candidate>();

        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }
            var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

            Configuration = await _botConfigService.GetConfigurationAsync(id, userId);
            if (Configuration == null)
            {
                return NotFound();
            }

            // Load assigned candidates if any
            if (Configuration.AssignedCandidates?.Any() == true)
            {
                foreach (var candidateId in Configuration.AssignedCandidates)
                {
                    var candidate = await _candidateService.GetCandidateAsync(candidateId, userId);
                    if (candidate != null)
                    {
                        AssignedCandidates.Add(candidate);
                    }
                }
            }

            return Page();
        }
    }
}
