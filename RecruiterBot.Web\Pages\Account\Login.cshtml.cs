using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;

namespace RecruiterBot.Web.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly SignInManager<User> _signInManager;
        private readonly UserManager<User> _userManager;
        private readonly ILogger<LoginModel> _logger;
        private readonly IRoleManagementService _roleManagementService;

        public LoginModel(
            SignInManager<User> signInManager,
            UserManager<User> userManager,
            ILogger<LoginModel> logger,
            IRoleManagementService roleManagementService)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _logger = logger;
            _roleManagementService = roleManagementService;
            Input = new InputModel();
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public string ReturnUrl { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class InputModel
        {
            public string Email { get; set; }
            public string? Password { get; set; }
            public bool RememberMe { get; set; }
        }


        public async Task OnGetAsync(string? returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/");

            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl ?? "/";
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            _logger.LogInformation("Login POST request received");
            returnUrl = returnUrl ?? Url.Content("~/");

            _logger.LogInformation("ModelState is valid: {IsValid}", ModelState.IsValid);
            if (!ModelState.IsValid)
            {
                _logger.LogWarning("Model state is invalid. Errors: {Errors}", 
                    string.Join(", ", ModelState.Values
                        .SelectMany(v => v.Errors)
                        .Select(e => e.ErrorMessage)));
                return Page();
            }

            _logger.LogInformation("Attempting to sign in user: {Email}", Input.Email);

            // Check if user exists first
            var userCheck = await _userManager.FindByEmailAsync(Input.Email);
            if (userCheck == null)
            {
                _logger.LogWarning("Login attempt for non-existent user: {Email}", Input.Email);
                ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                return Page();
            }

            _logger.LogInformation("User found: {UserId}, EmailConfirmed: {EmailConfirmed}, IsActivated: {IsActivated}, MustChangePassword: {MustChangePassword}, IsActive: {IsActive}",
                userCheck.Id, userCheck.EmailConfirmed, userCheck.IsActivated, userCheck.MustChangePassword, userCheck.IsActive);

            // Check if user is active
            if (!userCheck.IsActive)
            {
                _logger.LogWarning("Login attempt for inactive user: {Email}", Input.Email);
                ModelState.AddModelError(string.Empty, "Your account has been deactivated. Please contact your administrator.");
                return Page();
            }

            // Check if user's Corp Admin is active (for non-Admin users)
            var userRoles = await _userManager.GetRolesAsync(userCheck);
            if (!userRoles.Contains("ADMIN"))
            {
                try
                {
                    var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userCheck.Id);
                    if (!string.IsNullOrEmpty(corpAdminId))
                    {
                        var corpAdmin = await _userManager.FindByIdAsync(corpAdminId);
                        if (corpAdmin != null && !corpAdmin.IsActive)
                        {
                            _logger.LogWarning("Login attempt for user {Email} whose Corp Admin {CorpAdminEmail} is inactive",
                                Input.Email, corpAdmin.Email);
                            ModelState.AddModelError(string.Empty, "Your organization account has been deactivated. Please contact support.");
                            return Page();
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking Corp Admin status for user {Email}", Input.Email);
                    // Continue with login if we can't determine Corp Admin status
                }
            }

            try
            {
                var result = await _signInManager.PasswordSignInAsync(
                    Input.Email,
                    Input.Password,
                    Input.RememberMe,
                    lockoutOnFailure: true);

                if (result.Succeeded)
                {
                    _logger.LogInformation("User {Email} logged in successfully.", Input.Email);

                    // Check if user needs to complete account setup
                    var user = await _userManager.FindByEmailAsync(Input.Email);
                    if (user != null && (user.MustChangePassword || user.IsTemporaryPassword || !user.IsActivated))
                    {
                        // If user has an activation token, redirect to setup account page
                        if (!string.IsNullOrEmpty(user.ActivationToken) &&
                            user.ActivationTokenExpiry.HasValue &&
                            user.ActivationTokenExpiry > DateTime.UtcNow)
                        {
                            _logger.LogInformation("User {Email} needs to complete account setup, redirecting to setup page.", Input.Email);
                            return RedirectToPage("./SetupAccount", new {
                                userId = user.Id,
                                token = user.ActivationToken,
                                registration = true
                            });
                        }
                        else
                        {
                            // Fallback to change password page if no valid activation token
                            _logger.LogInformation("User {Email} must change password, redirecting to change password page.", Input.Email);
                            return RedirectToPage("./ChangePassword", new { returnUrl = returnUrl, forced = true });
                        }
                    }

                    // Redirect to Dashboard after successful login
                    if (returnUrl == "/" || returnUrl == "~/" || string.IsNullOrEmpty(returnUrl))
                    {
                        return RedirectToPage("/Dashboard");
                    }
                    return LocalRedirect(returnUrl);
                }
                
                if (result.IsLockedOut)
                {
                    _logger.LogWarning("User account {Email} locked out.", Input.Email);
                    return RedirectToPage("./Lockout");
                }
                
                if (result.RequiresTwoFactor)
                {
                    _logger.LogInformation("User {Email} requires two-factor authentication.", Input.Email);
                    return RedirectToPage("./LoginWith2fa", new { ReturnUrl = returnUrl, RememberMe = Input.RememberMe });
                }

                if (result.IsNotAllowed)
                {
                    // Check if email is not confirmed
                    var user = await _userManager.FindByEmailAsync(Input.Email);
                    if (user != null && !user.EmailConfirmed)
                    {
                        _logger.LogWarning("Login attempt for user {Email} with unconfirmed email.", Input.Email);
                        ModelState.AddModelError(string.Empty, "You must confirm your email before you can log in.");
                        ViewData["ShowResendLink"] = true;
                        ViewData["Email"] = Input.Email;
                        return Page();
                    }
                    else
                    {
                        _logger.LogWarning("Login not allowed for user {Email}.", Input.Email);
                        ModelState.AddModelError(string.Empty, "Your account is not allowed to sign in.");
                        return Page();
                    }
                }

                _logger.LogWarning("Invalid login attempt for user {Email}. Result: Succeeded={Succeeded}, IsLockedOut={IsLockedOut}, IsNotAllowed={IsNotAllowed}, RequiresTwoFactor={RequiresTwoFactor}",
                    Input.Email, result.Succeeded, result.IsLockedOut, result.IsNotAllowed, result.RequiresTwoFactor);
                ModelState.AddModelError(string.Empty, "Invalid login attempt.");
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for user {Email}", Input.Email);
                ModelState.AddModelError(string.Empty, "An error occurred while processing your request.");
                return Page();
            }
        }
    }
}
