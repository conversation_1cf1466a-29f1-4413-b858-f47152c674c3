@page "{id?}"
@model RecruiterBot.Web.Pages.Candidates.DeleteModel
@{
    ViewData["Title"] = "Delete Candidate";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h3 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        Delete Candidate
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. The candidate and all associated data will be permanently deleted.
                    </div>

                    <h5 class="mb-3">Are you sure you want to delete this candidate?</h5>

                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Name:</strong></td>
                                    <td>@Model.Candidate.FullName</td>
                                </tr>
                                @if (!string.IsNullOrEmpty(Model.Candidate.Email))
                                {
                                    <tr>
                                        <td><strong>Email:</strong></td>
                                        <td>@Model.Candidate.Email</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.Phone))
                                {
                                    <tr>
                                        <td><strong>Phone:</strong></td>
                                        <td>@Model.Candidate.Phone</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.CurrentTitle))
                                {
                                    <tr>
                                        <td><strong>Current Position:</strong></td>
                                        <td>@Model.Candidate.CurrentTitle</td>
                                    </tr>
                                }
                                @if (!string.IsNullOrEmpty(Model.Candidate.CurrentCompany))
                                {
                                    <tr>
                                        <td><strong>Current Company:</strong></td>
                                        <td>@Model.Candidate.CurrentCompany</td>
                                    </tr>
                                }
                                <tr>
                                    <td><strong>Added:</strong></td>
                                    <td>@Model.Candidate.CreatedDateUtc.ToString("MMM dd, yyyy 'at' HH:mm")</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <form method="post">
                        <input type="hidden" asp-for="Input.CandidateId" />
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-danger" id="deleteBtn">
                                    <i class="bi bi-trash me-2"></i>
                                    Yes, Delete Candidate
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary ms-2">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    Cancel
                                </a>
                                <a asp-page="./Details" asp-route-id="@Model.Candidate.Id" class="btn btn-info ms-2">
                                    <i class="bi bi-eye me-2"></i>
                                    View Details
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#deleteBtn').html();

            // Show loading state when form is submitted
            $('form').on('submit', function(e) {
                // Show confirmation dialog
                if (!confirm('Are you absolutely sure you want to delete this candidate? This action cannot be undone.')) {
                    e.preventDefault();
                    return false;
                }

                var $btn = $('#deleteBtn');
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Deleting...');
            });

            // Reset button state if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $btn = $('#deleteBtn');
                $btn.prop('disabled', false);
                $btn.html(originalButtonHtml);
            }
        });
    </script>
}
