using System.Threading.Tasks;
using Microsoft.AspNetCore.Identity;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;

namespace RecruiterBot.Infrastructure.Services
{
    public class UserService
    {
        private readonly IMongoCollection<User> _users;
        private readonly IPasswordHasher<User> _passwordHasher;

        public UserService(MongoDbContext context, IPasswordHasher<User> passwordHasher)
        {
            _users = context.Users;
            _passwordHasher = passwordHasher;
        }

        public async Task<User> RegisterAsync(string email, string password, string userName)
        {
            var existingUser = await _users.Find(u => u.Email == email).FirstOrDefaultAsync();
            if (existingUser != null)
            {
                return null; // User already exists
            }

            var user = new User
            {
                Email = email,
                UserName = userName,
                CreatedAt = DateTime.UtcNow,
                EmailConfirmed = false
            };

            user.PasswordHash = _passwordHasher.HashPassword(user, password);
            await _users.InsertOneAsync(user);
            return user;
        }

        public async Task<User> LoginAsync(string email, string password)
        {
            var user = await _users.Find(u => u.Email == email).FirstOrDefaultAsync();
            if (user == null)
            {
                return null;
            }

            var result = _passwordHasher.VerifyHashedPassword(user, user.PasswordHash, password);
            if (result == PasswordVerificationResult.Success)
            {
                user.LastLoginAt = DateTime.UtcNow;
                await _users.ReplaceOneAsync(u => u.Id == user.Id, user);
                return user;
            }

            return null;
        }
    }
}
