@page
@model DeletePersonalDataModel
@{
    ViewData["Title"] = "Delete Personal Data";
    ViewData["ActivePage"] = ManageNavPages.PersonalData;
}

<div class="space-y-6">
    <div class="alert alert-error">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636"></path>
            </svg>
            <label><strong>Deleting this data will permanently remove your account, and this cannot be recovered.</strong></label>
        </div>
    </div>

    <div class="card bg-base-100 shadow">
        <div class="card-body">
            <form id="delete-user" method="post" class="space-y-4">
                <div asp-validation-summary="All" class="text-error"></div>
                
                @if (Model.RequirePassword)
                {
                    <div class="form-control">
                        <label asp-for="Input.Password" class="label">
                            <span class="label-text">Password</span>
                        </label>
                        <input asp-for="Input.Password" class="input input-bordered w-full" />
                        <span asp-validation-for="Input.Password" class="text-error text-sm"></span>
                    </div>
                }
                
                <div class="form-control mt-6">
                    <button class="btn btn-error" type="submit">
                        Delete data and close my account
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
