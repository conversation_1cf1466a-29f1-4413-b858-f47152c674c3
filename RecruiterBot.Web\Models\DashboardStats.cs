using System;

namespace RecruiterBot.Web.Models
{
    public class DashboardStats
    {
        // Bot statistics (for Corp Admin and Users)
        public int TotalBots { get; set; }
        public int ActiveBots { get; set; }
        public int InactiveBots { get; set; }
        public int AiSearchBots { get; set; }
        public int ManualSearchBots { get; set; }

        // User statistics (for Admin)
        public int TotalCorpAdmins { get; set; }
        public int ActiveCorpAdmins { get; set; }

        public DateTime LastUpdated { get; set; }
    }
}
