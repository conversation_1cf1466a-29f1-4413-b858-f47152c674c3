using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;
using RecruiterBot.Core.Services;

namespace RecruiterBot.Infrastructure.Services
{
    public class LLMModelService : ILLMModelService
    {
        private readonly IMongoCollection<LLMModel> _models;
        private readonly ILogger<LLMModelService> _logger;
        private readonly IEncryptionService _encryptionService;
        private const string CollectionName = "LLMModels";

        public LLMModelService(
            IOptions<MongoDbSettings> settings,
            ILogger<LLMModelService> logger,
            IEncryptionService encryptionService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));

            if (settings?.Value == null)
                throw new ArgumentNullException(nameof(settings));

            var mongoClient = new MongoClient(settings.Value.ConnectionString);
            var database = mongoClient.GetDatabase(settings.Value.DatabaseName);
            _models = database.GetCollection<LLMModel>(CollectionName);

            // Create indexes for better performance
            CreateIndexes();
        }

        public async Task<IEnumerable<LLMModel>> GetAllModelsAsync()
        {
            try
            {
                var models = await _models
                    .Find(_ => true)
                    .SortByDescending(m => m.CreatedAt)
                    .ToListAsync();

                // Decrypt API keys for each model
                foreach (var model in models)
                {
                    try
                    {
                        model.DecryptApiKey(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting API key for model {ModelId}", model.Id);
                        // Continue without decrypted API key
                    }
                }

                _logger.LogInformation("Retrieved {Count} LLM models", models.Count);
                return models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all LLM models");
                throw;
            }
        }

        public async Task<IEnumerable<LLMModel>> GetActiveModelsAsync()
        {
            try
            {
                var models = await _models
                    .Find(m => m.IsActive)
                    .SortBy(m => m.ModelProvider)
                    .ThenBy(m => m.DisplayName)
                    .ToListAsync();

                // Decrypt API keys for each model
                foreach (var model in models)
                {
                    try
                    {
                        model.DecryptApiKey(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting API key for model {ModelId}", model.Id);
                        // Continue without decrypted API key
                    }
                }

                _logger.LogInformation("Retrieved {Count} active LLM models", models.Count);
                return models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving active LLM models");
                throw;
            }
        }

        public async Task<LLMModel?> GetModelByIdAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    return null;

                var model = await _models
                    .Find(m => m.Id == id)
                    .FirstOrDefaultAsync();

                if (model != null)
                {
                    try
                    {
                        model.DecryptApiKey(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting API key for model {ModelId}", model.Id);
                        // Continue without decrypted API key
                    }
                }

                return model;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving LLM model {ModelId}", id);
                throw;
            }
        }

        public async Task<LLMModel> CreateModelAsync(LLMModel model, string userId)
        {
            try
            {
                if (model == null)
                    throw new ArgumentNullException(nameof(model));

                if (string.IsNullOrWhiteSpace(userId))
                    throw new ArgumentException("User ID cannot be empty", nameof(userId));

                // Check if a model with the same name already exists
                var existingModel = await _models
                    .Find(m => m.ModelName.ToLower() == model.ModelName.ToLower() && m.ModelProvider == model.ModelProvider)
                    .FirstOrDefaultAsync();

                if (existingModel != null)
                {
                    throw new InvalidOperationException($"A model with name '{model.ModelName}' already exists for provider '{model.ModelProvider}'");
                }

                // Encrypt API key before saving
                model.EncryptApiKey(_encryptionService);

                model.CreatedBy = userId;
                model.CreatedAt = DateTime.UtcNow;
                model.UpdatedBy = userId;
                model.UpdatedAt = DateTime.UtcNow;

                await _models.InsertOneAsync(model);

                _logger.LogInformation("Created LLM model {ModelId} ({ModelName}) by user {UserId}", 
                    model.Id, model.ModelName, userId);

                return model;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating LLM model {ModelName}", model?.ModelName);
                throw;
            }
        }

        public async Task<LLMModel> UpdateModelAsync(LLMModel model, string userId)
        {
            try
            {
                if (model == null)
                    throw new ArgumentNullException(nameof(model));

                if (string.IsNullOrWhiteSpace(userId))
                    throw new ArgumentException("User ID cannot be empty", nameof(userId));

                var existingModel = await GetModelByIdAsync(model.Id);
                if (existingModel == null)
                    throw new InvalidOperationException($"LLM model with ID {model.Id} not found");

                // Check if another model with the same name exists (excluding current model)
                var duplicateModel = await _models
                    .Find(m => m.Id != model.Id && 
                              m.ModelName.ToLower() == model.ModelName.ToLower() && 
                              m.ModelProvider == model.ModelProvider)
                    .FirstOrDefaultAsync();

                if (duplicateModel != null)
                {
                    throw new InvalidOperationException($"A model with name '{model.ModelName}' already exists for provider '{model.ModelProvider}'");
                }

                // Encrypt API key before saving
                model.EncryptApiKey(_encryptionService);

                // Preserve creation info
                model.CreatedBy = existingModel.CreatedBy;
                model.CreatedAt = existingModel.CreatedAt;
                model.UpdatedBy = userId;
                model.UpdatedAt = DateTime.UtcNow;

                await _models.ReplaceOneAsync(m => m.Id == model.Id, model);

                _logger.LogInformation("Updated LLM model {ModelId} ({ModelName}) by user {UserId}", 
                    model.Id, model.ModelName, userId);

                return model;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating LLM model {ModelId}", model?.Id);
                throw;
            }
        }

        public async Task<bool> DeleteModelAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                    return false;

                var result = await _models.DeleteOneAsync(m => m.Id == id);

                if (result.DeletedCount > 0)
                {
                    _logger.LogInformation("Deleted LLM model {ModelId}", id);
                    return true;
                }

                _logger.LogWarning("LLM model {ModelId} not found for deletion", id);
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting LLM model {ModelId}", id);
                throw;
            }
        }

        public async Task<bool> ToggleModelStatusAsync(string id, string userId)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id) || string.IsNullOrWhiteSpace(userId))
                    return false;

                var model = await GetModelByIdAsync(id);
                if (model == null)
                    return false;

                var update = Builders<LLMModel>.Update
                    .Set(m => m.IsActive, !model.IsActive)
                    .Set(m => m.UpdatedBy, userId)
                    .Set(m => m.UpdatedAt, DateTime.UtcNow);

                var result = await _models.UpdateOneAsync(m => m.Id == id, update);

                if (result.ModifiedCount > 0)
                {
                    _logger.LogInformation("Toggled status of LLM model {ModelId} to {Status} by user {UserId}", 
                        id, !model.IsActive ? "Active" : "Inactive", userId);
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling status of LLM model {ModelId}", id);
                throw;
            }
        }

        public async Task<IEnumerable<LLMModel>> GetModelsByProviderAsync(ModelProvider provider)
        {
            try
            {
                var models = await _models
                    .Find(m => m.ModelProvider == provider)
                    .SortBy(m => m.DisplayName)
                    .ToListAsync();

                // Decrypt API keys for each model
                foreach (var model in models)
                {
                    try
                    {
                        model.DecryptApiKey(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting API key for model {ModelId}", model.Id);
                        // Continue without decrypted API key
                    }
                }

                return models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving models for provider {Provider}", provider);
                throw;
            }
        }

        public async Task<IEnumerable<LLMModel>> SearchModelsAsync(string searchTerm)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(searchTerm))
                    return await GetAllModelsAsync();

                var filter = Builders<LLMModel>.Filter.Or(
                    Builders<LLMModel>.Filter.Regex(m => m.ModelName, new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<LLMModel>.Filter.Regex(m => m.DisplayName, new MongoDB.Bson.BsonRegularExpression(searchTerm, "i")),
                    Builders<LLMModel>.Filter.Regex(m => m.Description, new MongoDB.Bson.BsonRegularExpression(searchTerm, "i"))
                );

                var models = await _models
                    .Find(filter)
                    .SortByDescending(m => m.CreatedAt)
                    .ToListAsync();

                // Decrypt API keys for each model
                foreach (var model in models)
                {
                    try
                    {
                        model.DecryptApiKey(_encryptionService);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error decrypting API key for model {ModelId}", model.Id);
                        // Continue without decrypted API key
                    }
                }

                return models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching models with term {SearchTerm}", searchTerm);
                throw;
            }
        }

        private void CreateIndexes()
        {
            try
            {
                // Create index on ModelProvider and ModelName for uniqueness check
                var providerNameIndex = Builders<LLMModel>.IndexKeys
                    .Ascending(m => m.ModelProvider)
                    .Ascending(m => m.ModelName);
                
                _models.Indexes.CreateOne(new CreateIndexModel<LLMModel>(providerNameIndex));

                // Create index on IsActive for filtering
                var activeIndex = Builders<LLMModel>.IndexKeys.Ascending(m => m.IsActive);
                _models.Indexes.CreateOne(new CreateIndexModel<LLMModel>(activeIndex));

                // Create text index for search functionality
                var textIndex = Builders<LLMModel>.IndexKeys
                    .Text(m => m.ModelName)
                    .Text(m => m.DisplayName)
                    .Text(m => m.Description);
                
                _models.Indexes.CreateOne(new CreateIndexModel<LLMModel>(textIndex));
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error creating indexes for LLMModel collection");
                // Don't throw - indexes are not critical for basic functionality
            }
        }
    }
}
