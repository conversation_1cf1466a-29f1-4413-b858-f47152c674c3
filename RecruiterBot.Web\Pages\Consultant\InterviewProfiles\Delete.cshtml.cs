using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Consultant.InterviewProfiles
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class DeleteModel : PageModel
    {
        private readonly IInterviewProfileService _interviewProfileService;
        private readonly ILogger<DeleteModel> _logger;

        public DeleteModel(IInterviewProfileService interviewProfileService, ILogger<DeleteModel> logger)
        {
            _interviewProfileService = interviewProfileService;
            _logger = logger;
        }

        [BindProperty]
        public string ProfileId { get; set; } = string.Empty;

        public InterviewProfile? Profile { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile ID is required." });
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                ProfileId = id;
                Profile = await _interviewProfileService.GetProfileByIdAsync(id, consultantId);

                if (Profile == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile not found or you don't have access to it." });
                }

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading delete interview profile page for ID {ProfileId}", id);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the profile." });
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (string.IsNullOrWhiteSpace(ProfileId))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile ID is required." });
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                var success = await _interviewProfileService.DeleteProfileAsync(ProfileId, consultantId);

                if (success)
                {
                    _logger.LogInformation("Consultant {ConsultantId} deleted interview profile {ProfileId}", 
                        consultantId, ProfileId);

                    return RedirectToPage("./Index", new { SuccessMessage = "Interview profile deleted successfully." });
                }
                else
                {
                    _logger.LogWarning("Failed to delete interview profile {ProfileId} for consultant {ConsultantId}", 
                        ProfileId, consultantId);
                    return RedirectToPage("./Index", new { ErrorMessage = "Failed to delete the profile. It may have already been deleted." });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting interview profile {ProfileId}", ProfileId);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while deleting the profile. Please try again." });
            }
        }
    }
}
