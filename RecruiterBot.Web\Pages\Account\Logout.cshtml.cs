using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;

namespace RecruiterBot.Web.Pages.Account
{
    [AllowAnonymous]
    public class LogoutModel : PageModel
    {
        private readonly SignInManager<User> _signInManager;
        private readonly ILogger<LogoutModel> _logger;

        public LogoutModel(SignInManager<User> signInManager, ILogger<LogoutModel> logger)
        {
            _signInManager = signInManager;
            _logger = logger;
        }

        public void OnGet()
        {
            // Show the logout confirmation page
        }

        public async Task<IActionResult> OnPost(string returnUrl = null)
        {
            // Clear the existing external cookie
            await HttpContext.SignOutAsync(IdentityConstants.ApplicationScheme);
            
            // Sign out the user
            await _signInManager.SignOutAsync();
            
            _logger.LogInformation("User logged out.");
            
            // Clear any existing session data
            HttpContext.Session.Clear();
            
            // Redirect to the home page after logout
            if (returnUrl != null)
            {
                return LocalRedirect(returnUrl);
            }
            else
            {
                // Force a full page reload to ensure all auth state is cleared
                return RedirectToPage("/Account/Logout");
            }
        }
    }
}
