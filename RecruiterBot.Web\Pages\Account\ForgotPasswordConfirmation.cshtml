@page
@model ForgotPasswordConfirmation
@{
    ViewData["Title"] = "Password Reset Email Sent";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-envelope-check-fill text-success" style="font-size: 3rem;"></i>
                        <h2 class="mt-3 text-success">Email Sent!</h2>
                        <p class="text-muted">If an account with that email exists, we've sent you a password reset link.</p>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>What's next?</strong>
                        <ul class="mb-0 mt-2">
                            <li>Check your email inbox (and spam folder)</li>
                            <li>Click the password reset link in the email</li>
                            <li>The link will expire in 1 hour for security</li>
                        </ul>
                    </div>

                    <div class="text-center mt-4">
                        <a asp-page="./Login" class="btn btn-primary">
                            <i class="bi bi-arrow-left me-2"></i>
                            Back to Login
                        </a>
                    </div>

                    <div class="text-center mt-3">
                        <small class="text-muted">
                            Didn't receive an email?
                            <a asp-page="./ForgotPassword" class="text-decoration-none">Try again</a>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
