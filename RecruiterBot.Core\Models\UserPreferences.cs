using MongoDB.Bson.Serialization.Attributes;

namespace RecruiterBot.Core.Models
{
    public class UserPreferences
    {
        [BsonElement("theme")]
        public string Theme { get; set; } = "light";

        [BsonElement("notifications")]
        public NotificationPreferences Notifications { get; set; } = new NotificationPreferences();

        [BsonElement("emailNotifications")]
        public bool EmailNotifications { get; set; } = true;

        [BsonElement("pushNotifications")]
        public bool PushNotifications { get; set; } = true;
    }
}
