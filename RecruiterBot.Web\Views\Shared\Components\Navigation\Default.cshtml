@using Microsoft.AspNetCore.Identity
@using RecruiterBot.Core.Models
@inject SignInManager<User> SignInManager
@model User

<nav class="bg-base-100 shadow-lg">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
            <div class="flex items-center">
                <a href="/" class="text-xl font-bold text-primary">Smart Recruiter AI</a>
                @if (SignInManager.IsSignedIn(User))
                {
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="/Dashboard" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-base-content hover:border-primary hover:text-primary">
                            Dashboard
                        </a>
                        <a href="/Jobs" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-base-content hover:border-primary hover:text-primary">
                            Jobs
                        </a>
                        <a href="/Candidates" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-base-content hover:border-primary hover:text-primary">
                            Candidates
                        </a>
                        <a href="/Analytics" class="inline-flex items-center px-1 pt-1 border-b-2 border-transparent text-sm font-medium text-base-content hover:border-primary hover:text-primary">
                            Analytics
                        </a>
                    </div>
                }
            </div>
            @if (SignInManager.IsSignedIn(User))
            {
                <div class="hidden md:ml-4 md:flex-shrink-0 md:flex md:items-center">
                    <div class="dropdown dropdown-end">
                        <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                            <div class="w-10 rounded-full">
                                <img src="@(Model?.ProfileImageUrl ?? "/images/default-avatar.svg")" alt="@(Model?.DisplayName ?? "User")" />
                            </div>
                        </div>
                        <ul tabindex="0" class="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
                            <li class="menu-title">
                                <span>@(Model?.DisplayName ?? "User")</span>
                            </li>
                            <li><a href="/Account/Manage">Profile</a></li>
                            <li><a href="/Account/Manage/ChangePassword">Change Password</a></li>
                            <li><a href="/Account/Manage/TwoFactorAuthentication">Two-Factor Auth</a></li>
                            <li class="divider"></li>
                            <li>
                                <form id="logoutForm" class="form-inline" action="/Account/Logout" asp-route-returnUrl="@Url.Page("/", new { area = "" })">
                                    <button id="logout" type="submit" class="btn btn-ghost">Logout</button>
                                </form>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="-mr-2 flex items-center md:hidden">
                    <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-base-content hover:text-primary hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            }
            else
            {
                <div class="hidden md:flex items-center space-x-4">
                    <a href="/Account/Login" class="btn btn-ghost">Log in</a>
                    <a href="/Account/Register" class="btn btn-primary">Sign up</a>
                </div>
                <div class="-mr-2 flex items-center md:hidden">
                    <button type="button" class="inline-flex items-center justify-center p-2 rounded-md text-base-content hover:text-primary hover:bg-base-200 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary" aria-controls="mobile-menu" aria-expanded="false">
                        <span class="sr-only">Open main menu</span>
                        <svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>
            }
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="md:hidden hidden" id="mobile-menu">
        <div class="pt-2 pb-3 space-y-1">
            @if (SignInManager.IsSignedIn(User))
            {
                <a href="/Dashboard" class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-base-content hover:bg-base-200 hover:border-primary">
                    Dashboard
                </a>
                <a href="/Jobs" class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-base-content hover:bg-base-200 hover:border-primary">
                    Jobs
                </a>
                <a href="/Candidates" class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-base-content hover:bg-base-200 hover:border-primary">
                    Candidates
                </a>
                <a href="/Analytics" class="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-base-content hover:bg-base-200 hover:border-primary">
                    Analytics
                </a>
            }
            else
            {
                <div class="px-4 py-2 space-y-2">
                    <a href="/Account/Login" class="btn btn-primary btn-block">Log in</a>
                    <a href="/Account/Register" class="btn btn-outline btn-block">Sign up</a>
                </div>
            }
        </div>
        @if (SignInManager.IsSignedIn(User))
        {
            <div class="pt-4 pb-3 border-t border-base-200">
                <div class="flex items-center px-4">
                    <div class="flex-shrink-0">
                        <img class="h-10 w-10 rounded-full" src="@(Model?.ProfileImageUrl ?? "/images/default-avatar.png")" alt="@(Model?.DisplayName ?? "User")">
                    </div>
                    <div class="ml-3">
                        <div class="text-base font-medium text-base-content">@(Model?.DisplayName ?? "User")</div>
                        <div class="text-sm font-medium text-base-content/70">@Model?.Email</div>
                    </div>
                </div>
                <div class="mt-3 space-y-1">
                    <a href="/Account/Manage" class="block px-4 py-2 text-base font-medium text-base-content hover:bg-base-200">Your Profile</a>
                    <a href="/Account/Manage/ChangePassword" class="block px-4 py-2 text-base font-medium text-base-content hover:bg-base-200">Change Password</a>
                    <a href="/Account/Manage/TwoFactorAuthentication" class="block px-4 py-2 text-base font-medium text-base-content hover:bg-base-200">Two-Factor Auth</a>
                    <form id="logoutForm" class="px-4 py-2" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Page("/", new { area = "" })">
                        <button type="submit" class="text-left w-full text-base font-medium text-base-content hover:bg-base-200">
                            Sign out
                        </button>
                    </form>
                </div>
            </div>
        }
    </div>
</nav>

<script>
    // Toggle mobile menu
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuButton = document.querySelector('[aria-controls="mobile-menu"]');
        const mobileMenu = document.getElementById('mobile-menu');
        
        if (mobileMenuButton && mobileMenu) {
            mobileMenuButton.addEventListener('click', function() {
                const expanded = mobileMenuButton.getAttribute('aria-expanded') === 'true' || false;
                mobileMenuButton.setAttribute('aria-expanded', !expanded);
                mobileMenu.classList.toggle('hidden');
                mobileMenu.classList.toggle('block');
            });
        }
    });
</script>
