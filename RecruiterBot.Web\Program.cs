using Microsoft.AspNetCore.Http.Features;
using Microsoft.Extensions.AI;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using OllamaSharp;
using OpenAI;
using RecruiterBot.Infrastructure.Data;
using RecruiterBot.Infrastructure.Extensions;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Web.Middleware;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using Serilog;
using Serilog.Events;

// Configure Serilog early to capture startup logs
Log.Logger = new LoggerConfiguration()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
    .Enrich.FromLogContext()
    .WriteTo.Console()
    .CreateBootstrapLogger();

try
{
    Log.Information("Starting RecruiterBot application");

    var builder = WebApplication.CreateBuilder(args);

    // Configure Serilog
    builder.Host.UseSerilog((context, services, configuration) => configuration
        .ReadFrom.Configuration(context.Configuration)
        .ReadFrom.Services(services)
        .Enrich.FromLogContext()
        .Enrich.WithMachineName()
        .Enrich.WithProcessId()
        .Enrich.WithThreadId()
        .Enrich.WithEnvironmentName());

    // Ensure logs directory exists
    var logsDirectory = Path.Combine(builder.Environment.ContentRootPath, "logs");
    if (!Directory.Exists(logsDirectory))
    {
        Directory.CreateDirectory(logsDirectory);
        Log.Information("Created logs directory at {LogsDirectory}", logsDirectory);
    }

// Configure MongoDB serialization
BsonSerializer.RegisterSerializer(new GuidSerializer(BsonType.String));
BsonSerializer.RegisterSerializer(new DateTimeOffsetSerializer(BsonType.String));

var pack = new ConventionPack
{
    new CamelCaseElementNameConvention(),
    new IgnoreExtraElementsConvention(true),
    new EnumRepresentationConvention(BsonType.String)
};
ConventionRegistry.Register("CustomConventions", pack, t => true);

// Add services to the container.
var mongoDbSettings = builder.Configuration.GetSection("MongoDbSettings").Get<MongoDbSettings>();
if (mongoDbSettings == null)
{
    throw new InvalidOperationException("MongoDB configuration is missing. Please check your appsettings.json file.");
}

builder.Services.Configure<MongoDbSettings>(builder.Configuration.GetSection("MongoDbSettings"));

// Register MongoDB context
builder.Services.AddSingleton<MongoDbContext>();

// Register UserProfileService
builder.Services.AddScoped<UserProfileService>();

// Register encryption service
builder.Services.AddSingleton<IEncryptionService, AesEncryptionService>();
builder.Services.AddSingleton<RecruiterBot.Core.Services.IEncryptionService, AesEncryptionService>();

// Add MongoDB Identity services
builder.Services.AddMongoDbIdentity(builder.Configuration);

// Add authorization policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.AdminOnly, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.AdminOrCorp, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin, RecruiterBot.Core.Constants.UserRoles.CorpAdmin));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.AllRoles, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin, RecruiterBot.Core.Constants.UserRoles.CorpAdmin, RecruiterBot.Core.Constants.UserRoles.User, RecruiterBot.Core.Constants.UserRoles.Consultant));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.CanCreateUsers, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin, RecruiterBot.Core.Constants.UserRoles.CorpAdmin));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.CanManageUsers, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin, RecruiterBot.Core.Constants.UserRoles.CorpAdmin));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.ConsultantOnly, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Consultant));

    options.AddPolicy(RecruiterBot.Core.Constants.AuthorizationPolicies.CanManageInterviews, policy =>
        policy.RequireRole(RecruiterBot.Core.Constants.UserRoles.Admin, RecruiterBot.Core.Constants.UserRoles.CorpAdmin, RecruiterBot.Core.Constants.UserRoles.User, RecruiterBot.Core.Constants.UserRoles.Consultant));
});

// Configure cookie settings
builder.Services.ConfigureApplicationCookie(options =>
{
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
    options.LoginPath = "/Account/Login";
    options.LogoutPath = "/Account/Logout";
    options.AccessDeniedPath = "/Account/AccessDenied";
    options.SlidingExpiration = true;
});

// Configure file uploads
builder.Services.Configure<FormOptions>(options =>
{
    options.MultipartBodyLengthLimit = 10 * 1024 * 1024; // 10MB max file size
});

// Configure Kestrel to handle larger file uploads
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.Limits.MaxRequestBodySize = 20 * 1024 * 1024; // 20MB
});

// Add application services
builder.Services.AddScoped<UserService>();

// Register BotConfigurationService
builder.Services.Configure<MongoDBSettings>(builder.Configuration.GetSection("MongoDbSettings"));
builder.Services.AddScoped<IBotConfigurationService, BotConfigurationService>();

// Register Candidate services
builder.Services.AddScoped<ICandidateService, CandidateService>();

// Register Resume Parsing services
builder.Services.AddScoped<OllamaResumeParsingService>();
builder.Services.AddScoped<OpenAIResumeParsingService>();
builder.Services.AddScoped<IResumeParsingServiceFactory, ResumeParsingServiceFactory>();

// Register HttpClient for Ollama with 10-minute timeout
builder.Services.AddHttpClient("OllamaClient", (provider, client) =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    var baseUrl = configuration["Ollama:BaseUrl"] ?? "http://localhost:11434";

    client.BaseAddress = new Uri(baseUrl);
    client.Timeout = TimeSpan.FromMinutes(10);
});

// Register Ollama client
builder.Services.AddSingleton<IOllamaApiClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    var httpClientFactory = provider.GetRequiredService<IHttpClientFactory>();
    var modelName = configuration["Ollama:ModelName"] ?? "llama3.2";

    var httpClient = httpClientFactory.CreateClient("OllamaClient");
    return new OllamaApiClient(httpClient, modelName);
});

// Register OpenAI client
builder.Services.AddSingleton<IChatClient>(provider =>
{
    var configuration = provider.GetRequiredService<IConfiguration>();
    var apiKey = configuration["OpenAI:ApiKey"];
    var model = configuration["OpenAI:Model"] ?? "gpt-4o-mini";

    if (string.IsNullOrEmpty(apiKey))
    {
        throw new InvalidOperationException("OpenAI API key is not configured");
    }

    return new OpenAIClient(apiKey).AsChatClient(model);
});

// Register the main service based on environment
builder.Services.AddScoped<IResumeParsingService>(provider =>
{
    var factory = provider.GetRequiredService<IResumeParsingServiceFactory>();
    return factory.CreateService();
});

// Configure SendGrid settings and email service
builder.Services.Configure<SendGridSettings>(builder.Configuration.GetSection("SendGrid"));
builder.Services.AddScoped<IEmailService, SendGridEmailService>();

// Register Password Generator Service
builder.Services.AddScoped<IPasswordGeneratorService, PasswordGeneratorService>();

// Register Consultant Registration Service
builder.Services.AddScoped<IConsultantRegistrationService, ConsultantRegistrationService>();

// Register LLM Model Service
builder.Services.AddScoped<ILLMModelService, LLMModelService>();

// Register Interview Profile Service
builder.Services.AddScoped<IInterviewProfileService, InterviewProfileService>();

// Register Interview Service
builder.Services.AddScoped<IInterviewService, InterviewService>();

// Configure File Storage Options
builder.Services.Configure<RecruiterBot.Infrastructure.Configuration.FileStorageOptions>(
    builder.Configuration.GetSection(RecruiterBot.Infrastructure.Configuration.FileStorageOptions.SectionName));

    // Register File Storage Service
    var useGcpStorage = builder.Configuration.GetValue<bool>("GCP:Storage:Enabled", false);
    if (useGcpStorage)
    {
        builder.Services.AddScoped<IFileStorageService, GoogleCloudStorageService>();
        Log.Information("Using Google Cloud Storage for file storage");
    }
    else
    {
        builder.Services.AddScoped<IFileStorageService, LocalFileStorageService>();
        Log.Information("Using Local File Storage for file storage");
    }

// Add API controllers
builder.Services.AddControllers();

// Add Razor Pages with runtime compilation for development
var mvcBuilder = builder.Services.AddRazorPages();

if (builder.Environment.IsDevelopment())
{
// mvcBuilder.AddRazorRuntimeCompilation();
}

// Add session support
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

var app = builder.Build();

// Initialize roles and seed data
using (var scope = app.Services.CreateScope())
{
    var seedDataService = scope.ServiceProvider.GetRequiredService<RecruiterBot.Infrastructure.Services.ISeedDataService>();
    await seedDataService.SeedInitialDataAsync();
}

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Use password change middleware after authentication
app.UsePasswordChangeMiddleware();
app.UseSession();

// Add redirect for /Account/Manage to /Account/Manage/Index
app.UseEndpoints(endpoints =>
{
    endpoints.MapGet("/Account/Manage", context => 
    {
        context.Response.Redirect("/Account/Manage/Index");
        return Task.CompletedTask;
    });
});

    // Map API controllers
    app.MapControllers();

    // Map Razor Pages
    app.MapRazorPages();

    // Add a fallback route for SPA-style routing
    app.MapFallbackToPage("/Index");

    Log.Information("RecruiterBot application started successfully");
    app.Run();
}
catch (Exception ex)
{
    Log.Fatal(ex, "RecruiterBot application terminated unexpectedly");
}
finally
{
    Log.Information("RecruiterBot application is shutting down");
    Log.CloseAndFlush();
}
