@page "{id?}"
@model RecruiterBot.Web.Pages.Candidates.EditModel
@{
    ViewData["Title"] = "Edit Candidate";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="bi bi-person-gear me-2"></i>
                        Edit Candidate Information
                    </h3>
                    @if (Model.Candidate.ParsingStatus == RecruiterBot.Core.Models.ParsingStatus.Completed)
                    {
                        <span class="badge bg-success ms-2">
                            <i class="bi bi-check-circle me-1"></i>
                            AI Parsed
                        </span>
                    }
                    else if (Model.Candidate.ParsingStatus == RecruiterBot.Core.Models.ParsingStatus.Failed)
                    {
                        <span class="badge bg-warning text-dark ms-2">
                            <i class="bi bi-exclamation-triangle me-1"></i>
                            Manual Entry Required
                        </span>
                    }
                    else if (Model.Candidate.ParsingStatus == RecruiterBot.Core.Models.ParsingStatus.Manual)
                    {
                        <span class="badge bg-primary ms-2">
                            <i class="bi bi-pencil me-1"></i>
                            Manual Entry
                        </span>
                    }
                </div>
                <div class="card-body">
                    <!-- Resume Upload Section (Separate Form) -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="bi bi-upload me-2"></i>
                                        Upload New Resume
                                    </h6>
                                    <p class="card-text text-muted small">
                                        Upload a new resume to update candidate information automatically.
                                    </p>

                                    <form method="post" enctype="multipart/form-data" asp-page-handler="UploadResume">
                                        <input type="hidden" asp-for="Candidate.Id" />

                                        <div class="row">
                                            <div class="col-md-8">
                                                <div class="form-group">
                                                    <label asp-for="NewResumeFile" class="form-label">
                                                        <i class="bi bi-file-earmark-pdf me-1"></i>
                                                        New Resume File
                                                    </label>
                                                    <input asp-for="NewResumeFile"
                                                           class="form-control"
                                                           type="file"
                                                           accept=".pdf,.doc,.docx,.txt">
                                                    <span asp-validation-for="NewResumeFile" class="text-danger"></span>
                                                    <small class="form-text text-muted">
                                                        Supported formats: PDF, DOC, DOCX, TXT (Max size: 10MB)
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="col-md-4 d-flex align-items-end">
                                                <button type="submit" class="btn btn-outline-primary" id="uploadResumeBtn">
                                                    <i class="bi bi-upload me-2"></i>
                                                    Upload & Parse
                                                </button>
                                            </div>
                                        </div>

                                        @if (!Model.IsOllamaAvailable)
                                        {
                                            <div class="alert alert-warning mt-3">
                                                <i class="bi bi-exclamation-triangle me-2"></i>
                                                <strong>AI Service Unavailable:</strong>
                                                The Ollama AI service is not currently available. You can still upload resumes, but automatic parsing will not be available.
                                            </div>
                                        }

                                        <!-- Progress indicator (hidden by default) -->
                                        <div class="mt-3" id="uploadResumeProgressContainer" style="display: none;">
                                            <div class="alert alert-info">
                                                <div class="d-flex align-items-center">
                                                    <div class="spinner-border spinner-border-sm me-3" role="status">
                                                        <span class="visually-hidden">Loading...</span>
                                                    </div>
                                                    <div>
                                                        <strong>Uploading and parsing resume...</strong>
                                                        <br><small class="text-muted">This may take a few moments.</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Main Candidate Edit Form -->
                    <form method="post">
                        <input type="hidden" asp-for="Candidate.Id" />
                        <input type="hidden" asp-for="Candidate.CreatedDateUtc" />
                        <input type="hidden" asp-for="Candidate.CreatedBy" />
                        <input type="hidden" asp-for="Candidate.Tenant" />
                        <input type="hidden" asp-for="Candidate.OriginalResumeFilename" />
                        <input type="hidden" asp-for="Candidate.ResumeFilePath" />
                        <input type="hidden" asp-for="Candidate.ResumeText" />
                        <input type="hidden" asp-for="Candidate.ParsingStatus" />
                        <input type="hidden" asp-for="Candidate.ParsingError" value="NA" />
                        <input type="hidden" asp-for="Candidate.AiConfidenceScore" />
                        <input type="hidden" asp-for="Candidate.DeactivatedBy" value="NA" />
                        <input type="hidden" asp-for="Candidate.DeactivationReason" value="NA" />

                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        @if (!string.IsNullOrEmpty(TempData["WarningMessage"] as string))
                        {
                            <div class="alert alert-warning alert-dismissible fade show">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Validation Warning:</strong> @TempData["WarningMessage"]
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.Candidate.ParsingError))
                        {
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>Parsing Error:</strong> @Model.Candidate.ParsingError
                                <br><small>Please fill in the information manually.</small>
                            </div>
                        }

                        <!-- Personal Information -->
                        <div class="row">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-person me-2"></i>
                                    Personal Information
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.FirstName" class="form-label"></label>
                                    <input asp-for="Candidate.FirstName" class="form-control" />
                                    <span asp-validation-for="Candidate.FirstName" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.LastName" class="form-label"></label>
                                    <input asp-for="Candidate.LastName" class="form-control" />
                                    <span asp-validation-for="Candidate.LastName" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.Email" class="form-label"></label>
                                    <input asp-for="Candidate.Email" class="form-control" type="email" />
                                    <span asp-validation-for="Candidate.Email" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.Phone" class="form-label"></label>
                                    <input asp-for="Candidate.Phone" class="form-control" />
                                    <span asp-validation-for="Candidate.Phone" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label asp-for="Candidate.Address" class="form-label"></label>
                                    <input asp-for="Candidate.Address" class="form-control" />
                                    <span asp-validation-for="Candidate.Address" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Candidate.City" class="form-label"></label>
                                    <input asp-for="Candidate.City" class="form-control" />
                                    <span asp-validation-for="Candidate.City" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Candidate.State" class="form-label"></label>
                                    <input asp-for="Candidate.State" class="form-control" />
                                    <span asp-validation-for="Candidate.State" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Candidate.ZipCode" class="form-label"></label>
                                    <input asp-for="Candidate.ZipCode" class="form-control" />
                                    <span asp-validation-for="Candidate.ZipCode" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label asp-for="Candidate.Country" class="form-label"></label>
                                    <input asp-for="Candidate.Country" class="form-control" placeholder="e.g., USA, Canada, UK" />
                                    <span asp-validation-for="Candidate.Country" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.LinkedInUrl" class="form-label"></label>
                                    <input asp-for="Candidate.LinkedInUrl" class="form-control" type="url" />
                                    <span asp-validation-for="Candidate.LinkedInUrl" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.PortfolioUrl" class="form-label"></label>
                                    <input asp-for="Candidate.PortfolioUrl" class="form-control" type="url" />
                                    <span asp-validation-for="Candidate.PortfolioUrl" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Professional Information -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-briefcase me-2"></i>
                                    Professional Information
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.CurrentTitle" class="form-label"></label>
                                    <input asp-for="Candidate.CurrentTitle" class="form-control" />
                                    <span asp-validation-for="Candidate.CurrentTitle" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.CurrentCompany" class="form-label"></label>
                                    <input asp-for="Candidate.CurrentCompany" class="form-control" />
                                    <span asp-validation-for="Candidate.CurrentCompany" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label asp-for="Candidate.YearsOfExperience" class="form-label"></label>
                                    <input asp-for="Candidate.YearsOfExperience" class="form-control" type="number" min="0" max="50" />
                                    <span asp-validation-for="Candidate.YearsOfExperience" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label asp-for="SkillsText" class="form-label">Skills (comma-separated)</label>
                                    <textarea asp-for="SkillsText" class="form-control" rows="3" 
                                              placeholder="e.g., C#, JavaScript, React, SQL Server, Azure"></textarea>
                                    <span asp-validation-for="SkillsText" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-12">
                                <div class="form-group">
                                    <label asp-for="Candidate.Summary" class="form-label"></label>
                                    <textarea asp-for="Candidate.Summary" class="form-control" rows="4"></textarea>
                                    <span asp-validation-for="Candidate.Summary" class="text-danger"></span>
                                </div>
                            </div>
                        </div>

                        <!-- Work Experience -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-briefcase me-2"></i>
                                    Work Experience
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="addWorkExperience()">
                                        <i class="bi bi-plus me-1"></i>Add Experience
                                    </button>
                                </h5>
                                <div id="workExperienceContainer">
                                    @if (Model.Candidate.WorkExperience != null && Model.Candidate.WorkExperience.Any())
                                    {
                                        @for (int i = 0; i < Model.Candidate.WorkExperience.Count; i++)
                                        {
                                            <div class="work-experience-item border rounded p-3 mb-3" data-index="@i">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0">Experience #@(i + 1)</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeWorkExperience(@i)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].Company" />
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].Title" />
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].StartDate" />
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].EndDate" />
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].Description" />
                                                <input type="hidden" asp-for="Candidate.WorkExperience[i].Location" />

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">Job Title</label>
                                                            <input type="text" class="form-control" name="Candidate.WorkExperience[@i].Title" value="@Model.Candidate.WorkExperience[i].Title" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">Company</label>
                                                            <input type="text" class="form-control" name="Candidate.WorkExperience[@i].Company" value="@Model.Candidate.WorkExperience[i].Company" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">Start Date</label>
                                                            <input type="text" class="form-control" name="Candidate.WorkExperience[@i].StartDate" value="@Model.Candidate.WorkExperience[i].StartDate" placeholder="e.g., Jan 2020" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">End Date</label>
                                                            <input type="text" class="form-control" name="Candidate.WorkExperience[@i].EndDate" value="@Model.Candidate.WorkExperience[i].EndDate" placeholder="e.g., Present, Dec 2022" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">Location</label>
                                                            <input type="text" class="form-control" name="Candidate.WorkExperience[@i].Location" value="@Model.Candidate.WorkExperience[i].Location" placeholder="e.g., New York, NY" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-12">
                                                        <div class="form-group">
                                                            <label class="form-label">Description</label>
                                                            <textarea class="form-control" name="Candidate.WorkExperience[@i].Description" rows="3">@Model.Candidate.WorkExperience[i].Description</textarea>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- Education -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-mortarboard me-2"></i>
                                    Education
                                    <button type="button" class="btn btn-sm btn-outline-primary ms-2" onclick="addEducation()">
                                        <i class="bi bi-plus me-1"></i>Add Education
                                    </button>
                                </h5>
                                <div id="educationContainer">
                                    @if (Model.Candidate.Education != null && Model.Candidate.Education.Any())
                                    {
                                        @for (int i = 0; i < Model.Candidate.Education.Count; i++)
                                        {
                                            <div class="education-item border rounded p-3 mb-3" data-index="@i">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0">Education #@(i + 1)</h6>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeEducation(@i)">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </div>
                                                <input type="hidden" asp-for="Candidate.Education[i].Institution" />
                                                <input type="hidden" asp-for="Candidate.Education[i].Degree" />
                                                <input type="hidden" asp-for="Candidate.Education[i].FieldOfStudy" />
                                                <input type="hidden" asp-for="Candidate.Education[i].GraduationYear" />
                                                <input type="hidden" asp-for="Candidate.Education[i].GPA" />

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">Institution</label>
                                                            <input type="text" class="form-control" name="Candidate.Education[@i].Institution" value="@Model.Candidate.Education[i].Institution" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label class="form-label">Degree</label>
                                                            <input type="text" class="form-control" name="Candidate.Education[@i].Degree" value="@Model.Candidate.Education[i].Degree" placeholder="e.g., Bachelor of Science" />
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">Field of Study</label>
                                                            <input type="text" class="form-control" name="Candidate.Education[@i].FieldOfStudy" value="@Model.Candidate.Education[i].FieldOfStudy" placeholder="e.g., Computer Science" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">Graduation Year</label>
                                                            <input type="text" class="form-control" name="Candidate.Education[@i].GraduationYear" value="@Model.Candidate.Education[i].GraduationYear" placeholder="e.g., 2020" />
                                                        </div>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <div class="form-group">
                                                            <label class="form-label">GPA</label>
                                                            <input type="text" class="form-control" name="Candidate.Education[@i].GPA" value="@Model.Candidate.Education[i].GPA" placeholder="e.g., 3.8/4.0" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    }
                                </div>
                            </div>
                        </div>

                        <!-- Certifications -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-award me-2"></i>
                                    Certifications
                                </h5>
                                <div class="form-group">
                                    <label class="form-label">Certifications (one per line)</label>
                                    <textarea id="certificationsText" class="form-control" rows="4"
                                              placeholder="e.g.,&#10;Microsoft Azure Fundamentals&#10;AWS Certified Solutions Architect&#10;PMP Certification">@(Model.Candidate.Certifications != null ? string.Join("\n", Model.Candidate.Certifications) : "")</textarea>
                                    <small class="form-text text-muted">Enter each certification on a new line</small>
                                </div>
                            </div>
                        </div>

                        <!-- Status Management -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <h5 class="text-primary mb-3">
                                    <i class="bi bi-gear me-2"></i>
                                    Status Management
                                </h5>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input asp-for="Candidate.IsActive" class="form-check-input" type="checkbox" />
                                        <label asp-for="Candidate.IsActive" class="form-check-label">
                                            Active Candidate
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">Uncheck to deactivate this candidate</small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <button type="submit" class="btn btn-primary" id="saveBtn">
                                    <i class="bi bi-save me-2"></i>
                                    Save Candidate
                                </button>
                                <a asp-page="./Index" class="btn btn-secondary ms-2">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    Back to Candidates
                                </a>
                                @if (!string.IsNullOrEmpty(Model.Candidate.ResumeFilePath))
                                {
                                    <a href="@Model.Candidate.ResumeFilePath" target="_blank" class="btn btn-info ms-2">
                                        <i class="bi bi-file-earmark-pdf me-2"></i>
                                        View Resume
                                    </a>
                                }
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <script>
        $(document).ready(function() {
            var originalSaveButtonHtml = $('#saveBtn').html();
            var originalUploadButtonHtml = $('#uploadResumeBtn').html();

            // Show loading state when main form is submitted (only if validation passes)
            $('form:not([asp-page-handler])').on('submit', function(e) {
                var $form = $(this);
                var $btn = $('#saveBtn');

                // Check if form is valid using HTML5 validation
                if (!this.checkValidity()) {
                    // Form is invalid, don't show loading state
                    return true; // Let the browser handle validation display
                }

                // Check for any existing validation errors from server-side validation
                var hasValidationErrors = $form.find('.text-danger').filter(function() {
                    return $(this).text().trim() !== '';
                }).length > 0;

                if (hasValidationErrors) {
                    // There are validation errors, don't show loading state
                    return true;
                }

                // Form appears to be valid, show loading state
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Saving...');

                // Set a timeout to reset the button if something goes wrong
                setTimeout(function() {
                    if ($btn.prop('disabled')) {
                        $btn.prop('disabled', false);
                        $btn.html(originalSaveButtonHtml);
                    }
                }, 30000); // Reset after 30 seconds if still loading
            });

            // Handle resume upload form submission
            $('form[asp-page-handler="UploadResume"]').on('submit', function(e) {
                var $form = $(this);
                var $btn = $('#uploadResumeBtn');
                var $progressContainer = $('#uploadResumeProgressContainer');

                // Check if form is valid
                if (!this.checkValidity()) {
                    return true; // Let the browser handle validation display
                }

                // Check for file selection
                var fileInput = $form.find('input[type="file"]')[0];
                if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
                    alert('Please select a file to upload.');
                    return false;
                }

                // Disable button and show spinner
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Processing...');

                // Show progress container
                $progressContainer.show();

                // Set a timeout to reset the button if something goes wrong
                setTimeout(function() {
                    if ($btn.prop('disabled')) {
                        $btn.prop('disabled', false);
                        $btn.html(originalUploadButtonHtml);
                        $progressContainer.hide();
                    }
                }, 60000); // Reset after 60 seconds for file upload
            });

            // Reset button states if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $saveBtn = $('#saveBtn');
                var $uploadBtn = $('#uploadResumeBtn');
                var $progressContainer = $('#uploadResumeProgressContainer');

                $saveBtn.prop('disabled', false);
                $saveBtn.html(originalSaveButtonHtml);
                $uploadBtn.prop('disabled', false);
                $uploadBtn.html(originalUploadButtonHtml);
                $progressContainer.hide();
            }

            // File input validation for resume upload
            $('input[asp-for="NewResumeFile"]').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var fileSize = file.size / 1024 / 1024; // Convert to MB
                    var maxSize = 10; // 10MB

                    if (fileSize > maxSize) {
                        alert('File size must be less than ' + maxSize + 'MB');
                        $(this).val('');
                        return;
                    }

                    var allowedTypes = ['application/pdf', 'application/msword',
                                      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                      'text/plain'];

                    if (!allowedTypes.includes(file.type)) {
                        alert('Please select a valid file type (PDF, DOC, DOCX, or TXT)');
                        $(this).val('');
                        return;
                    }
                }
            });

            // Real-time validation feedback to clear validation errors when user starts typing
            $('form:not([asp-page-handler]) input, form:not([asp-page-handler]) textarea, form:not([asp-page-handler]) select').on('input change', function() {
                var $field = $(this);
                var $errorSpan = $field.siblings('.text-danger');

                // Clear the validation error message when user starts typing
                if ($errorSpan.length > 0 && $errorSpan.text().trim() !== '') {
                    $errorSpan.text('');
                }

                // Remove invalid class if present
                $field.removeClass('is-invalid');

                // Check if field is now valid
                if (this.checkValidity()) {
                    $field.removeClass('is-invalid').addClass('is-valid');
                } else {
                    $field.removeClass('is-valid');
                }
            });

            // Handle form submission to process certifications
            $('form:not([asp-page-handler])').on('submit', function() {
                // Convert certifications textarea to hidden inputs
                var certificationsText = $('#certificationsText').val();
                var certifications = certificationsText.split('\n').filter(function(cert) {
                    return cert.trim() !== '';
                }).map(function(cert) {
                    return cert.trim();
                });

                // Remove existing certification hidden inputs
                $('input[name^="Candidate.Certifications"]').remove();

                // Add new certification hidden inputs
                for (var i = 0; i < certifications.length; i++) {
                    $('<input>').attr({
                        type: 'hidden',
                        name: 'Candidate.Certifications[' + i + ']',
                        value: certifications[i]
                    }).appendTo(this);
                }
            });
        });

        // Work Experience Functions
        function addWorkExperience() {
            var container = document.getElementById('workExperienceContainer');
            var index = container.children.length;

            var html = `
                <div class="work-experience-item border rounded p-3 mb-3" data-index="${index}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Experience #${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeWorkExperience(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Job Title</label>
                                <input type="text" class="form-control" name="Candidate.WorkExperience[${index}].Title" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Company</label>
                                <input type="text" class="form-control" name="Candidate.WorkExperience[${index}].Company" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Start Date</label>
                                <input type="text" class="form-control" name="Candidate.WorkExperience[${index}].StartDate" placeholder="e.g., Jan 2020" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">End Date</label>
                                <input type="text" class="form-control" name="Candidate.WorkExperience[${index}].EndDate" placeholder="e.g., Present, Dec 2022" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Location</label>
                                <input type="text" class="form-control" name="Candidate.WorkExperience[${index}].Location" placeholder="e.g., New York, NY" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="Candidate.WorkExperience[${index}].Description" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', html);
        }

        function removeWorkExperience(index) {
            var item = document.querySelector(`.work-experience-item[data-index="${index}"]`);
            if (item) {
                item.remove();
                // Reindex remaining items
                reindexWorkExperience();
            }
        }

        function reindexWorkExperience() {
            var items = document.querySelectorAll('.work-experience-item');
            items.forEach(function(item, newIndex) {
                item.setAttribute('data-index', newIndex);
                item.querySelector('h6').textContent = `Experience #${newIndex + 1}`;
                item.querySelector('button').setAttribute('onclick', `removeWorkExperience(${newIndex})`);

                // Update input names
                var inputs = item.querySelectorAll('input, textarea');
                inputs.forEach(function(input) {
                    var name = input.getAttribute('name');
                    if (name && name.includes('WorkExperience[')) {
                        var newName = name.replace(/WorkExperience\[\d+\]/, `WorkExperience[${newIndex}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }

        // Education Functions
        function addEducation() {
            var container = document.getElementById('educationContainer');
            var index = container.children.length;

            var html = `
                <div class="education-item border rounded p-3 mb-3" data-index="${index}">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">Education #${index + 1}</h6>
                        <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeEducation(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Institution</label>
                                <input type="text" class="form-control" name="Candidate.Education[${index}].Institution" />
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Degree</label>
                                <input type="text" class="form-control" name="Candidate.Education[${index}].Degree" placeholder="e.g., Bachelor of Science" />
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Field of Study</label>
                                <input type="text" class="form-control" name="Candidate.Education[${index}].FieldOfStudy" placeholder="e.g., Computer Science" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">Graduation Year</label>
                                <input type="text" class="form-control" name="Candidate.Education[${index}].GraduationYear" placeholder="e.g., 2020" />
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="form-label">GPA</label>
                                <input type="text" class="form-control" name="Candidate.Education[${index}].GPA" placeholder="e.g., 3.8/4.0" />
                            </div>
                        </div>
                    </div>
                </div>
            `;

            container.insertAdjacentHTML('beforeend', html);
        }

        function removeEducation(index) {
            var item = document.querySelector(`.education-item[data-index="${index}"]`);
            if (item) {
                item.remove();
                // Reindex remaining items
                reindexEducation();
            }
        }

        function reindexEducation() {
            var items = document.querySelectorAll('.education-item');
            items.forEach(function(item, newIndex) {
                item.setAttribute('data-index', newIndex);
                item.querySelector('h6').textContent = `Education #${newIndex + 1}`;
                item.querySelector('button').setAttribute('onclick', `removeEducation(${newIndex})`);

                // Update input names
                var inputs = item.querySelectorAll('input, textarea');
                inputs.forEach(function(input) {
                    var name = input.getAttribute('name');
                    if (name && name.includes('Education[')) {
                        var newName = name.replace(/Education\[\d+\]/, `Education[${newIndex}]`);
                        input.setAttribute('name', newName);
                    }
                });
            });
        }
    </script>
}
