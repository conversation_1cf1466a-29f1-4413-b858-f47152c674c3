// Custom JavaScript for RecruiterBot

document.addEventListener('DOMContentLoaded', function() {
    // Initialize any custom JavaScript here
    console.log('RecruiterBot application initialized');
    
    // Example: Toggle dark/light mode
    const themeToggle = document.getElementById('theme-toggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
        });
    }
    
    // Restore theme preference
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
});

// Add any additional utility functions here
function showToast(message, type = 'info') {
    // Implementation for showing toast notifications
    console.log(`${type}: ${message}`);
}
