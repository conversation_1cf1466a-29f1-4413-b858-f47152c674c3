using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Models;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.Account
{
    [AllowAnonymous]
    public class ResetPasswordModel : PageModel
    {
        private readonly UserManager<User> _userManager;
        private readonly ILogger<ResetPasswordModel> _logger;

        public ResetPasswordModel(UserManager<User> userManager, ILogger<ResetPasswordModel> logger)
        {
            _userManager = userManager;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new InputModel();

        public class InputModel
        {
            public string UserId { get; set; } = string.Empty;
            public string Token { get; set; } = string.Empty;

            [Required]
            [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
            [DataType(DataType.Password)]
            [Display(Name = "New Password")]
            public string Password { get; set; } = string.Empty;

            [DataType(DataType.Password)]
            [Display(Name = "Confirm New Password")]
            [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
            public string ConfirmPassword { get; set; } = string.Empty;
        }

        public IActionResult OnGet(string userId, string token)
        {
            if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(token))
            {
                TempData["ErrorMessage"] = "Invalid password reset link. Please request a new password reset.";
                return RedirectToPage("./ForgotPassword");
            }

            Input.UserId = userId;
            Input.Token = token;
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            if (!ModelState.IsValid)
            {
                return Page();
            }

            if (string.IsNullOrEmpty(Input.UserId) || string.IsNullOrEmpty(Input.Token))
            {
                TempData["ErrorMessage"] = "Invalid password reset request. Please request a new password reset.";
                return RedirectToPage("./ForgotPassword");
            }

            try
            {
                var user = await _userManager.FindByIdAsync(Input.UserId);
                if (user == null)
                {
                    // Don't reveal that the user does not exist
                    TempData["SuccessMessage"] = "Your password has been reset successfully. You can now sign in with your new password.";
                    return RedirectToPage("./Login");
                }

                var result = await _userManager.ResetPasswordAsync(user, Input.Token, Input.Password);
                if (result.Succeeded)
                {
                    _logger.LogInformation("Password reset successfully for user {UserId}", user.Id);
                    TempData["SuccessMessage"] = "Your password has been reset successfully. You can now sign in with your new password.";
                    return RedirectToPage("./Login");
                }

                foreach (var error in result.Errors)
                {
                    if (error.Code == "InvalidToken")
                    {
                        ModelState.AddModelError(string.Empty, "The password reset link is invalid or has expired. Please request a new password reset.");
                    }
                    else
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                }

                _logger.LogWarning("Password reset failed for user {UserId}. Errors: {Errors}", 
                    Input.UserId, string.Join(", ", result.Errors.Select(e => e.Description)));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resetting password for user {UserId}", Input.UserId);
                ModelState.AddModelError(string.Empty, "An error occurred while resetting your password. Please try again.");
            }

            return Page();
        }
    }
}
