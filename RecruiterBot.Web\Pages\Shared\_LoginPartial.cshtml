@using Microsoft.AspNetCore.Identity
@using RecruiterBot.Core.Models
@inject SignInManager<User> SignInManager
@inject UserManager<User> UserManager

@if (SignInManager.IsSignedIn(User))
{
    <div class="dropdown">
        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
            @if (!string.IsNullOrEmpty(UserManager.GetUserAsync(User).Result?.ProfileImageUrl))
            {
                <img src="@UserManager.GetUserAsync(User).Result.ProfileImageUrl" alt="Profile" class="rounded-circle me-2" style="width: 32px; height: 32px; object-fit: cover;">
            }
            else
            {
                <div class="bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style="width: 32px; height: 32px; font-size: 0.9rem;">
                    @UserManager.GetUserAsync(User).Result?.DisplayName?[0].ToString().ToUpper()
                </div>
            }
            <span class="d-none d-lg-inline">@UserManager.GetUserAsync(User).Result?.DisplayName</span>
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li><a class="dropdown-item" href="/Account/Manage"><i class="bi bi-person me-2"></i>Profile</a></li>
            <li><a class="dropdown-item" href="/Account/Manage/ChangePassword"><i class="bi bi-key me-2"></i>Change Password</a></li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form class="form-inline" asp-page="/Account/Logout" method="post">
                    <button type="submit" class="dropdown-item"><i class="bi bi-box-arrow-right me-2"></i>Logout</button>
                    @Html.AntiForgeryToken()
                </form>
            </li>
        </ul>
    </div>
}
else
{
    <div class="d-flex gap-2">
        <a class="btn btn-outline-primary" asp-page="/Account/Login">Login</a>
        <a class="btn btn-primary" asp-page="/Account/Register">Sign Up</a>
    </div>
}
