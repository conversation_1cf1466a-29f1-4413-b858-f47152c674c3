namespace RecruiterBot.Core.Constants
{
    public static class TechnologyOptions
    {
        public static readonly List<string> ProgrammingLanguagesAndTechnologies = new List<string>
        {
            // Programming Languages
            "JavaScript",
            "Python",
            "Java",
            "C#",
            "C++",
            "C",
            "TypeScript",
            "P<PERSON>",
            "Ruby",
            "Go",
            "Rust",
            "Swift",
            "Kotlin",
            "Scala",
            "R",
            "MATLAB",
            "Perl",
            "Objective-C",
            "Dart",
            "Elixir",
            "F#",
            "Haskell",
            "Clojure",
            "Lua",
            "Shell/Bash",
            "PowerShell",
            "SQL",
            "NoSQL",
            
            // Frontend Frameworks & Libraries
            "React",
            "Angular",
            "Vue.js",
            "Svelte",
            "jQuery",
            "Bootstrap",
            "Tailwind CSS",
            "Material-UI",
            "Ant Design",
            "Chakra UI",
            "Next.js",
            "Nuxt.js",
            "Gatsby",
            "Ember.js",
            "Backbone.js",
            
            // Backend Frameworks
            "Node.js",
            "Express.js",
            "Django",
            "Flask",
            "FastAPI",
            "Spring Boot",
            "Spring Framework",
            "ASP.NET Core",
            "ASP.NET",
            "Ruby on Rails",
            "Laravel",
            "Symfony",
            "CodeIgniter",
            "Gin",
            "Echo",
            "Fiber",
            "Actix",
            "Rocket",
            "NestJS",
            "Koa.js",
            "Hapi.js",
            
            // Mobile Development
            "React Native",
            "Flutter",
            "Xamarin",
            "Ionic",
            "Cordova/PhoneGap",
            "Unity",
            "Unreal Engine",
            
            // Cloud Platforms
            "AWS",
            "Microsoft Azure",
            "Google Cloud Platform (GCP)",
            "IBM Cloud",
            "Oracle Cloud",
            "DigitalOcean",
            "Heroku",
            "Vercel",
            "Netlify",
            
            // Databases
            "MySQL",
            "PostgreSQL",
            "MongoDB",
            "Redis",
            "SQLite",
            "Microsoft SQL Server",
            "Oracle Database",
            "Cassandra",
            "DynamoDB",
            "Firebase",
            "Elasticsearch",
            "Neo4j",
            "CouchDB",
            "InfluxDB",
            
            // DevOps & Tools
            "Docker",
            "Kubernetes",
            "Jenkins",
            "GitLab CI/CD",
            "GitHub Actions",
            "Terraform",
            "Ansible",
            "Chef",
            "Puppet",
            "Vagrant",
            "Apache",
            "Nginx",
            "Linux",
            "Windows Server",
            "macOS",
            
            // Version Control
            "Git",
            "GitHub",
            "GitLab",
            "Bitbucket",
            "SVN",
            "Mercurial",
            
            // Testing Frameworks
            "Jest",
            "Mocha",
            "Jasmine",
            "Cypress",
            "Selenium",
            "Playwright",
            "JUnit",
            "TestNG",
            "PyTest",
            "RSpec",
            "PHPUnit",
            "xUnit",
            "MSTest",
            
            // Data Science & AI/ML
            "TensorFlow",
            "PyTorch",
            "Scikit-learn",
            "Pandas",
            "NumPy",
            "Matplotlib",
            "Seaborn",
            "Jupyter",
            "Apache Spark",
            "Hadoop",
            "Kafka",
            "Airflow",
            "MLflow",
            "Kubeflow",
            
            // Other Technologies
            "GraphQL",
            "REST API",
            "SOAP",
            "gRPC",
            "WebSocket",
            "Microservices",
            "Serverless",
            "Blockchain",
            "Ethereum",
            "Solidity",
            "Web3",
            "AR/VR",
            "IoT",
            "Machine Learning",
            "Artificial Intelligence",
            "Data Analytics",
            "Big Data",
            "Cybersecurity",
            "DevSecOps"
        };

        public static List<string> GetSortedTechnologies()
        {
            return ProgrammingLanguagesAndTechnologies.OrderBy(t => t).ToList();
        }

        public static List<string> GetPopularTechnologies()
        {
            return new List<string>
            {
                "JavaScript",
                "Python",
                "Java",
                "C#",
                "TypeScript",
                "React",
                "Node.js",
                "Angular",
                "Vue.js",
                "Spring Boot",
                "ASP.NET Core",
                "Django",
                "Flask",
                "Express.js",
                "AWS",
                "Docker",
                "Kubernetes",
                "MySQL",
                "PostgreSQL",
                "MongoDB",
                "Git"
            };
        }
    }
}
