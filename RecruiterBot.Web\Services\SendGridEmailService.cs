using Microsoft.Extensions.Options;
using RecruiterBot.Web.Models;
using SendGrid;
using SendGrid.Helpers.Mail;
using System;
using System.Threading.Tasks;
using RecruiterBot.Infrastructure.Services;
using Microsoft.Extensions.Logging;

namespace RecruiterBot.Web.Services
{
    public class SendGridEmailService : IEmailService
    {
        private readonly SendGridSettings _settings;
        private readonly ILogger<SendGridEmailService> _logger;
        private readonly ISendGridClient _sendGridClient;

        public SendGridEmailService(
            IOptions<SendGridSettings> settings,
            ILogger<SendGridEmailService> logger)
        {
            _settings = settings.Value;
            _logger = logger;
            _sendGridClient = new SendGridClient(_settings.ApiKey);
        }

        public async Task<bool> SendEmailAsync(string toEmail, string subject, string htmlContent, string? plainTextContent = null)
        {
            try
            {
                var from = new EmailAddress(_settings.FromEmail, _settings.FromName);
                var to = new EmailAddress(toEmail);
                
                var msg = MailHelper.CreateSingleEmail(
                    from, 
                    to, 
                    subject, 
                    plainTextContent ?? htmlContent, 
                    htmlContent);

                var response = await _sendGridClient.SendEmailAsync(msg);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Email sent successfully to {Email} with subject '{Subject}'", toEmail, subject);
                    return true;
                }
                else
                {
                    var responseBody = await response.Body.ReadAsStringAsync();
                    _logger.LogError("Failed to send email to {Email}. Status: {StatusCode}, Response: {Response}", 
                        toEmail, response.StatusCode, responseBody);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {Email} with subject '{Subject}'", toEmail, subject);
                return false;
            }
        }

        public async Task<bool> SendEmailVerificationAsync(string toEmail, string userName, string verificationUrl)
        {
            var subject = "Verify your email address - RecruiterBot";
            
            var htmlContent = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Email Verification</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #007bff; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 30px; background-color: #f8f9fa; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Welcome to RecruiterBot!</h1>
        </div>
        <div class='content'>
            <h2>Hi {userName},</h2>
            <p>Thank you for registering with RecruiterBot. To complete your registration and start using our AI-powered recruitment platform, please verify your email address.</p>
            
            <p>Click the button below to verify your email:</p>
            
            <a href='{verificationUrl}' class='button'>Verify Email Address</a>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href='{verificationUrl}'>{verificationUrl}</a></p>
            
            <p>This verification link will expire in 24 hours for security reasons.</p>
            
            <p>If you didn't create an account with RecruiterBot, please ignore this email.</p>
            
            <p>Best regards,<br>The RecruiterBot Team</p>
        </div>
        <div class='footer'>
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>";

            var plainTextContent = $@"
Welcome to RecruiterBot!

Hi {userName},

Thank you for registering with RecruiterBot. To complete your registration and start using our AI-powered recruitment platform, please verify your email address.

Please click the following link to verify your email:
{verificationUrl}

This verification link will expire in 24 hours for security reasons.

If you didn't create an account with RecruiterBot, please ignore this email.

Best regards,
The RecruiterBot Team

This is an automated email. Please do not reply to this message.";

            return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
        }

        public async Task<bool> SendPasswordResetAsync(string toEmail, string userName, string resetUrl)
        {
            var subject = "Reset your password - RecruiterBot";
            
            var htmlContent = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Password Reset</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background-color: #dc3545; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 30px; background-color: #f8f9fa; }}
        .button {{ display: inline-block; padding: 12px 30px; background-color: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
        .warning {{ background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0; }}
    </style>
</head>
<body>
    <div class='container'>
        <div class='header'>
            <h1>Password Reset Request</h1>
        </div>
        <div class='content'>
            <h2>Hi {userName},</h2>
            <p>We received a request to reset your password for your RecruiterBot account.</p>
            
            <p>Click the button below to reset your password:</p>
            
            <a href='{resetUrl}' class='button'>Reset Password</a>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p><a href='{resetUrl}'>{resetUrl}</a></p>
            
            <div class='warning'>
                <strong>Security Notice:</strong>
                <ul>
                    <li>This password reset link will expire in 1 hour for security reasons.</li>
                    <li>If you didn't request a password reset, please ignore this email.</li>
                    <li>Your password will remain unchanged until you create a new one.</li>
                </ul>
            </div>
            
            <p>If you continue to have problems, please contact our support team.</p>
            
            <p>Best regards,<br>The RecruiterBot Team</p>
        </div>
        <div class='footer'>
            <p>This is an automated email. Please do not reply to this message.</p>
        </div>
    </div>
</body>
</html>";

            var plainTextContent = $@"
Password Reset Request

Hi {userName},

We received a request to reset your password for your RecruiterBot account.

Please click the following link to reset your password:
{resetUrl}

Security Notice:
- This password reset link will expire in 1 hour for security reasons.
- If you didn't request a password reset, please ignore this email.
- Your password will remain unchanged until you create a new one.

If you continue to have problems, please contact our support team.

Best regards,
The RecruiterBot Team

This is an automated email. Please do not reply to this message.";

            return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
        }

        public async Task<bool> SendAccountActivationAsync(string toEmail, string userName, string temporaryPassword, string activationUrl, string createdByRole)
        {
            var isRegistration = createdByRole == "REGISTRATION";
            var isConsultant = createdByRole == "CONSULTANT";
            var subject = isRegistration ? "Complete Your Registration - RecruiterBot" :
                         isConsultant ? "Welcome to RecruiterBot - Consultant Account Created" :
                         "Account Activation Required - RecruiterBot";
            var roleDisplayName = createdByRole == "ADMIN" ? "Administrator" :
                                 createdByRole == "REGISTRATION" ? "Registration" :
                                 createdByRole == "CONSULTANT" ? "Consultant" :
                                 "Corporate Administrator";

            var htmlContent = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Account Activation - RecruiterBot</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
        .credentials {{ background: #fff; border: 2px solid #007bff; border-radius: 8px; padding: 20px; margin: 20px 0; }}
        .btn {{ display: inline-block; background: #007bff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }}
        .btn:hover {{ background: #0056b3; }}
        .warning {{ background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin: 15px 0; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>🚀 Welcome to RecruiterBot!</h1>
        <p>{(isRegistration ? "Complete your registration" : "Your account has been created")}</p>
    </div>
    <div class='content'>
        <h2>Hello {userName},</h2>

        <p>{(isRegistration ? "Thank you for registering as a Corporate Administrator with RecruiterBot! To complete your registration and start using our AI-powered recruitment platform, you'll need to set up your account and create a secure password." : $"Your RecruiterBot account has been created by an {roleDisplayName}. To get started, you'll need to activate your account and set up your password.")}</p>

        <div class='credentials'>
            <h3>🔐 Your {(isRegistration ? "Registration" : "Temporary Login")} Credentials</h3>
            <p><strong>Email:</strong> {toEmail}</p>
            <p><strong>{(isRegistration ? "Registration Code" : "Temporary Password")}:</strong> <code style='background: #f1f3f4; padding: 4px 8px; border-radius: 4px; font-family: monospace;'>{temporaryPassword}</code></p>
        </div>

        <div class='warning'>
            <strong>⚠️ Important Security Notice:</strong><br>
            {(isRegistration ? "This is a registration code. You will use it to set up your permanent password during the registration process." : "This is a temporary password. You will be required to change it immediately upon your first login for security purposes.")}
        </div>

        <p>Click the button below to {(isRegistration ? "complete your registration" : "activate your account")}:</p>

        <a href='{activationUrl}' class='btn'>{(isRegistration ? "🎯 Complete Registration" : "🔓 Activate My Account")}</a>

        <p>Or copy and paste this link into your browser:</p>
        <p style='word-break: break-all; background: #f1f3f4; padding: 10px; border-radius: 4px;'>{activationUrl}</p>

        <h3>What happens next?</h3>
        <ol>
            <li>Click the {(isRegistration ? "registration" : "activation")} link above</li>
            <li>{(isRegistration ? "Enter your registration code" : "Log in with your temporary credentials")}</li>
            <li>You'll be prompted to create a new secure password</li>
            <li>Start using RecruiterBot's powerful features!</li>
        </ol>

        <p><strong>This {(isRegistration ? "registration" : "activation")} link will expire in 24 hours</strong> for security reasons.</p>

        <p>If you have any questions or need assistance, please {(isRegistration ? "contact our support team" : $"contact your {roleDisplayName}")}.</p>

        <p>Best regards,<br>The RecruiterBot Team</p>
    </div>
    <div class='footer'>
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>© 2024 RecruiterBot. All rights reserved.</p>
    </div>
</body>
</html>";

            var plainTextContent = $@"
Welcome to RecruiterBot!

Hello {userName},

{(isRegistration ? "Thank you for registering as a Corporate Administrator with RecruiterBot! To complete your registration and start using our AI-powered recruitment platform, you'll need to set up your account and create a secure password." :
  isConsultant ? "Welcome to RecruiterBot! Your consultant account has been created based on your candidate profile. You can now access the platform to manage your professional information and connect with potential opportunities." :
  $"Your RecruiterBot account has been created by an {roleDisplayName}. To get started, you'll need to activate your account and set up your password.")}

Your {(isRegistration ? "Registration" : isConsultant ? "Consultant Login" : "Temporary Login")} Credentials:
Email: {toEmail}
{(isRegistration ? "Registration Code" : isConsultant ? "Temporary Password" : "Temporary Password")}: {temporaryPassword}

IMPORTANT SECURITY NOTICE:
{(isRegistration ? "This is a registration code. You will use it to set up your permanent password during the registration process." : "This is a temporary password. You will be required to change it immediately upon your first login for security purposes.")}

To {(isRegistration ? "complete your registration" : "activate your account")}, please visit:
{activationUrl}

What happens next?
1. Click the {(isRegistration ? "registration" : "activation")} link above
2. {(isRegistration ? "Enter your registration code" : "Log in with your temporary credentials")}
3. You'll be prompted to create a new secure password
4. Start using RecruiterBot's powerful features!

This {(isRegistration ? "registration" : "activation")} link will expire in 24 hours for security reasons.

If you have any questions or need assistance, please {(isRegistration ? "contact our support team" : isConsultant ? "contact our support team or your recruiting contact" : $"contact your {roleDisplayName}")}.

Best regards,
The RecruiterBot Team

This is an automated email. Please do not reply to this message.
© 2024 RecruiterBot. All rights reserved.";

            return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
        }

        public async Task<bool> SendWelcomeEmailAsync(string toEmail, string userName, string loginUrl)
        {
            var subject = "Welcome to RecruiterBot - Account Activated!";

            var htmlContent = $@"
<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Welcome to RecruiterBot</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }}
        .content {{ background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px; }}
        .btn {{ display: inline-block; background: #28a745; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; margin: 10px 0; }}
        .btn:hover {{ background: #218838; }}
        .features {{ background: #fff; border-radius: 8px; padding: 20px; margin: 20px 0; }}
        .footer {{ text-align: center; margin-top: 30px; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class='header'>
        <h1>🎉 Welcome to RecruiterBot!</h1>
        <p>Your account is now active</p>
    </div>
    <div class='content'>
        <h2>Congratulations, {userName}!</h2>

        <p>Your RecruiterBot account has been successfully activated and your password has been set. You're now ready to explore our AI-powered recruitment platform!</p>

        <a href='{loginUrl}' class='btn'>🚀 Start Using RecruiterBot</a>

        <div class='features'>
            <h3>🌟 What you can do with RecruiterBot:</h3>
            <ul>
                <li><strong>AI Resume Parsing:</strong> Automatically extract candidate information from resumes</li>
                <li><strong>Candidate Management:</strong> Organize and track your candidate pipeline</li>
                <li><strong>Bot Configuration:</strong> Set up automated recruitment workflows</li>
                <li><strong>Team Collaboration:</strong> Work seamlessly with your recruitment team</li>
                <li><strong>Advanced Analytics:</strong> Get insights into your recruitment process</li>
            </ul>
        </div>

        <p>If you need any help getting started, don't hesitate to reach out to your administrator or check our documentation.</p>

        <p>Happy recruiting!</p>

        <p>Best regards,<br>The RecruiterBot Team</p>
    </div>
    <div class='footer'>
        <p>This is an automated email. Please do not reply to this message.</p>
        <p>© 2024 RecruiterBot. All rights reserved.</p>
    </div>
</body>
</html>";

            var plainTextContent = $@"
Welcome to RecruiterBot!

Congratulations, {userName}!

Your RecruiterBot account has been successfully activated and your password has been set. You're now ready to explore our AI-powered recruitment platform!

Start using RecruiterBot: {loginUrl}

What you can do with RecruiterBot:
- AI Resume Parsing: Automatically extract candidate information from resumes
- Candidate Management: Organize and track your candidate pipeline
- Bot Configuration: Set up automated recruitment workflows
- Team Collaboration: Work seamlessly with your recruitment team
- Advanced Analytics: Get insights into your recruitment process

If you need any help getting started, don't hesitate to reach out to your administrator or check our documentation.

Happy recruiting!

Best regards,
The RecruiterBot Team

This is an automated email. Please do not reply to this message.
© 2024 RecruiterBot. All rights reserved.";

            return await SendEmailAsync(toEmail, subject, htmlContent, plainTextContent);
        }
    }
}
