using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Core.Models
{
    [BsonIgnoreExtraElements]
    public class LLMModel
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("model_provider")]
        [BsonRepresentation(BsonType.String)]
        [Required(ErrorMessage = "Model provider is required")]
        [Display(Name = "Model Provider")]
        public ModelProvider ModelProvider { get; set; }

        [BsonElement("model_name")]
        [Required(ErrorMessage = "Model name is required")]
        [StringLength(100, ErrorMessage = "Model name cannot exceed 100 characters")]
        [Display(Name = "Model Name")]
        public string ModelName { get; set; }

        [BsonElement("display_name")]
        [Required(ErrorMessage = "Display name is required")]
        [StringLength(150, ErrorMessage = "Display name cannot exceed 150 characters")]
        [Display(Name = "Display Name")]
        public string DisplayName { get; set; }

        [BsonElement("description")]
        [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
        [Display(Name = "Description")]
        public string Description { get; set; }

        // This property stores the encrypted API key in the database
        [BsonElement("enc_api_key")]
        public string EncryptedApiKey { get; set; }

        // This property is used in the UI and forms (not stored in DB)
        [BsonIgnore]
        [Display(Name = "API Key")]
        [DataType(DataType.Password)]
        public string ApiKey { get; set; }

        [BsonElement("model_type")]
        [BsonRepresentation(BsonType.String)]
        [Display(Name = "Model Type")]
        public ModelType ModelType { get; set; } = ModelType.Free;

        [BsonElement("is_active")]
        [Display(Name = "Is Active")]
        public bool IsActive { get; set; } = true;

        [BsonElement("max_tokens")]
        [Range(1, 1000000, ErrorMessage = "Max tokens must be between 1 and 1,000,000")]
        [Display(Name = "Max Tokens")]
        public int MaxTokens { get; set; } = 4096;

        [BsonElement("created_at")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        [BsonElement("updated_at")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updated_by")]
        public string UpdatedBy { get; set; }

        // Helper property for display
        [BsonIgnore]
        public string ProviderDisplayName => ModelProvider.ToString();

        [BsonIgnore]
        public string TypeDisplayName => ModelType.ToString();

        [BsonIgnore]
        public string StatusDisplayName => IsActive ? "Active" : "Inactive";

        [BsonIgnore]
        public string StatusBadgeClass => IsActive ? "badge bg-success" : "badge bg-secondary";

        // Helper methods to handle encryption/decryption
        public void EncryptApiKey(RecruiterBot.Core.Services.IEncryptionService encryptionService)
        {
            if (!string.IsNullOrEmpty(ApiKey))
                EncryptedApiKey = encryptionService.Encrypt(ApiKey);
        }

        public void DecryptApiKey(RecruiterBot.Core.Services.IEncryptionService encryptionService)
        {
            if (!string.IsNullOrEmpty(EncryptedApiKey))
                ApiKey = encryptionService.Decrypt(EncryptedApiKey);
        }
    }

    public enum ModelProvider
    {
        [Display(Name = "OpenAI")]
        OpenAI,
        
        [Display(Name = "Google")]
        Google,
        
        [Display(Name = "Anthropic")]
        Anthropic,
        
        [Display(Name = "Ollama")]
        Ollama,
        
        [Display(Name = "Azure OpenAI")]
        AzureOpenAI,
        
        [Display(Name = "Hugging Face")]
        HuggingFace,
        
        [Display(Name = "Cohere")]
        Cohere,
        
        [Display(Name = "Other")]
        Other
    }

    public enum ModelType
    {
        [Display(Name = "Free")]
        Free,
        
        [Display(Name = "Paid")]
        Paid
    }
}
