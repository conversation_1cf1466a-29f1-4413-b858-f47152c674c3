using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IInterviewService
    {
        /// <summary>
        /// Creates a new interview
        /// </summary>
        /// <param name="interview">Interview to create</param>
        /// <param name="userId">ID of the user creating the interview</param>
        /// <returns>Created interview</returns>
        Task<Interview> CreateInterviewAsync(Interview interview, string userId);

        /// <summary>
        /// Gets an interview by ID
        /// </summary>
        /// <param name="interviewId">Interview ID</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <returns>Interview if found and user has access, null otherwise</returns>
        Task<Interview?> GetInterviewByIdAsync(string interviewId, string userId);

        /// <summary>
        /// Updates an existing interview
        /// </summary>
        /// <param name="interview">Interview to update</param>
        /// <param name="userId">ID of the user updating the interview</param>
        /// <returns>Updated interview</returns>
        Task<Interview> UpdateInterviewAsync(Interview interview, string userId);

        /// <summary>
        /// Deletes an interview (soft delete)
        /// </summary>
        /// <param name="interviewId">Interview ID</param>
        /// <param name="userId">ID of the user deleting the interview</param>
        /// <returns>True if deleted successfully</returns>
        Task<bool> DeleteInterviewAsync(string interviewId, string userId);

        /// <summary>
        /// Gets interviews created by a specific user (for Standard users)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>List of interviews</returns>
        Task<IEnumerable<Interview>> GetInterviewsByCreatorAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false);

        /// <summary>
        /// Gets interviews assigned to a specific consultant
        /// </summary>
        /// <param name="consultantId">Consultant ID</param>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>List of interviews assigned to the consultant</returns>
        Task<IEnumerable<Interview>> GetInterviewsByConsultantAsync(string consultantId, int skip = 0, int limit = 50, bool includeInactive = false);

        /// <summary>
        /// Gets interviews for a specific tenant (Corp Admin view)
        /// </summary>
        /// <param name="tenantId">Tenant ID (Corp Admin ID)</param>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>List of interviews in the tenant</returns>
        Task<IEnumerable<Interview>> GetInterviewsByTenantAsync(string tenantId, int skip = 0, int limit = 50, bool includeInactive = false);

        /// <summary>
        /// Gets count of interviews created by a specific user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>Count of interviews</returns>
        Task<long> GetInterviewCountByCreatorAsync(string userId, bool includeInactive = false);

        /// <summary>
        /// Gets count of interviews assigned to a specific consultant
        /// </summary>
        /// <param name="consultantId">Consultant ID</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>Count of interviews</returns>
        Task<long> GetInterviewCountByConsultantAsync(string consultantId, bool includeInactive = false);

        /// <summary>
        /// Gets count of interviews for a specific tenant
        /// </summary>
        /// <param name="tenantId">Tenant ID (Corp Admin ID)</param>
        /// <param name="includeInactive">Whether to include inactive interviews</param>
        /// <returns>Count of interviews</returns>
        Task<long> GetInterviewCountByTenantAsync(string tenantId, bool includeInactive = false);

        /// <summary>
        /// Updates interview status
        /// </summary>
        /// <param name="interviewId">Interview ID</param>
        /// <param name="status">New status</param>
        /// <param name="userId">ID of the user updating the status</param>
        /// <param name="notes">Optional notes for the status change</param>
        /// <returns>True if updated successfully</returns>
        Task<bool> UpdateInterviewStatusAsync(string interviewId, InterviewStatus status, string userId, string notes = null);

        /// <summary>
        /// Gets upcoming interviews for a consultant
        /// </summary>
        /// <param name="consultantId">Consultant ID</param>
        /// <param name="daysAhead">Number of days ahead to look</param>
        /// <returns>List of upcoming interviews</returns>
        Task<IEnumerable<Interview>> GetUpcomingInterviewsForConsultantAsync(string consultantId, int daysAhead = 7);

        /// <summary>
        /// Searches interviews by various criteria
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <param name="userId">ID of the requesting user</param>
        /// <param name="userRole">Role of the requesting user</param>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="limit">Maximum number of records to return</param>
        /// <returns>List of matching interviews</returns>
        Task<IEnumerable<Interview>> SearchInterviewsAsync(string searchTerm, string userId, string userRole, int skip = 0, int limit = 50);
    }
}
