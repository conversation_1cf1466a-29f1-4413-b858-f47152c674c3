using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using System;
using System.Threading.Tasks;

namespace RecruiterBot.Infrastructure.Services
{
    public class SeedDataService : ISeedDataService
    {
        private readonly UserManager<User> _userManager;
        private readonly IRoleManagementService _roleManagementService;
        private readonly IConfiguration _configuration;
        private readonly ILogger<SeedDataService> _logger;

        public SeedDataService(
            UserManager<User> userManager,
            IRoleManagementService roleManagementService,
            IConfiguration configuration,
            ILogger<SeedDataService> logger)
        {
            _userManager = userManager;
            _roleManagementService = roleManagementService;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task SeedInitialDataAsync()
        {
            try
            {
                // Initialize roles first
                await _roleManagementService.InitializeRolesAsync();

                // Check if admin user already exists
                var adminEmail = _configuration["SeedData:AdminEmail"] ?? "<EMAIL>";
                var existingAdmin = await _userManager.FindByEmailAsync(adminEmail);

                if (existingAdmin == null)
                {
                    // Create the initial admin user
                    var adminUser = new User
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        FirstName = "System",
                        LastName = "Administrator",
                        EmailConfirmed = true,
                        IsActive = true,
                        IsActivated = true,
                        MustChangePassword = false,
                        IsTemporaryPassword = false,
                        PasswordChangedAt = DateTime.UtcNow
                    };

                    var adminPassword = _configuration["SeedData:AdminPassword"] ?? "Admin123!";
                    var result = await _userManager.CreateAsync(adminUser, adminPassword);

                    if (result.Succeeded)
                    {
                        // Assign Admin role
                        var roleAssigned = await _roleManagementService.AssignRoleToUserAsync(adminUser.Id, UserRoles.Admin);
                        
                        if (roleAssigned)
                        {
                            _logger.LogInformation("Initial admin user created successfully: {Email}", adminEmail);
                            _logger.LogWarning("Default admin credentials - Email: {Email}, Password: {Password}", adminEmail, adminPassword);
                        }
                        else
                        {
                            _logger.LogError("Failed to assign Admin role to initial user");
                            await _userManager.DeleteAsync(adminUser);
                        }
                    }
                    else
                    {
                        _logger.LogError("Failed to create initial admin user: {Errors}", 
                            string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    _logger.LogInformation("Admin user already exists, checking if properties need updating");

                    // Check if existing admin user needs property updates
                    bool needsUpdate = false;
                    if (!existingAdmin.IsActivated)
                    {
                        existingAdmin.IsActivated = true;
                        needsUpdate = true;
                        _logger.LogInformation("Setting IsActivated = true for existing admin user");
                    }

                    if (existingAdmin.MustChangePassword)
                    {
                        existingAdmin.MustChangePassword = false;
                        needsUpdate = true;
                        _logger.LogInformation("Setting MustChangePassword = false for existing admin user");
                    }

                    if (existingAdmin.IsTemporaryPassword)
                    {
                        existingAdmin.IsTemporaryPassword = false;
                        needsUpdate = true;
                        _logger.LogInformation("Setting IsTemporaryPassword = false for existing admin user");
                    }

                    if (!existingAdmin.PasswordChangedAt.HasValue)
                    {
                        existingAdmin.PasswordChangedAt = DateTime.UtcNow;
                        needsUpdate = true;
                        _logger.LogInformation("Setting PasswordChangedAt for existing admin user");
                    }

                    if (needsUpdate)
                    {
                        var updateResult = await _userManager.UpdateAsync(existingAdmin);
                        if (updateResult.Succeeded)
                        {
                            _logger.LogInformation("Successfully updated existing admin user properties");
                        }
                        else
                        {
                            _logger.LogError("Failed to update existing admin user properties: {Errors}",
                                string.Join(", ", updateResult.Errors.Select(e => e.Description)));
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Admin user properties are already correct, no update needed");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding initial data");
            }
        }
    }
}
