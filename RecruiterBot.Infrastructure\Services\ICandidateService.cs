using RecruiterBot.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecruiterBot.Infrastructure.Services
{
    public interface ICandidateService
    {
        Task<Candidate> CreateCandidateAsync(Candidate candidate, string userId);
        Task<Candidate> UpdateCandidateAsync(Candidate candidate, string userId);
        Task<Candidate> GetCandidateAsync(string candidateId, string userId);
        Task<List<Candidate>> GetCandidatesAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false);
        Task<bool> DeleteCandidateAsync(string candidateId, string userId);
        Task<bool> DeactivateCandidateAsync(string candidateId, string userId, string reason = null);
        Task<bool> ActivateCandidateAsync(string candidateId, string userId);
        Task<long> GetCandidateCountAsync(string userId, bool includeInactive = false);
        Task<List<Candidate>> GetTeamCandidatesAsync(string corpAdminId, int skip = 0, int limit = 50, bool includeInactive = false);
        Task<List<Candidate>> GetCandidatesForUsersAsync(IEnumerable<string> userIds, int skip = 0, int limit = 50, bool includeInactive = false);
        Task<long> GetTeamCandidateCountAsync(string corpAdminId, bool includeInactive = false);
        Task<List<Candidate>> GetUserOwnCandidatesAsync(string userId, int skip = 0, int limit = 50, bool includeInactive = false);
        Task<long> GetUserOwnCandidateCountAsync(string userId, bool includeInactive = false);
    }
}
