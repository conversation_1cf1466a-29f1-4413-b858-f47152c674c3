using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;
using System.Text.Json;

namespace RecruiterBot.Web.Pages.Consultant.InterviewProfiles
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class EditModel : PageModel
    {
        private readonly IInterviewProfileService _interviewProfileService;
        private readonly ILogger<EditModel> _logger;

        public EditModel(IInterviewProfileService interviewProfileService, ILogger<EditModel> logger)
        {
            _interviewProfileService = interviewProfileService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public class InputModel
        {
            public string Id { get; set; } = string.Empty;

            [Required(ErrorMessage = "About You section is required")]
            [StringLength(1000, ErrorMessage = "About You cannot exceed 1000 characters")]
            [Display(Name = "About You")]
            public string AboutYou { get; set; } = string.Empty;

            [Display(Name = "Primary Technology")]
            public string PrimaryTechnology { get; set; } = string.Empty;

            // JSON fields for complex data
            public string ExperiencesJson { get; set; } = "[]";
            public string AchievementsJson { get; set; } = "[]";

            // Helper properties to deserialize JSON
            public List<WorkExperienceInput> Experiences
            {
                get
                {
                    try
                    {
                        return JsonSerializer.Deserialize<List<WorkExperienceInput>>(ExperiencesJson) ?? new List<WorkExperienceInput>();
                    }
                    catch
                    {
                        return new List<WorkExperienceInput>();
                    }
                }
            }

            public List<string> Achievements
            {
                get
                {
                    try
                    {
                        return JsonSerializer.Deserialize<List<string>>(AchievementsJson) ?? new List<string>();
                    }
                    catch
                    {
                        return new List<string>();
                    }
                }
            }
        }

        public class WorkExperienceInput
        {
            public string CompanyName { get; set; } = string.Empty;
            public string StartDate { get; set; } = string.Empty; // YYYY-MM format from month input
            public string EndDate { get; set; } = string.Empty;   // YYYY-MM format from month input
            public string ProjectDescription { get; set; } = string.Empty;
            public List<string> TechnologiesUsed { get; set; } = new List<string>();
            public bool IsCurrentProject { get; set; }
        }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile ID is required." });
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                var profile = await _interviewProfileService.GetProfileByIdAsync(id, consultantId);
                if (profile == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile not found or you don't have access to it." });
                }

                // Populate the input model
                Input = new InputModel
                {
                    Id = profile.Id,
                    AboutYou = profile.AboutYou,
                    PrimaryTechnology = profile.PrimaryTechnology,
                    AchievementsJson = JsonSerializer.Serialize(profile.Achievements),
                    ExperiencesJson = JsonSerializer.Serialize(ConvertFromWorkExperiences(profile.Experiences))
                };

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit interview profile page for ID {ProfileId}", id);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the profile." });
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                // Custom validation for JSON fields
                ValidateJsonFields();

                if (!ModelState.IsValid)
                {
                    return Page();
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                // Create the updated interview profile
                var profile = new InterviewProfile
                {
                    Id = Input.Id,
                    AboutYou = Input.AboutYou.Trim(),
                    PrimaryTechnology = Input.PrimaryTechnology?.Trim() ?? string.Empty,
                    Achievements = Input.Achievements.Where(ach => !string.IsNullOrWhiteSpace(ach)).ToList(),
                    Experiences = ConvertToWorkExperiences(Input.Experiences)
                };

                var updatedProfile = await _interviewProfileService.UpdateProfileAsync(profile, consultantId);

                if (updatedProfile == null)
                {
                    ModelState.AddModelError(string.Empty, "Profile not found or you don't have permission to update it.");
                    return Page();
                }

                _logger.LogInformation("Consultant {ConsultantId} updated interview profile {ProfileId}", 
                    consultantId, updatedProfile.Id);

                return RedirectToPage("./Index", new { SuccessMessage = "Interview profile updated successfully!" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating interview profile {ProfileId}", Input.Id);
                ModelState.AddModelError(string.Empty, "An error occurred while updating the profile. Please try again.");
                return Page();
            }
        }

        private void ValidateJsonFields()
        {
            // Validate experiences - only validate if user has started filling out the experience
            var experiences = Input.Experiences;
            for (int i = 0; i < experiences.Count; i++)
            {
                var exp = experiences[i];

                // Skip validation for completely empty experiences
                bool hasAnyContent = !string.IsNullOrWhiteSpace(exp.CompanyName) ||
                                   !string.IsNullOrWhiteSpace(exp.StartDate) ||
                                   !string.IsNullOrWhiteSpace(exp.EndDate) ||
                                   !string.IsNullOrWhiteSpace(exp.ProjectDescription) ||
                                   (exp.TechnologiesUsed?.Any(t => !string.IsNullOrWhiteSpace(t)) == true);

                if (!hasAnyContent)
                    continue;

                // If user started filling out experience, validate required fields
                if (string.IsNullOrWhiteSpace(exp.CompanyName))
                {
                    ModelState.AddModelError($"Experience[{i}].CompanyName", "Company name is required");
                }

                if (string.IsNullOrWhiteSpace(exp.StartDate))
                {
                    ModelState.AddModelError($"Experience[{i}].StartDate", "Start date is required");
                }

                if (!exp.IsCurrentProject && !string.IsNullOrWhiteSpace(exp.EndDate))
                {
                    if (DateTime.TryParse($"{exp.StartDate}-01", out var startDate) &&
                        DateTime.TryParse($"{exp.EndDate}-01", out var endDate))
                    {
                        if (endDate < startDate)
                        {
                            ModelState.AddModelError($"Experience[{i}].EndDate", "End date cannot be before start date");
                        }
                    }
                }

                if (!string.IsNullOrWhiteSpace(exp.ProjectDescription) && exp.ProjectDescription.Length > 2000)
                {
                    ModelState.AddModelError($"Experience[{i}].ProjectDescription", "Project description cannot exceed 2000 characters");
                }
            }

            // Validate that at least some content is provided
            if (string.IsNullOrWhiteSpace(Input.AboutYou) &&
                string.IsNullOrWhiteSpace(Input.PrimaryTechnology) &&
                !experiences.Any() &&
                !Input.Achievements.Any())
            {
                ModelState.AddModelError(string.Empty, "Please provide at least some profile information");
            }
        }

        private List<InterviewWorkExperience> ConvertToWorkExperiences(List<WorkExperienceInput> inputs)
        {
            var experiences = new List<InterviewWorkExperience>();

            foreach (var input in inputs)
            {
                if (string.IsNullOrWhiteSpace(input.CompanyName) || string.IsNullOrWhiteSpace(input.StartDate))
                    continue;

                var experience = new InterviewWorkExperience
                {
                    CompanyName = input.CompanyName.Trim(),
                    ProjectDescription = input.ProjectDescription?.Trim() ?? string.Empty,
                    TechnologiesUsed = input.TechnologiesUsed?.Where(t => !string.IsNullOrWhiteSpace(t)).ToList() ?? new List<string>(),
                    IsCurrentProject = input.IsCurrentProject
                };

                // Parse start date
                if (DateTime.TryParse($"{input.StartDate}-01", out var startDate))
                {
                    experience.StartDate = startDate;
                }
                else
                {
                    continue; // Skip invalid start date
                }

                // Parse end date
                if (!input.IsCurrentProject && !string.IsNullOrWhiteSpace(input.EndDate))
                {
                    if (DateTime.TryParse($"{input.EndDate}-01", out var endDate))
                    {
                        experience.EndDate = endDate;
                    }
                }

                experiences.Add(experience);
            }

            return experiences.OrderByDescending(e => e.StartDate).ToList();
        }

        private List<WorkExperienceInput> ConvertFromWorkExperiences(List<InterviewWorkExperience> experiences)
        {
            return experiences.Select(exp => new WorkExperienceInput
            {
                CompanyName = exp.CompanyName,
                StartDate = exp.StartDate.ToString("yyyy-MM"),
                EndDate = exp.EndDate?.ToString("yyyy-MM") ?? string.Empty,
                ProjectDescription = exp.ProjectDescription,
                TechnologiesUsed = exp.TechnologiesUsed,
                IsCurrentProject = exp.IsCurrentProject
            }).ToList();
        }
    }
}
