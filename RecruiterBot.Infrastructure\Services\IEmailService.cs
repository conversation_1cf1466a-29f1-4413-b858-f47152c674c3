using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IEmailService
    {
        Task<bool> SendEmailAsync(string toEmail, string subject, string htmlContent, string? plainTextContent = null);
        Task<bool> SendEmailVerificationAsync(string toEmail, string userName, string verificationUrl);
        Task<bool> SendPasswordResetAsync(string toEmail, string userName, string resetUrl);
        Task<bool> SendAccountActivationAsync(string toEmail, string userName, string temporaryPassword, string activationUrl, string createdByRole);
        Task<bool> SendWelcomeEmailAsync(string toEmail, string userName, string loginUrl);
        Task<bool> SendInterviewAssignmentAsync(string consultant<PERSON><PERSON>, string consultantName, Interview interview, string createdByName);
    }
}
