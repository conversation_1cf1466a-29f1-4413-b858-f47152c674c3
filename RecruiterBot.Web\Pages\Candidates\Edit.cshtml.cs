using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System;
using System.ComponentModel.DataAnnotations;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Threading.Tasks;
using UglyToad.PdfPig;

namespace RecruiterBot.Web.Pages.Candidates
{
    [Authorize(Policy = RecruiterBot.Core.Constants.AuthorizationPolicies.AllRoles)]
    public class EditModel : PageModel
    {
        private readonly ICandidateService _candidateService;
        private readonly IResumeParsingService _resumeParsingService;
        private readonly IWebHostEnvironment _environment;
        private readonly IFileStorageService _fileStorageService;
        private readonly ILogger<EditModel> _logger;

        public EditModel(
            ICandidateService candidateService,
            IResumeParsingService resumeParsingService,
            IWebHostEnvironment environment,
            IFileStorageService fileStorageService,
            ILogger<EditModel> logger)
        {
            _candidateService = candidateService;
            _resumeParsingService = resumeParsingService;
            _environment = environment;
            _fileStorageService = fileStorageService;
            _logger = logger;
        }

        [BindProperty]
        public Candidate Candidate { get; set; }

        [BindProperty]
        [Display(Name = "Skills")]
        public string SkillsText { get; set; }

        [BindProperty]
        public IFormFile? NewResumeFile { get; set; }

        public bool IsOllamaAvailable { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            if (string.IsNullOrEmpty(id))
            {
                return NotFound();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                Candidate = await _candidateService.GetCandidateAsync(id, userId);
                if (Candidate == null)
                {
                    return NotFound();
                }

                // Convert skills list to comma-separated string for editing
                SkillsText = Candidate.Skills != null && Candidate.Skills.Any()
                    ? string.Join(", ", Candidate.Skills)
                    : string.Empty;

                IsOllamaAvailable = await _resumeParsingService.IsServiceAvailableAsync();

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving candidate {CandidateId} for user {UserId}", id, userId);
                TempData["ErrorMessage"] = "Error loading candidate information.";
                return RedirectToPage("./Index");
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            IsOllamaAvailable = await _resumeParsingService.IsServiceAvailableAsync();

            if (!ModelState.IsValid)
            {
                return Page();
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                // Convert comma-separated skills text back to list
                if (!string.IsNullOrWhiteSpace(SkillsText))
                {
                    Candidate.Skills = SkillsText
                        .Split(',')
                        .Select(s => s.Trim())
                        .Where(s => !string.IsNullOrEmpty(s))
                        .ToList();
                }
                else
                {
                    Candidate.Skills = new List<string>();
                }

                // Update the candidate
                await _candidateService.UpdateCandidateAsync(Candidate, userId);

                TempData["SuccessMessage"] = "Candidate information updated successfully!";
                return RedirectToPage("./Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating candidate {CandidateId} for user {UserId}", Candidate.Id, userId);
                ModelState.AddModelError(string.Empty, "An error occurred while saving the candidate information.");
                return Page();
            }
        }

        public async Task<IActionResult> OnPostUploadResumeAsync()
        {
            if (NewResumeFile == null || NewResumeFile.Length == 0)
            {
                ModelState.AddModelError("NewResumeFile", "Please select a resume file.");
                return await OnGetAsync(Candidate.Id);
            }

            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized();
            }

            try
            {
                // Validate file size (10MB max)
                const int maxFileSize = 10 * 1024 * 1024;
                if (NewResumeFile.Length > maxFileSize)
                {
                    ModelState.AddModelError("NewResumeFile", "File size must be less than 10MB.");
                    return await OnGetAsync(Candidate.Id);
                }

                // Validate file type
                var allowedExtensions = new[] { ".pdf", ".doc", ".docx", ".txt" };
                var fileExtension = Path.GetExtension(NewResumeFile.FileName).ToLowerInvariant();
                if (!allowedExtensions.Contains(fileExtension))
                {
                    ModelState.AddModelError("NewResumeFile", "Only PDF, DOC, DOCX, and TXT files are allowed.");
                    return await OnGetAsync(Candidate.Id);
                }

                // Upload the file to storage service
                var fileUrl = await _fileStorageService.UploadFileAsync(NewResumeFile, "resumes");

                // Extract text from the file
                string resumeText;
                try
                {
                    resumeText = await ExtractTextFromFileAsync(NewResumeFile, fileExtension);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error extracting text from file: {FileName}", NewResumeFile.FileName);
                    ModelState.AddModelError(string.Empty, "Error reading the file. Please ensure it's a valid document.");

                    // Clean up the uploaded file
                    await _fileStorageService.DeleteFileAsync(fileUrl);

                    return await OnGetAsync(Candidate.Id);
                }

                if (string.IsNullOrWhiteSpace(resumeText))
                {
                    ModelState.AddModelError(string.Empty, "No text could be extracted from the file. Please check the file format.");

                    // Clean up the uploaded file
                    await _fileStorageService.DeleteFileAsync(fileUrl);

                    return await OnGetAsync(Candidate.Id);
                }

                // Delete old resume file if it exists
                if (!string.IsNullOrEmpty(Candidate.ResumeFilePath))
                {
                    await _fileStorageService.DeleteFileAsync(Candidate.ResumeFilePath);
                }

                // Update candidate with new resume information
                Candidate.OriginalResumeFilename = NewResumeFile.FileName;
                Candidate.ResumeFilePath = fileUrl;
                Candidate.ResumeText = resumeText;
                Candidate.ParsingStatus = ParsingStatus.Pending;

                // Try to parse the resume with AI if available
                if (await _resumeParsingService.IsServiceAvailableAsync())
                {
                    try
                    {
                        var parsedCandidate = await _resumeParsingService.ParseResumeAsync(resumeText, NewResumeFile.FileName);

                        // Update candidate with parsed information (but keep existing data if parsed data is empty)
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.FirstName))
                            Candidate.FirstName = parsedCandidate.FirstName;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.LastName))
                            Candidate.LastName = parsedCandidate.LastName;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.Email))
                            Candidate.Email = parsedCandidate.Email;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.Phone))
                            Candidate.Phone = parsedCandidate.Phone;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.CurrentTitle))
                            Candidate.CurrentTitle = parsedCandidate.CurrentTitle;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.CurrentCompany))
                            Candidate.CurrentCompany = parsedCandidate.CurrentCompany;
                        if (!string.IsNullOrWhiteSpace(parsedCandidate.Summary))
                            Candidate.Summary = parsedCandidate.Summary;
                        if (parsedCandidate.Skills != null && parsedCandidate.Skills.Any())
                            Candidate.Skills = parsedCandidate.Skills;

                        Candidate.ParsingStatus = ParsingStatus.Completed;
                        Candidate.AiConfidenceScore = parsedCandidate.AiConfidenceScore;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error parsing resume with AI for candidate {CandidateId}", Candidate.Id);
                        Candidate.ParsingStatus = ParsingStatus.Failed;
                        Candidate.ParsingError = "AI parsing failed. Resume uploaded successfully.";
                    }
                }

                // Update the candidate in the database
                await _candidateService.UpdateCandidateAsync(Candidate, userId);

                TempData["SuccessMessage"] = "Resume uploaded and processed successfully!";
                return RedirectToPage("./Edit", new { id = Candidate.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading resume for candidate {CandidateId}", Candidate.Id);
                ModelState.AddModelError(string.Empty, "An error occurred while uploading the resume. Please try again.");
                return await OnGetAsync(Candidate.Id);
            }
        }

        private async Task<string> ExtractTextFromFileAsync(IFormFile file, string fileExtension)
        {
            switch (fileExtension)
            {
                case ".pdf":
                    return await ExtractTextFromPdfAsync(file);

                case ".doc":
                case ".docx":
                    // For now, we'll return a message indicating manual text extraction is needed
                    // In a production environment, you might want to use a library like DocumentFormat.OpenXml
                    throw new NotSupportedException("DOC/DOCX files are not yet supported. Please convert to PDF or TXT format.");

                default:
                    throw new NotSupportedException($"File type {fileExtension} is not supported.");
            }
        }


        private async Task<string> ExtractTextFromPdfAsync(IFormFile file)
        {
            try
            {
                using var stream = file.OpenReadStream();
                using var document = PdfDocument.Open(stream);
                var text = new StringBuilder();

                foreach (var page in document.GetPages())
                {
                    text.AppendLine(page.Text);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FileName}", file.FileName);
                throw new Exception("Error reading PDF file. The file may be corrupted or password-protected.");
            }
        }

        private string ExtractTextFromPdf(string filePath)
        {
            try
            {
                using var document = PdfDocument.Open(filePath);
                var text = new StringBuilder();

                foreach (var page in document.GetPages())
                {
                    text.AppendLine(page.Text);
                }

                return text.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error extracting text from PDF: {FilePath}", filePath);
                throw new Exception("Error reading PDF file. The file may be corrupted or password-protected.");
            }
        }
    }
}
