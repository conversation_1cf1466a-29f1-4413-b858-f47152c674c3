using RecruiterBot.Core.Models;

namespace RecruiterBot.Infrastructure.Services
{
    public interface ILLMModelService
    {
        /// <summary>
        /// Gets all LLM models
        /// </summary>
        /// <returns>Collection of all LLM models</returns>
        Task<IEnumerable<LLMModel>> GetAllModelsAsync();

        /// <summary>
        /// Gets all active LLM models
        /// </summary>
        /// <returns>Collection of active LLM models</returns>
        Task<IEnumerable<LLMModel>> GetActiveModelsAsync();

        /// <summary>
        /// Gets an LLM model by ID
        /// </summary>
        /// <param name="id">Model ID</param>
        /// <returns>LLM model if found, null otherwise</returns>
        Task<LLMModel?> GetModelByIdAsync(string id);

        /// <summary>
        /// Creates a new LLM model
        /// </summary>
        /// <param name="model">Model to create</param>
        /// <param name="userId">ID of the user creating the model</param>
        /// <returns>Created model</returns>
        Task<LLMModel> CreateModelAsync(LLMModel model, string userId);

        /// <summary>
        /// Updates an existing LLM model
        /// </summary>
        /// <param name="model">Model to update</param>
        /// <param name="userId">ID of the user updating the model</param>
        /// <returns>Updated model</returns>
        Task<LLMModel> UpdateModelAsync(LLMModel model, string userId);

        /// <summary>
        /// Deletes an LLM model
        /// </summary>
        /// <param name="id">ID of the model to delete</param>
        /// <returns>True if deleted successfully, false otherwise</returns>
        Task<bool> DeleteModelAsync(string id);

        /// <summary>
        /// Toggles the active status of an LLM model
        /// </summary>
        /// <param name="id">Model ID</param>
        /// <param name="userId">ID of the user making the change</param>
        /// <returns>True if updated successfully, false otherwise</returns>
        Task<bool> ToggleModelStatusAsync(string id, string userId);

        /// <summary>
        /// Gets models by provider
        /// </summary>
        /// <param name="provider">Model provider</param>
        /// <returns>Collection of models from the specified provider</returns>
        Task<IEnumerable<LLMModel>> GetModelsByProviderAsync(ModelProvider provider);

        /// <summary>
        /// Searches models by name or description
        /// </summary>
        /// <param name="searchTerm">Search term</param>
        /// <returns>Collection of matching models</returns>
        Task<IEnumerable<LLMModel>> SearchModelsAsync(string searchTerm);
    }
}
