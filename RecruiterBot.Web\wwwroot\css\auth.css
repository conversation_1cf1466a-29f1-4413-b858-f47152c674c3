/* Auth Pages Styling */
.auth-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.auth-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 420px;
    overflow: hidden;
    animation: fadeIn 0.5s ease-in-out;
}

.auth-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    padding: 2rem;
    text-align: center;
}

.auth-logo {
    width: 64px;
    height: 64px;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    padding: 12px;
}

.auth-title {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.auth-subtitle {
    opacity: 0.9;
    font-weight: 400;
}

.auth-body {
    padding: 2rem;
}

.auth-footer {
    text-align: center;
    padding: 1.5rem 2rem;
    border-top: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #6b7280;
}

.auth-footer a {
    color: #4f46e5;
    font-weight: 500;
    text-decoration: none;
    transition: color 0.2s;
}

.auth-footer a:hover {
    color: #4338ca;
    text-decoration: underline;
}

/* Form Elements */
.form-group {
    margin-bottom: 1.25rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #374151;
    font-size: 0.875rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #1f2937;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #4f46e5;
    outline: 0;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    font-size: 1rem;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    border: none;
}

.btn-primary {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    color: white;
    width: 100%;
    padding: 0.875rem 1.5rem;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #4338ca 0%, #6d28d9 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

.btn-google {
    background: white;
    color: #4b5563;
    border: 1px solid #d1d5db;
    width: 100%;
    padding: 0.75rem 1.5rem;
    margin-bottom: 1rem;
}

.btn-google:hover {
    background: #f9fafb;
    border-color: #9ca3af;
}

.divider {
    display: flex;
    align-items: center;
    text-align: center;
    margin: 1.5rem 0;
    color: #9ca3af;
    font-size: 0.875rem;
}

.divider::before,
.divider::after {
    content: '';
    flex: 1;
    border-bottom: 1px solid #e5e7eb;
}

.divider:not(:empty)::before {
    margin-right: 1rem;
}

.divider:not(:empty)::after {
    margin-left: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Adjustments */
@media (max-width: 480px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-body {
        padding: 1.5rem;
    }
    
    .auth-header {
        padding: 1.5rem 1rem;
    }
}
