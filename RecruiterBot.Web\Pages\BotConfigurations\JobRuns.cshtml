@page
@model RecruiterBot.Web.Pages.BotConfigurations.JobRunsModel
@{
    ViewData["Title"] = "All Job Runs";
}

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">All Job Runs</h1>
            <p class="text-muted mb-0">@Model.TotalJobRuns job runs across @Model.TotalBots bots</p>
        </div>
        <a asp-page="./Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to Bots
        </a>
    </div>

    @if (Model.JobRunsByBot.Any())
    {
        @foreach (var botGroup in Model.JobRunsByBot.OrderByDescending(x => x.Value.FirstOrDefault()?.RunDate ?? DateTime.MinValue))
        {
            var bot = botGroup.Key;
            var jobRuns = botGroup.Value;

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="m-0 font-weight-bold text-primary">@bot.Query</h6>
                            <small class="text-muted">
                                @jobRuns.Count job run@(jobRuns.Count != 1 ? "s" : "")
                                @if (jobRuns.Any())
                                {
                                    <span>• Last run: @jobRuns.First().RunDate.ToString("MMM dd, yyyy 'at' HH:mm")</span>
                                }
                            </small>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <a asp-page="./Details" asp-route-id="@bot.Id" class="btn btn-outline-info" title="View Bot Details">
                                <i class="bi bi-eye"></i>
                            </a>
                            <a asp-page="./Edit" asp-route-id="@bot.Id" class="btn btn-outline-primary" title="Edit Bot">
                                <i class="bi bi-pencil"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (jobRuns.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover table-sm" id="<EMAIL>">
                                <thead>
                                    <tr>
                                        <th>Run Date</th>
                                        <th>Posts Found</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var run in jobRuns.Take(10)) // Show only latest 10 runs per bot
                                    {
                                        <tr data-run-id="@run.Id" class="job-run-row">
                                            <td>@run.RunDate.ToString("MMM dd, yyyy 'at' HH:mm")</td>
                                            <td>
                                                <span class="badge bg-info">@run.Posts.Count posts</span>
                                            </td>
                                            <td class="status-cell">
                                                <div class="spinner-border spinner-border-sm text-primary d-none" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                                <i class="bi bi-chevron-down"></i>
                                            </td>
                                        </tr>
                                        <tr class="details-row">
                                            <td colspan="3" class="p-0">
                                                <div class="collapse" id="<EMAIL>" data-run-id="@run.Id">
                                                    <div class="p-3 border-top bg-light">
                                                        <div class="job-details-content">
                                                            <div class="text-center my-3">
                                                                <div class="spinner-border text-primary" role="status">
                                                                    <span class="visually-hidden">Loading...</span>
                                                                </div>
                                                                <p class="mt-2">Loading job run details...</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                    @if (jobRuns.Count > 10)
                                    {
                                        <tr>
                                            <td colspan="3" class="text-center text-muted py-2">
                                                <small>Showing latest 10 of @jobRuns.Count job runs</small>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="bi bi-calendar-x text-gray-300 mb-3" style="font-size: 2rem;"></i>
                            <h6 class="text-muted">No job runs found</h6>
                            <p class="text-muted small mb-0">This bot has not been run yet.</p>
                        </div>
                    }
                </div>
            </div>
        }
    }
    else
    {
        <div class="card shadow">
            <div class="card-body">
                <div class="text-center py-5">
                    <i class="bi bi-calendar-x text-gray-300 mb-4" style="font-size: 4rem;"></i>
                    <h4 class="text-muted">No job runs found</h4>
                    <p class="text-muted">You don't have any bots with job runs yet.</p>
                    <a asp-page="./Create" class="btn btn-primary mt-3">
                        <i class="bi bi-plus-circle me-2"></i>Create Your First Bot
                    </a>
                </div>
            </div>
        </div>
    }
</div>

<!-- Template for job run details -->
<template id="jobRunDetailsTemplate">
    <div class="job-run-details">
        <div class="details-content"></div>
    </div>
</template>

@section Styles {
    <style>
        .job-post {
            border-left: 4px solid #4e73df;
            margin-bottom: 1.5rem;
            padding-left: 1rem;
        }
        .job-post:last-child {
            margin-bottom: 0;
        }
        .skills-badge {
            margin-right: 0.3rem;
            margin-bottom: 0.3rem;
        }
        .visa-info {
            display: inline-block;
            margin-right: 1rem;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            console.log('Document ready - initializing job runs page');
            
            // Initialize all collapse elements
            $('.collapse').on('show.bs.collapse', function(e) {
                console.log('Collapse show event triggered');
                const $detailsDiv = $(this);
                const $row = $detailsDiv.closest('tr.details-row').prev('tr.job-run-row');
                
                if ($row.length && !$detailsDiv.data('loaded')) {
                    const runId = $row.data('run-id');
                    const $statusCell = $row.find('.status-cell');
                    const $spinner = $statusCell.find('.spinner-border');
                    const $icon = $statusCell.find('i');
                    
                    console.log('Loading job run details for ID:', runId);
                    loadJobRunDetails(runId, $detailsDiv, $spinner, $icon);
                }
                
                // Toggle the icon
                $row.find('.status-cell i')
                    .removeClass('bi-chevron-down')
                    .addClass('bi-chevron-up');
            });
            
            $('.collapse').on('hide.bs.collapse', function(e) {
                console.log('Collapse hide event triggered');
                const $row = $(this).closest('tr.details-row').prev('tr.job-run-row');
                
                // Toggle the icon
                $row.find('.status-cell i')
                    .removeClass('bi-chevron-up')
                    .addClass('bi-chevron-down');
            });
            
            // Handle click on job run rows
            $(document).on('click', 'tr.job-run-row', function(e) {
                console.log('Job run row clicked');
                
                // Don't do anything if the click was on a link or button inside the row
                if ($(e.target).is('a, button, :has(>a, >button)')) {
                    console.log('Click was on a link or button, ignoring');
                    return;
                }
                
                e.stopPropagation();
                e.preventDefault();
                
                const $row = $(this);
                const $detailsRow = $row.next('tr.details-row');
                
                if (!$detailsRow.length) {
                    console.error('Details row not found');
                    return;
                }
                
                const $detailsDiv = $detailsRow.find('.collapse');
                
                // Toggle the collapse
                console.log('Toggling details div');
                $detailsDiv.collapse('toggle');
            });
            
            // Make rows look clickable
            $('tr.job-run-row').css('cursor', 'pointer');
            
            // Load job run details
            function loadJobRunDetails(runId, $container, $spinner, $icon) {
                console.log('loadJobRunDetails called with runId:', runId);
                const $contentDiv = $container.find('.job-details-content');
                
                // Show loading state
                console.log('Showing loading state');
                $spinner.removeClass('d-none');
                $icon.addClass('d-none');
                
                // Get anti-forgery token from the page
                const $tokenElement = $('input[name="__RequestVerificationToken"]');
                const token = $tokenElement.val();
                console.log('Anti-forgery token found:', !!token);
                
                const apiUrl = `/api/JobRuns/${encodeURIComponent(runId)}`;
                console.log('Making AJAX request to:', apiUrl);
                
                // Make the AJAX request
                $.ajax({
                    url: apiUrl,
                    type: 'GET',
                    dataType: 'json',
                    headers: {
                        'RequestVerificationToken': token,
                        'Accept': 'application/json'
                    },
                    xhrFields: {
                        withCredentials: true
                    },
                    beforeSend: function(xhr) {
                        console.log('AJAX request started');
                        console.log('Request headers:', {
                            'RequestVerificationToken': !!token,
                            'Accept': 'application/json'
                        });
                    },
                    success: function(jobRun, status, xhr) {
                        console.log('AJAX success:', status);
                        console.log('Response headers:', xhr.getAllResponseHeaders());
                        console.log('Job run data received:', jobRun);
                        
                        if (!jobRun) {
                            throw new Error('No data returned from server');
                        }
                        
                        try {
                            renderJobRunDetails(jobRun, $contentDiv);
                            $container.attr('data-loaded', 'true');
                        } catch (e) {
                            console.error('Error rendering job details:', e);
                            throw e;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', {
                            status: status,
                            error: error,
                            statusCode: xhr.status,
                            statusText: xhr.statusText,
                            responseText: xhr.responseText
                        });
                        
                        let errorMessage = 'An unknown error occurred while loading job run details.';
                        
                        try {
                            const errorData = xhr.responseJSON || {};
                            errorMessage = errorData.message || errorMessage;
                            console.error('Error details:', errorData);
                        } catch (e) {
                            console.error('Error parsing error response:', e);
                        }
                        
                        $contentDiv.html(`
                            <div class="alert alert-danger">
                                <h5>Error loading job run details</h5>
                                <p class="mb-0">${escapeHtml(errorMessage)}</p>
                                <p class="mt-2 small text-muted">Status: ${xhr.status} ${xhr.statusText}</p>
                            </div>
                        `);
                    },
                    complete: function(xhr, status) {
                        console.log('AJAX request completed with status:', status);
                        // Hide spinner and show icon
                        $spinner.addClass('d-none');
                        $icon.removeClass('d-none');
                    }
                });
            }
            
            // Render job run details
            function renderJobRunDetails(jobRun, $container) {
                if (!jobRun || !$container) return;
                
                // Handle both camelCase and snake_case property names
                const posts = jobRun.posts || jobRun.Posts || [];
                
                if (posts.length === 0) {
                    $container.html('<div class="alert alert-info">No job posts found in this run.</div>');
                    return;
                }
                
                let html = '<div class="job-posts">';
                
                posts.forEach(post => {
                    const jobInfo = post.jobInformation;
                    
                    // Extract post URL - handle different property names and formats
                    const postUrl = post.url;
                    const title = jobInfo.jobTitle || 'Not Avauilable';
                    const location = jobInfo.jobLocation || 'N/A';
                    const description = post.text || 'No description available';
                    
                    // Build skills HTML if available
                    let skillsHtml = '';
                    if (Array.isArray(jobInfo.mandatorySkills) && jobInfo.mandatorySkills.length > 0) {
                        skillsHtml = `
                            <div class="mb-2">
                                <span class="text-muted">Skills:</span>
                                ${jobInfo.mandatorySkills.map(skill =>
                                    skill ? `<span class="badge bg-primary me-1">${escapeHtml(skill.toString().trim())}</span>` : ''
                                ).join('')}
                            </div>`;
                    }
                    
                    // Render job details
                    html += `
                    <hr/>
                        <div class="job-details">
                            <h5 class="mb-3">${escapeHtml(title)}</h5>
                            
                            <div class="mb-2">
                                <span class="text-muted">Location:</span> ${escapeHtml(location)}
                            </div>
                            
                            
                            
                            ${skillsHtml}
                            
                            <div class="mb-3">
                                <p class="mb-2 text-muted">Description:</p>
                                <div class="p-3 bg-light rounded">
                                    ${description}
                                </div>
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <a href="${escapeHtml(postUrl)}" target="_blank" rel="noopener noreferrer" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-box-arrow-up-right me-1"></i> View Original Post
                                </a>
                                <div>
                                    
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                $container.html(html);
            }
            
            // Helper function to format date
            function formatDate(dateString) {
                if (!dateString) return 'N/A';
                
                try {
                    const date = new Date(dateString);
                    if (isNaN(date.getTime())) return 'Invalid date';
                    
                    return date.toLocaleDateString('en-US', {
                        year: 'numeric',
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: true
                    });
                } catch (e) {
                    console.error('Error formatting date:', e);
                    return 'Invalid date';
                }
            }
            
            // Helper function to escape HTML
            function escapeHtml(unsafe) {
                if (typeof unsafe !== 'string') return unsafe;
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }
        });
    </script>
    <style>
        .job-run-row {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .job-run-row:hover {
            background-color: rgba(0, 0, 0, 0.03);
        }
        .job-run-row td {
            vertical-align: middle;
        }
        .status-cell {
            text-align: right;
            width: 50px;
            position: relative;
        }
        .status-cell .spinner-border {
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
        .job-post {
            border-left: 4px solid #4e73df;
            padding: 0.75rem 1rem;
            margin-bottom: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
        }
        .job-post:last-child {
            margin-bottom: 0;
        }
        .skills-badge {
            margin-right: 0.3rem;
            margin-bottom: 0.3rem;
            font-weight: 500;
            font-size: 0.75rem;
        }
        .visa-info {
            display: inline-block;
            margin-right: 1rem;
            font-size: 0.875rem;
            color: #6c757d;
        }
        .job-details-content {
            max-height: 400px;
            overflow-y: auto;
        }
        .loading-spinner {
            display: none;
            position: absolute;
            right: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    </style>
}
