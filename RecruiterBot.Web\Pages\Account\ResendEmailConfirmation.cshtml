@page
@model RecruiterBot.Web.Pages.Account.ResendEmailConfirmationModel
@{
    ViewData["Title"] = "Resend Email Confirmation";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        <i class="bi bi-envelope-check" style="font-size: 3rem; color: #007bff;"></i>
                        <h2 class="mt-3">Resend Email Confirmation</h2>
                        <p class="text-muted">Enter your email address to receive a new confirmation email.</p>
                    </div>

                    <form method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
                        
                        <div class="form-group mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" autocomplete="email" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" id="resendBtn">
                                <i class="bi bi-envelope me-2"></i>
                                Resend Confirmation Email
                            </button>
                        </div>
                    </form>

                    <div class="text-center mt-4">
                        <a asp-page="./Login" class="text-decoration-none">
                            <i class="bi bi-arrow-left me-1"></i>
                            Back to Login
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        $(document).ready(function() {
            var originalButtonHtml = $('#resendBtn').html();
            
            // Show loading state when form is submitted
            $('form').on('submit', function() {
                var $btn = $('#resendBtn');
                $btn.prop('disabled', true);
                $btn.html('<i class="spinner-border spinner-border-sm me-2" role="status"></i>Sending...');
            });
            
            // Reset button state if there are validation errors on page load
            if ($('.text-danger').length > 0 && $('.text-danger').text().trim() !== '') {
                var $btn = $('#resendBtn');
                $btn.prop('disabled', false);
                $btn.html(originalButtonHtml);
            }
        });
    </script>
}
