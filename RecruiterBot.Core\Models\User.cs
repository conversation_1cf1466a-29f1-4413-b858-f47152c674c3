using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;

namespace RecruiterBot.Core.Models
{
    [BsonIgnoreExtraElements]
    public class User : IdentityUser, IUserStore<User>, IUserPasswordStore<User>, IUserEmailStore<User>
    {
        public User()
        {
            Id = ObjectId.GenerateNewId().ToString();
            CreatedAt = DateTime.UtcNow;
            UpdatedAt = DateTime.UtcNow;
            SecurityStamp = Guid.NewGuid().ToString();
            ConcurrencyStamp = Guid.NewGuid().ToString();
            IsActive = true;
            EmailConfirmed = false; // Require email confirmation
        }

        // [Key]
        // [BsonId]
        // [BsonRepresentation(BsonType.ObjectId)]
        // public override string Id { get; set; }

        [Required]
        [BsonElement("createdAt")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime CreatedAt { get; set; }

        [BsonElement("updatedAt")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime UpdatedAt { get; set; }

        [Required]
        [MaxLength(50)]
        [BsonElement("firstName")]
        public string FirstName { get; set; }

        [Required]
        [MaxLength(50)]
        [BsonElement("lastName")]
        public string LastName { get; set; }

        [BsonElement("displayName")]
        public string DisplayName => $"{FirstName} {LastName}".Trim();

        [BsonElement("lastLoginAt")]
        public DateTime? LastLoginAt { get; set; }

        [BsonElement("isActive")]
        public bool IsActive { get; set; }

        [BsonElement("profileImageUrl")]
        public string ProfileImageUrl { get; set; }

        [BsonElement("timeZone")]
        public string TimeZone { get; set; } = "UTC";

        [BsonElement("preferences")]
        public UserPreferences Preferences { get; set; } = new UserPreferences();

        // Navigation properties
        [BsonElement("claims")]
        public virtual ICollection<IdentityUserClaim<string>> Claims { get; set; } = new List<IdentityUserClaim<string>>();

        [BsonElement("logins")]
        public virtual ICollection<IdentityUserLogin<string>> Logins { get; set; } = new List<IdentityUserLogin<string>>();

        [BsonElement("tokens")]
        public virtual ICollection<IdentityUserToken<string>> Tokens { get; set; } = new List<IdentityUserToken<string>>();

        [BsonElement("roles")]
        public virtual ICollection<string> Roles { get; set; } = new List<string>();

        [BsonElement("createdBy")]
        public string CreatedBy { get; set; }

        [BsonElement("tenant")]
        public string Tenant { get; set; }

        // Password management fields
        [BsonElement("mustChangePassword")]
        public bool MustChangePassword { get; set; } = false;

        [BsonElement("temporaryPassword")]
        public bool IsTemporaryPassword { get; set; } = false;

        [BsonElement("passwordChangedAt")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime? PasswordChangedAt { get; set; }

        [BsonElement("activationToken")]
        public string ActivationToken { get; set; }

        [BsonElement("activationTokenExpiry")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        public DateTime? ActivationTokenExpiry { get; set; }

        [BsonElement("isActivated")]
        public bool IsActivated { get; set; } = false;

        #region IUserStore Implementation

        public Task<IdentityResult> CreateAsync(User user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<IdentityResult> DeleteAsync(User user, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public void Dispose()
        {
            // Nothing to dispose
        }

        public Task<User> FindByIdAsync(string userId, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<User> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetNormalizedUserNameAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.NormalizedUserName);
        }

        public Task<string> GetUserIdAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Id);
        }

        public Task<string> GetUserNameAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.UserName);
        }

        public Task SetNormalizedUserNameAsync(User user, string normalizedName, CancellationToken cancellationToken)
        {
            user.NormalizedUserName = normalizedName;
            return Task.CompletedTask;
        }

        public Task SetUserNameAsync(User user, string userName, CancellationToken cancellationToken)
        {
            user.UserName = userName;
            return Task.CompletedTask;
        }

        public Task<IdentityResult> UpdateAsync(User user, CancellationToken cancellationToken)
        {
            user.UpdatedAt = DateTime.UtcNow;
            // In a real implementation, you would save the changes to the database here
            return Task.FromResult(IdentityResult.Success);
        }

        #endregion

        #region IUserPasswordStore Implementation

        public Task<string> GetPasswordHashAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.PasswordHash);
        }

        public Task<bool> HasPasswordAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(!string.IsNullOrEmpty(user.PasswordHash));
        }

        public Task SetPasswordHashAsync(User user, string passwordHash, CancellationToken cancellationToken)
        {
            user.PasswordHash = passwordHash;
            return Task.CompletedTask;
        }

        #endregion

        #region IUserEmailStore Implementation

        public Task<User> FindByEmailAsync(string normalizedEmail, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<string> GetEmailAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Email);
        }

        public Task<bool> GetEmailConfirmedAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.EmailConfirmed);
        }

        public Task<string> GetNormalizedEmailAsync(User user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.NormalizedEmail);
        }

        public Task SetEmailAsync(User user, string email, CancellationToken cancellationToken)
        {
            user.Email = email;
            return Task.CompletedTask;
        }

        public Task SetEmailConfirmedAsync(User user, bool confirmed, CancellationToken cancellationToken)
        {
            user.EmailConfirmed = confirmed;
            return Task.CompletedTask;
        }

        public Task SetNormalizedEmailAsync(User user, string normalizedEmail, CancellationToken cancellationToken)
        {
            user.NormalizedEmail = normalizedEmail;
            return Task.CompletedTask;
        }

        #endregion
    }
}
