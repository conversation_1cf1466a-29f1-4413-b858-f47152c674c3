using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace RecruiterBot.Web.Pages;

public class ContactModel : PageModel
{
    private readonly ILogger<ContactModel> _logger;

    public ContactModel(ILogger<ContactModel> logger)
    {
        _logger = logger;
    }

    [BindProperty]
    public ContactFormModel ContactForm { get; set; } = new();

    public string? SuccessMessage { get; set; }
    public string? ErrorMessage { get; set; }

    public void OnGet(string? inquiry = null)
    {
        if (!string.IsNullOrEmpty(inquiry))
        {
            ContactForm.InquiryType = inquiry switch
            {
                "enterprise" => "sales",
                "demo" => "demo",
                "support" => "support",
                _ => "other"
            };
        }
    }

    public async Task<IActionResult> OnPostAsync()
    {
        if (!ModelState.IsValid)
        {
            return Page();
        }

        try
        {
            // Here you would typically:
            // 1. Send an email notification to your support team
            // 2. Store the contact form submission in a database
            // 3. Send an auto-reply email to the user
            
            _logger.LogInformation("Contact form submitted by {Email} from {Company}", 
                ContactForm.Email, ContactForm.Company ?? "Unknown");

            // For now, we'll just log the submission and show a success message
            SuccessMessage = "Thank you for your message! We'll get back to you within 24 hours.";
            
            // Clear the form after successful submission
            ContactForm = new ContactFormModel();
            
            return Page();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing contact form submission");
            ErrorMessage = "Sorry, there was an error sending your message. Please try again or contact us directly.";
            return Page();
        }
    }
}

public class ContactFormModel
{
    [Required(ErrorMessage = "First name is required")]
    [Display(Name = "First Name")]
    public string FirstName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Last name is required")]
    [Display(Name = "Last Name")]
    public string LastName { get; set; } = string.Empty;

    [Required(ErrorMessage = "Email address is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid email address")]
    [Display(Name = "Email Address")]
    public string Email { get; set; } = string.Empty;

    [Display(Name = "Company")]
    public string? Company { get; set; }

    [Required(ErrorMessage = "Please select an inquiry type")]
    [Display(Name = "Inquiry Type")]
    public string InquiryType { get; set; } = string.Empty;

    [Required(ErrorMessage = "Message is required")]
    [MinLength(10, ErrorMessage = "Message must be at least 10 characters long")]
    [Display(Name = "Message")]
    public string Message { get; set; } = string.Empty;

    [Display(Name = "Subscribe to Newsletter")]
    public bool Newsletter { get; set; }
}
