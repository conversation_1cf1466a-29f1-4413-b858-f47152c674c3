@page
@model RecruiterBot.Web.Pages.Admin.LLMModels.CreateModel
@using RecruiterBot.Core.Constants
@using RecruiterBot.Core.Models
@{
    ViewData["Title"] = "Add New LLM Model";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-plus-circle me-2"></i>
                        Add New LLM Model
                    </h2>
                    <p class="text-muted mb-0">Configure a new Large Language Model for the platform</p>
                </div>
                <a asp-page="./Index" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-2"></i>
                    Back to Models
                </a>
            </div>

            <!-- Form -->
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card">
                        <div class="card-body">
                            <form method="post">
                                @Html.AntiForgeryToken()
                                
                                <!-- Error Summary -->
                                <div asp-validation-summary="All" class="alert alert-danger" role="alert"></div>

                                <!-- Basic Information -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary mb-3">
                                            <i class="bi bi-info-circle me-2"></i>Basic Information
                                        </h5>
                                    </div>
                                    
                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.ModelProvider" class="form-label">Model Provider *</label>
                                        <select asp-for="Input.ModelProvider" class="form-select" asp-items="@Model.ProviderOptions">
                                            <option value="">Select a provider...</option>
                                        </select>
                                        <span asp-validation-for="Input.ModelProvider" class="text-danger"></span>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.ModelType" class="form-label">Model Type</label>
                                        <select asp-for="Input.ModelType" class="form-select" asp-items="@Model.TypeOptions">
                                        </select>
                                        <span asp-validation-for="Input.ModelType" class="text-danger"></span>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.ModelName" class="form-label">Model Name *</label>
                                        <input asp-for="Input.ModelName" class="form-control" 
                                               placeholder="e.g., gpt-4o, claude-3-5-sonnet">
                                        <div class="form-text">The technical name of the model as used by the API</div>
                                        <span asp-validation-for="Input.ModelName" class="text-danger"></span>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.DisplayName" class="form-label">Display Name *</label>
                                        <input asp-for="Input.DisplayName" class="form-control" 
                                               placeholder="e.g., GPT-4 Omni, Claude 3.5 Sonnet">
                                        <div class="form-text">User-friendly name shown in the interface</div>
                                        <span asp-validation-for="Input.DisplayName" class="text-danger"></span>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <label asp-for="Input.Description" class="form-label">Description</label>
                                        <textarea asp-for="Input.Description" class="form-control" rows="3" 
                                                  placeholder="Describe the model's capabilities and use cases..."></textarea>
                                        <span asp-validation-for="Input.Description" class="text-danger"></span>
                                    </div>
                                </div>

                                <!-- Configuration -->
                                <div class="row mb-4">
                                    <div class="col-12">
                                        <h5 class="text-primary mb-3">
                                            <i class="bi bi-gear me-2"></i>Configuration
                                        </h5>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.ApiKey" class="form-label">API Key</label>
                                        <div class="input-group">
                                            <input asp-for="Input.ApiKey" type="password" class="form-control" 
                                                   placeholder="Enter API key (if required)">
                                            <button class="btn btn-outline-secondary" type="button" id="toggleApiKey">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">API key will be encrypted before storage</div>
                                        <span asp-validation-for="Input.ApiKey" class="text-danger"></span>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label asp-for="Input.MaxTokens" class="form-label">Max Tokens *</label>
                                        <input asp-for="Input.MaxTokens" type="number" class="form-control" 
                                               min="1" max="1000000" value="4096">
                                        <div class="form-text">Maximum number of tokens the model can process</div>
                                        <span asp-validation-for="Input.MaxTokens" class="text-danger"></span>
                                    </div>

                                    <div class="col-12 mb-3">
                                        <div class="form-check">
                                            <input asp-for="Input.IsActive" class="form-check-input" type="checkbox" checked>
                                            <label asp-for="Input.IsActive" class="form-check-label">
                                                Active
                                            </label>
                                            <div class="form-text">Only active models will be available for use</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions -->
                                <div class="row">
                                    <div class="col-12">
                                        <hr>
                                        <div class="d-flex justify-content-end gap-2">
                                            <a asp-page="./Index" class="btn btn-secondary">
                                                <i class="bi bi-x-circle me-2"></i>Cancel
                                            </a>
                                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                                <i class="bi bi-check-circle me-2"></i>
                                                <span class="btn-text">Create Model</span>
                                                <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    
    <script>
        // Toggle API key visibility
        document.getElementById('toggleApiKey').addEventListener('click', function() {
            const apiKeyInput = document.querySelector('input[name="Input.ApiKey"]');
            const icon = this.querySelector('i');
            
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });

        // Form submission with loading state
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const spinner = submitBtn.querySelector('.spinner-border');
            
            submitBtn.disabled = true;
            btnText.textContent = 'Creating...';
            spinner.classList.remove('d-none');
        });

        // Auto-generate display name from model name
        document.querySelector('input[name="Input.ModelName"]').addEventListener('input', function() {
            const displayNameInput = document.querySelector('input[name="Input.DisplayName"]');
            if (!displayNameInput.value) {
                // Simple transformation: replace hyphens with spaces and title case
                const displayName = this.value
                    .replace(/-/g, ' ')
                    .replace(/\b\w/g, l => l.toUpperCase());
                displayNameInput.value = displayName;
            }
        });

        // Provider-specific suggestions
        document.querySelector('select[name="Input.ModelProvider"]').addEventListener('change', function() {
            const modelNameInput = document.querySelector('input[name="Input.ModelName"]');
            const maxTokensInput = document.querySelector('input[name="Input.MaxTokens"]');
            
            // Clear previous values
            if (!modelNameInput.value) {
                switch(this.value) {
                    case 'OpenAI':
                        modelNameInput.placeholder = 'e.g., gpt-4o, gpt-4o-mini, gpt-3.5-turbo';
                        maxTokensInput.value = 128000;
                        break;
                    case 'Anthropic':
                        modelNameInput.placeholder = 'e.g., claude-3-5-sonnet, claude-3-haiku';
                        maxTokensInput.value = 200000;
                        break;
                    case 'Google':
                        modelNameInput.placeholder = 'e.g., gemini-2.0-flash-exp, gemini-1.5-pro';
                        maxTokensInput.value = 1000000;
                        break;
                    case 'Ollama':
                        modelNameInput.placeholder = 'e.g., llama3.2, qwen2.5, mistral';
                        maxTokensInput.value = 32768;
                        break;
                    default:
                        modelNameInput.placeholder = 'Enter the model name';
                        maxTokensInput.value = 4096;
                }
            }
        });
    </script>
}
