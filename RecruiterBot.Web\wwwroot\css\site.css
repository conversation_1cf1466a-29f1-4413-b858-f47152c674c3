:root {
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 70px;
    --transition-speed: 0.3s;
    --mdb-primary: #3b71ca;
    --mdb-secondary: #9fa6b2;
    --mdb-success: #14a44d;
    --mdb-info: #54b4d3;
    --mdb-warning: #e4a11b;
    --mdb-danger: #dc4c64;
    --mdb-light: #fbfbfb;
    --mdb-dark: #262626;
}

/* Base styles */
body {
    font-family: 'Roboto', sans-serif;
    font-size: 0.9rem;
    min-height: 100vh;
    background-color: #f5f5f5;
    color: #212529;
    line-height: 1.6;
}

/* Material Design elevation */
.z-depth-1 {
    box-shadow: 0 2px 5px 0 rgba(0,0,0,0.16), 0 2px 10px 0 rgba(0,0,0,0.12);
}
.z-depth-2 {
    box-shadow: 0 4px 10px 0 rgba(0,0,0,0.14), 0 4px 20px 0 rgba(0,0,0,0.12);
}
.z-depth-3 {
    box-shadow: 0 8px 17px 2px rgba(0,0,0,0.14), 0 3px 14px 2px rgba(0,0,0,0.12), 0 5px 5px -3px rgba(0,0,0,0.2);
}

/* Main content area */
.main-content {
    margin-left:var(--sidebar-width);
    width: 100%;
    transition: all var(--transition-speed) ease;
    min-height: 100vh;
    background-color: #f5f5f5;
    padding: 20px;
}



/* Navigation */
.navbar {
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.16), 0 2px 10px 0 rgba(0, 0, 0, 0.12);
    padding: 0.5rem 1rem;
    background-color: #fff !important;
}

.navbar-brand {
    font-weight: 500;
    font-size: 1.5rem;
    color: #3b71ca !important;
}

.nav-link {
    position: relative;
    padding: 0.5rem 1rem;
    font-weight: 500;
    color: #4f4f4f;
    transition: color 0.2s ease-in-out;
}

.nav-link:hover {
    color: #3b71ca;
}

.nav-link.active {
    color: #023c07 !important;
}

.nav-link.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 1rem;
    right: 1rem;
    height: 2px;
    background-color: #3b71ca;
    border-radius: 2px;
}

/* Sidebar */
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: var(--sidebar-width);
    background: linear-gradient(180deg, #3b71ca 0%, #1a4b9b 100%);
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    overflow-y: auto;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 1.5rem 1.25rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar .nav {
    padding: 0.5rem 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 0.75rem 1.5rem;
    margin: 0.25rem 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease-in-out;
    display: flex;
    align-items: center;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
}

.sidebar .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
    text-decoration: none;
    font-weight: 500;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
}

.sidebar .nav-link.active i {
    color: #fff;
}

.sidebar .nav-link i {
    margin-right: 0.75rem;
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
}

/* Collapsed sidebar */
.sidebar-collapsed .sidebar {
    width: var(--sidebar-collapsed-width);
}

.sidebar-collapsed .sidebar .nav-link span {
    display: none;
}

.sidebar-collapsed .sidebar .nav-link i {
    margin-right: 0;
    font-size: 1.5rem;
}

.sidebar-collapsed .sidebar .sidebar-header {
    padding: 1rem 0.5rem;
    text-align: center;
}

.sidebar-collapsed .sidebar .sidebar-header span {
    display: none;
}

.sidebar-collapsed .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* Content area */
.content-wrapper {
    padding: 2rem;
    transition: margin 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Card styles */
.card {
    border: none;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: #fff;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Card hover animation removed as requested */

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 500;
    font-size: 1.1rem;
    color: #3b71ca;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    position: relative;
    padding: 0.6rem 1.4rem;
    font-weight: 500;
    font-size: 0.875rem;
    line-height: 1.5;
    letter-spacing: 0.02857em;
    text-transform: uppercase;
    border: none;
    border-radius: 0.25rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.2);
}

.btn:hover {
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.25);
    transform: translateY(-1px);
}

.btn:active {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
    transform: translateY(0);
}

.btn-primary {
    background-color: #3b71ca;
}

.btn-secondary {
    background-color: #6c757d;
}

.btn-success {
    background-color: #14a44d;
}

.btn-danger {
    background-color: #dc4c64;
}

.btn-warning {
    background-color: #e4a11b;
    color: #212529;
}

.btn-info {
    background-color: #54b4d3;
}

.btn-light {
    background-color: #f8f9fa;
    color: #212529;
}

.btn-dark {
    background-color: #262626;
}

/* Floating Action Button */
.btn-floating {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    box-shadow: 0 4px 5px -2px rgba(0, 0, 0, 0.2), 0 7px 10px 1px rgba(0, 0, 0, 0.14), 0 2px 16px 1px rgba(0, 0, 0, 0.12);
}

.btn-floating i {
    font-size: 1.5rem;
}

/* Dark theme */
[data-bs-theme="dark"] {
    --bs-body-bg: #1a1a1a;
    --bs-body-color: #dee2e6;
    --bs-border-color: #495057;
    --bs-card-bg: #2d2d2d;
    --bs-card-cap-bg: #2d2d2d;
    --bs-light: #343a40;
    --bs-dark: #f8f9fa;
}

    [data-bs-theme="dark"] .card,
    [data-bs-theme="dark"] .card-header,
    [data-bs-theme="dark"] .modal-content,
    [data-bs-theme="dark"] .dropdown-menu {
        background-color: var(--bs-card-bg);
        color: var(--bs-body-color);
    }

    [data-bs-theme="dark"] .text-muted {
        color: #adb5bd !important;
    }

    [data-bs-theme="dark"] .bg-light {
        background-color: #2d2d2d !important;
    }

    [data-bs-theme="dark"] .border,
    [data-bs-theme="dark"] .border-top,
    [data-bs-theme="dark"] .border-end,
    [data-bs-theme="dark"] .border-bottom,
    [data-bs-theme="dark"] .border-start {
        border-color: #495057 !important;
    }

/* Form controls */
.form-control, .form-select {
    padding: 0.75rem 1rem;
    font-size: 1rem;
    line-height: 1.5;
    color: #495057;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
    color: #495057;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-floating > label {
    padding: 0.75rem 1rem;
}

.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: #6c757d;
}

/* Sign in form */
.form-signin {
    width: 100%;
    max-width: 400px;
    padding: 2rem;
    margin: 2rem auto;
    background-color: #fff;
    border-radius: 0.5rem;
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
}

.form-signin .form-floating:focus-within {
    z-index: 2;
}

.form-signin input[type="email"],
.form-signin input[type="password"] {
    margin-bottom: 1rem;
    border-radius: 0.375rem;
    padding: 1rem;
}

/* Material Design input fields */
.form-outline {
    position: relative;
    margin-bottom: 1.5rem;
}

.form-outline input,
.form-outline textarea,
.form-outline .form-control {
    min-height: auto;
    padding: 0.33em 0.75em;
    border: 0;
    background: transparent;
    transition: all 0.2s linear;
    border-bottom: 1px solid #bdbdbd;
    border-radius: 0;
}

.form-outline input:focus,
.form-outline textarea:focus,
.form-outline .form-control:focus {
    box-shadow: none;
    border-bottom: 2px solid #3b71ca;
}

.form-outline label {
    position: absolute;
    top: 0;
    left: 0;
    padding-top: 0.37rem;
    pointer-events: none;
    transform-origin: 0 0;
    transition: all 0.2s ease-out;
    color: #757575;
    margin-bottom: 0;
}

.form-outline input:focus ~ label,
.form-outline input:not(:placeholder-shown) ~ label,
.form-outline textarea:focus ~ label,
.form-outline textarea:not(:placeholder-shown) ~ label {
    transform: translateY(-1rem) scale(0.8);
    color: #3b71ca;
}

/* Material Design checkboxes and radio buttons */
.form-check-input[type="checkbox"] {
    width: 1.2em;
    height: 1.2em;
    margin-top: 0.2em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 2px solid #757575;
    appearance: none;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    border-radius: 0.25em;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-check-input[type="checkbox"]:checked {
    background-color: #3b71ca;
    border-color: #3b71ca;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}

/* Material Design switches */
.form-switch .form-check-input {
    width: 3em;
    height: 1.5em;
    margin-left: -2.5em;
    background-color: #e0e0e0;
    background-image: none;
    border: none;
    border-radius: 2em;
    transition: background-color 0.15s ease-in-out, background-position 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-switch .form-check-input:checked {
    background-color: #3b71ca;
    background-image: none;
}

.form-switch .form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(59, 113, 202, 0.25);
}

/* Material Design floating labels */
.form-floating > .form-control:focus ~ label::after,
.form-floating > .form-control:not(:placeholder-shown) ~ label::after,
.form-floating > .form-select ~ label::after {
    position: absolute;
    inset: 1rem 0.375rem;
    z-index: -1;
    height: 1.5em;
    content: "";
    background-color: #fff;
    border-radius: 0.25rem;
}

/* Material Design chips */
.chip {
    display: inline-flex;
    align-items: center;
    background-color: #e0e0e0;
    border-radius: 16px;
    padding: 0 12px;
    height: 32px;
    font-size: 0.875rem;
    line-height: 32px;
    margin: 4px;
    transition: all 0.2s ease-in-out;
}

.chip .close {
    margin-left: 8px;
    opacity: 0.7;
    cursor: pointer;
}

.chip .close:hover {
    opacity: 1;
}

/* Material Design progress bars */
.progress {
    height: 4px;
    border-radius: 2px;
    background-color: #e0e0e0;
    overflow: visible;
}

.progress-bar {
    background-color: #3b71ca;
    position: relative;
    overflow: visible;
}

.progress-bar:after {
    content: '';
    position: absolute;
    right: -4px;
    top: -4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #3b71ca;
}

/* Material Design tabs */
.nav-tabs {
    border-bottom: 1px solid #e0e0e0;
}

.nav-tabs .nav-link {
    border: none;
    color: #757575;
    font-weight: 500;
    padding: 12px 24px;
    position: relative;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.nav-tabs .nav-link.active {
    color: #3b71ca;
    background-color: transparent;
}

.nav-tabs .nav-link.active:after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: #3b71ca;
}

/* Material Design tooltips */
.tooltip-inner {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 0.75rem;
    box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.2), 0 6px 10px 0 rgba(0, 0, 0, 0.14), 0 1px 18px 0 rgba(0, 0, 0, 0.12);
}

/* Material Design badges */
.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.75rem;
    line-height: 1.25;
    letter-spacing: 0.4px;
}

/* Material Design alerts */
.alert {
    border: none;
    border-radius: 4px;
    padding: 16px;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);
}

.alert-dismissible .btn-close {
    padding: 1rem;
}

/* Material Design tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    border-bottom: 1px solid #e0e0e0;
    font-weight: 500;
    color: #757575;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease-in-out;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.table td, .table th {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #e0e0e0;
}
