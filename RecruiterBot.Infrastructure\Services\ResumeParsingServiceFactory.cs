using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace RecruiterBot.Infrastructure.Services
{
    public interface IResumeParsingServiceFactory
    {
        IResumeParsingService CreateService();
    }

    public class ResumeParsingServiceFactory : IResumeParsingServiceFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly IHostEnvironment _environment;
        private readonly ILogger<ResumeParsingServiceFactory> _logger;

        public ResumeParsingServiceFactory(
            IServiceProvider serviceProvider,
            IHostEnvironment environment,
            ILogger<ResumeParsingServiceFactory> logger)
        {
            _serviceProvider = serviceProvider;
            _environment = environment;
            _logger = logger;
        }

        public IResumeParsingService CreateService()
        {
            if (_environment.IsDevelopment())
            {
                _logger.LogInformation("Using Ollama service for resume parsing in development mode");
                return _serviceProvider.GetRequiredService<OllamaResumeParsingService>();
            }
            else
            {
                _logger.LogInformation("Using OpenAI service for resume parsing in production mode");
                return _serviceProvider.GetRequiredService<OpenAIResumeParsingService>();
            }
        }
    }
}
