"""
Python script to decrypt LinkedIn credentials that were encrypted by the .NET application.
This script demonstrates how to decrypt the credentials using the same encryption key and IV.
"""

from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad
import base64
import logging
from typing import Optional

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AesDecryptor:
    """Helper class to decrypt data using AES-256-CBC"""
    
    def __init__(self, key: str, iv: str):
        """
        Initialize the decryptor with the encryption key and IV.
        
        Args:
            key: The encryption key (32 bytes/256 bits) as a string
            iv: The initialization vector (16 bytes) as a string
        """
        # Convert key and IV to bytes
        self.key = key.encode('utf-8').ljust(32)[:32]  # Ensure key is 32 bytes
        self.iv = iv.encode('utf-8').ljust(16)[:16]    # Ensure IV is 16 bytes
    
    def decrypt(self, encrypted_data: str) -> Optional[str]:
        """
        Decrypt the given base64-encoded encrypted data.
        
        Args:
            encrypted_data: Base64-encoded encrypted string
            
        Returns:
            Decrypted string, or None if decryption fails
        """
        if not encrypted_data:
            return None
            
        try:
            # Decode the base64 data
            encrypted_bytes = base64.b64decode(encrypted_data)
            
            # Create a new AES cipher in CBC mode
            cipher = AES.new(self.key, AES.MODE_CBC, self.iv)
            
            # Decrypt and unpad the data
            decrypted_bytes = unpad(cipher.decrypt(encrypted_bytes), AES.block_size)
            
            # Convert bytes to string
            return decrypted_bytes.decode('utf-8')
            
        except Exception as e:
            logger.error(f"Error decrypting data: {str(e)}")
            return None

def main():
    # These values should match the ones in your appsettings.json
    ENCRYPTION_KEY = "YourSuperSecretKeyMustBe32CharsLong256Bits"
    ENCRYPTION_IV = "16ByteIV12345678"
    
    # Create decryptor instance
    decryptor = AesDecryptor(ENCRYPTION_KEY, ENCRYPTION_IV)
    
    # Example usage
    encrypted_username = "YOUR_ENCRYPTED_USERNAME_HERE"
    encrypted_password = "YOUR_ENCRYPTED_PASSWORD_HERE"
    
    # Decrypt the credentials
    username = decryptor.decrypt(encrypted_username)
    password = decryptor.decrypt(encrypted_password)
    
    print(f"Decrypted Username: {username}")
    print(f"Decrypted Password: {'*' * (len(password) if password else 0)}")
    
    # For testing with real data from MongoDB
    # Uncomment and modify the following lines:
    """
    from pymongo import MongoClient
    
    # Connect to MongoDB
    client = MongoClient('mongodb://localhost:27017/')
    db = client['recruiterbot']
    collection = db['botconfigurations']
    
    # Get a bot configuration
    bot_config = collection.find_one({"enc_linkedIn_username": {"$exists": True}})
    
    if bot_config:
        print("\nFound bot configuration:")
        print(f"ID: {bot_config.get('_id')}")
        print(f"Query: {bot_config.get('query')}")
        
        # Decrypt and print credentials
        enc_username = bot_config.get('enc_linkedIn_username')
        enc_password = bot_config.get('enc_linkedIn_password')
        
        if enc_username and enc_password:
            print("\nDecrypted Credentials:")
            print(f"Username: {decryptor.decrypt(enc_username)}")
            print(f"Password: {decryptor.decrypt(enc_password)}")
    """

if __name__ == "__main__":
    main()
