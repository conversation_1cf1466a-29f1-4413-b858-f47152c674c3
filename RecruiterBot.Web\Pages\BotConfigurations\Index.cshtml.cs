using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using RecruiterBot.Infrastructure.Services;
using RecruiterBot.Core.Constants;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace RecruiterBot.Web.Pages.BotConfigurations
{
    [Authorize]
    public class IndexModel : PageModel
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly IRoleManagementService _roleManagementService;
        private readonly ILogger<IndexModel> _logger;

        public IList<BotConfiguration> Configurations { get; set; } = new List<BotConfiguration>();

        public IndexModel(
            IBotConfigurationService botConfigService,
            IRoleManagementService roleManagementService,
            ILogger<IndexModel> logger)
        {
            _botConfigService = botConfigService;
            _roleManagementService = roleManagementService;
            _logger = logger;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                var userRole = await _roleManagementService.GetUserPrimaryRoleAsync(userId);

                // Admin users should not access Bot Configurations
                if (userRole == UserRoles.Admin)
                {
                    TempData["ErrorMessage"] = "Access denied. Administrators cannot access Bot Configurations.";
                    return RedirectToPage("/Dashboard");
                }

                if (userRole == UserRoles.CorpAdmin)
                {
                    // Corp Admin can see all team configurations
                    Configurations = (await _botConfigService.GetTeamConfigurationsAsync(userId)) as List<BotConfiguration>;
                }
                else if (userRole == UserRoles.User)
                {
                    // Users can see their own configurations plus configurations from their Corp Admin
                    var corpAdminId = await _roleManagementService.GetCorpAdminForUserAsync(userId);
                    if (!string.IsNullOrEmpty(corpAdminId))
                    {
                        // Get user's own configurations and Corp Admin's configurations
                        var userConfigs = await _botConfigService.GetAllConfigurationsAsync(userId);
                        var corpAdminConfigs = await _botConfigService.GetAllConfigurationsAsync(corpAdminId);

                        var allConfigs = new List<BotConfiguration>();
                        allConfigs.AddRange(userConfigs);
                        allConfigs.AddRange(corpAdminConfigs);

                        // Remove duplicates and sort by creation date
                        Configurations = allConfigs
                            .GroupBy(c => c.Id)
                            .Select(g => g.First())
                            .OrderByDescending(c => c.CreatedDateUtc)
                            .ToList();
                    }
                    else
                    {
                        // Fallback to user's own configurations
                        Configurations = (await _botConfigService.GetAllConfigurationsAsync(userId)) as List<BotConfiguration>;
                    }
                }
                else
                {
                    // Other roles see their own configurations
                    Configurations = (await _botConfigService.GetAllConfigurationsAsync(userId)) as List<BotConfiguration>;
                }

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading bot configurations");
                ModelState.AddModelError("", "An error occurred while loading bot configurations.");
                return Page();
            }
        }
    }
}
