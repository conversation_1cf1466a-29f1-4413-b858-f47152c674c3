@page
@model EnableAuthenticatorModel
@{
    ViewData["Title"] = "Configure authenticator app";
    ViewData["ActivePage"] = ManageNavPages.TwoFactorAuthentication;
}

<div class="space-y-6">
    <h3 class="text-lg font-medium">@ViewData["Title"]</h3>
    
    <div class="alert alert-info">
        <div class="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="w-6 h-6 mx-2 stroke-current">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <label>To use an authenticator app go through the following steps:</label>
        </div>
    </div>
    
    <div class="card bg-base-100 shadow">
        <div class="card-body">
            <ol class="list-decimal pl-5 space-y-4">
                <li>
                    <p>Download a two-factor authenticator app like Microsoft Authenticator for
                        <a href="https://go.microsoft.com/fwlink/?Linkid=825072" class="link">Android</a> and
                        <a href="https://go.microsoft.com/fwlink/?Linkid=825073" class="link">iOS</a> or
                        Google Authenticator for
                        <a href="https://play.google.com/store/apps/details?id=com.google.android.apps.authenticator2&hl=en" class="link">Android</a> and
                        <a href="https://itunes.apple.com/us/app/google-authenticator/id388497605?mt=8" class="link">iOS</a>.
                    </p>
                </li>
                <li>
                    <p>Scan the QR Code or enter this key <kbd class="kbd">@Model.SharedKey</kbd> into your two factor authenticator app. Spaces and casing do not matter.</p>
                    <div id="qrCode" class="my-4"></div>
                    <div id="qrCodeData" data-url="@Html.Raw(Model.AuthenticatorUri)"></div>
                </li>
                <li>
                    <p>
                        Once you have scanned the QR code or input the key above, your two factor authentication app will provide you
                        with a unique code. Enter the code in the confirmation box below.
                    </p>
                    <div class="form-control mt-4">
                        <form method="post" class="space-y-4">
                            <div class="form-control">
                                <label asp-for="Input.Code" class="label">
                                    <span class="label-text">Verification Code</span>
                                </label>
                                <input asp-for="Input.Code" class="input input-bordered w-full" autocomplete="off" />
                                <span asp-validation-for="Input.Code" class="text-error text-sm"></span>
                            </div>
                            <button type="submit" class="btn btn-primary">Verify</button>
                            <div asp-validation-summary="ModelOnly" class="text-error"></div>
                        </form>
                    </div>
                </li>
            </ol>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script type="text/javascript" src="~/lib/qrcodejs/qrcode.min.js"></script>
    <script type="text/javascript">
        window.addEventListener("load", () => {
            const uri = document.getElementById("qrCodeData").getAttribute("data-url");
            new QRCode(document.getElementById("qrCode"),
                {
                    text: uri,
                    width: 150,
                    height: 150
                });
        });
    </script>
}
