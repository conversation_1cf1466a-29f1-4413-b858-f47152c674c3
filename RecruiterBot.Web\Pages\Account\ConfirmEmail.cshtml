@page
@model RecruiterBot.Web.Pages.Account.ConfirmEmailModel
@{
    ViewData["Title"] = "Email Confirmation";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-body p-5">
                    <div class="text-center mb-4">
                        @if (Model.IsSuccess)
                        {
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 text-success">Email Confirmed!</h2>
                            <p class="text-muted">Your email has been successfully verified. You can now sign in to your account.</p>
                            
                            <div class="mt-4">
                                <a asp-page="./Login" class="btn btn-primary">
                                    <i class="bi bi-box-arrow-in-right me-2"></i>
                                    Sign In
                                </a>
                            </div>
                        }
                        else
                        {
                            <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 3rem;"></i>
                            <h2 class="mt-3 text-danger">Email Confirmation Failed</h2>
                            <p class="text-muted">@Model.ErrorMessage</p>
                            
                            <div class="mt-4">
                                <a asp-page="./ResendEmailConfirmation" class="btn btn-outline-primary me-2">
                                    <i class="bi bi-envelope me-2"></i>
                                    Resend Confirmation Email
                                </a>
                                <a asp-page="./Login" class="btn btn-secondary">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    Back to Login
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
