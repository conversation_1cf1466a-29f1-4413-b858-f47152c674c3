using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Consultant.InterviewProfiles
{
    [Authorize(Policy = AuthorizationPolicies.ConsultantOnly)]
    public class DetailsModel : PageModel
    {
        private readonly IInterviewProfileService _interviewProfileService;
        private readonly ILogger<DetailsModel> _logger;

        public DetailsModel(IInterviewProfileService interviewProfileService, ILogger<DetailsModel> logger)
        {
            _interviewProfileService = interviewProfileService;
            _logger = logger;
        }

        public InterviewProfile? Profile { get; set; }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile ID is required." });
                }

                var consultantId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(consultantId))
                {
                    return Challenge();
                }

                Profile = await _interviewProfileService.GetProfileByIdAsync(id, consultantId);

                if (Profile == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Profile not found or you don't have access to it." });
                }

                _logger.LogInformation("Consultant {ConsultantId} viewed interview profile {ProfileId}", 
                    consultantId, id);

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading interview profile details for ID {ProfileId}", id);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the profile." });
            }
        }
    }
}
