@page "{id}"
@model RecruiterBot.Web.Pages.BotConfigurations.DeleteModel
@{
    ViewData["Title"] = "Delete Bot Configuration";
}

<div class="container-fluid">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-page="./Index">Bot Configurations</a></li>
            <li class="breadcrumb-item active" aria-current="page">Delete</li>
        </ol>
    </nav>

    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-danger text-white">
            <h6 class="m-0 font-weight-bold">Delete Configuration</h6>
        </div>
        <div class="card-body">
            <h5 class="text-danger">Are you sure you want to delete this configuration?</h5>
            <p class="text-muted">This action cannot be undone. All data associated with this configuration will be permanently removed.</p>
            
            <div class="card mb-4">
                <div class="card-body
                    <dl class="row mb-0">
                        <dt class="col-sm-4">Search Query</dt>
                        <dd class="col-sm-8">@Model.Configuration.Query</dd>

                        <dt class="col-sm-4">Candidate Search</dt>
                        <dd class="col-sm-8">@Model.Configuration.CandidateSearch</dd>


                        <dt class="col-sm-4">Status</dt>
                        <dd class="col-sm-8">
                            @if (Model.Configuration.IsActive)
                            {
                                <span class="badge bg-success">Active</span>
                            }
                            else
                            {
                                <span class="badge bg-secondary">Inactive</span>
                            }
                        </dd>

                        <dt class="col-sm-4">Created</dt>
                        <dd class="col-sm-8">@Model.Configuration.CreatedDateUtc.ToString("g")</dd>
                    </dl>
                </div>
            </div>

            <form method="post">
                <div class="d-flex justify-content-between">
                    <a asp-page="./Index" class="btn btn-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Cancel
                    </a>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Delete Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
