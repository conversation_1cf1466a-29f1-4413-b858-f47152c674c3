@page
@model RecruiterBot.Web.Pages.Consultant.InterviewProfiles.IndexModel
@using RecruiterBot.Core.Constants
@{
    ViewData["Title"] = "My Interview Profiles";
}

@section Styles {
    <style>
        .profile-card {
            transition: transform 0.2s ease-in-out;
            border-left: 4px solid #0d6efd;
        }
        .profile-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .search-container {
            position: relative;
        }
        .search-container .bi-search {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
        }
        .search-input {
            padding-left: 35px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .experience-badge {
            background: rgba(13, 110, 253, 0.1);
            color: #0d6efd;
            border: 1px solid rgba(13, 110, 253, 0.2);
        }
    </style>
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2 class="text-primary mb-1">
                        <i class="bi bi-person-badge me-2"></i>
                        My Interview Profiles
                    </h2>
                    <p class="text-muted mb-0">Create and manage your professional profiles for interview preparation</p>
                </div>
                <a asp-page="./Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Create New Profile
                </a>
            </div>

            <!-- Statistics Cards -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card stat-card">
                        <div class="card-body text-center">
                            <i class="bi bi-collection display-4 mb-2"></i>
                            <h3 class="mb-0">@Model.TotalProfiles</h3>
                            <small>Total Profiles</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-briefcase display-4 mb-2"></i>
                            <h3 class="mb-0">@Model.TotalExperiences</h3>
                            <small>Work Experiences</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-code-slash display-4 mb-2"></i>
                            <h3 class="mb-0">@Model.TotalTechnologies</h3>
                            <small>Technologies</small>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body text-center">
                            <i class="bi bi-trophy display-4 mb-2"></i>
                            <h3 class="mb-0">@Model.TotalAchievements</h3>
                            <small>Achievements</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="search-container">
                        <i class="bi bi-search"></i>
                        <input type="text" id="searchInput" class="form-control search-input" 
                               placeholder="Search profiles by content..." 
                               value="@Model.SearchTerm">
                    </div>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-outline-secondary" onclick="clearSearch()">
                        <i class="bi bi-x-circle me-1"></i>Clear Search
                    </button>
                </div>
            </div>

            <!-- Success/Error Messages -->
            @if (!string.IsNullOrEmpty(Model.SuccessMessage))
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle me-2"></i>@Model.SuccessMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (!string.IsNullOrEmpty(Model.ErrorMessage))
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle me-2"></i>@Model.ErrorMessage
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <!-- Profiles Grid -->
            @if (Model.Profiles?.Any() == true)
            {
                <div class="row g-4" id="profilesContainer">
                    @foreach (var profile in Model.Profiles)
                    {
                        <div class="col-lg-6 col-xl-4 profile-item"
                             data-search="@($"{profile.AboutYou} {profile.PrimaryTechnology}".ToLower())">
                            <div class="card profile-card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-0">Profile #@(Model.Profiles.ToList().IndexOf(profile) + 1)</h6>
                                        <small class="text-muted">@profile.LastUpdatedDisplay</small>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" 
                                                data-bs-toggle="dropdown">
                                            <i class="bi bi-three-dots"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" asp-page="./Details" asp-route-id="@profile.Id">
                                                <i class="bi bi-eye me-2"></i>View Details
                                            </a></li>
                                            <li><a class="dropdown-item" asp-page="./Edit" asp-route-id="@profile.Id">
                                                <i class="bi bi-pencil me-2"></i>Edit
                                            </a></li>
                                            <li><hr class="dropdown-divider"></li>
                                            <li><button class="dropdown-item text-danger" onclick="confirmDelete('@profile.Id')">
                                                <i class="bi bi-trash me-2"></i>Delete
                                            </button></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- About You Preview -->
                                    <div class="mb-3">
                                        <h6 class="text-primary mb-2">About You</h6>
                                        <p class="card-text">
                                            @(profile.AboutYou.Length > 150 ? profile.AboutYou.Substring(0, 150) + "..." : profile.AboutYou)
                                        </p>
                                    </div>

                                    <!-- Quick Stats -->
                                    <div class="row text-center mb-3">
                                        <div class="col-4">
                                            <div class="experience-badge rounded p-2">
                                                <div class="fw-bold">@profile.ExperienceCount</div>
                                                <small>Experience@(profile.ExperienceCount != 1 ? "s" : "")</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="experience-badge rounded p-2">
                                                <div class="fw-bold">@(string.IsNullOrWhiteSpace(profile.PrimaryTechnology) ? 0 : 1)</div>
                                                <small>Technology</small>
                                            </div>
                                        </div>
                                        <div class="col-4">
                                            <div class="experience-badge rounded p-2">
                                                <div class="fw-bold">@profile.AchievementCount</div>
                                                <small>Achievement@(profile.AchievementCount != 1 ? "s" : "")</small>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Primary Technology Preview -->
                                    @if (!string.IsNullOrWhiteSpace(profile.PrimaryTechnology))
                                    {
                                        <div class="mb-2">
                                            <h6 class="text-primary mb-2">Primary Technology</h6>
                                            <div class="d-flex flex-wrap gap-1">
                                                <span class="badge bg-secondary">@profile.PrimaryTechnology</span>
                                            </div>
                                        </div>
                                    }

                                    <!-- Experience Summary -->
                                    @if (profile.TotalYearsExperience > 0)
                                    {
                                        <div class="mt-auto">
                                            <small class="text-muted">
                                                <i class="bi bi-calendar-check me-1"></i>
                                                ~@profile.TotalYearsExperience year@(profile.TotalYearsExperience != 1 ? "s" : "") total experience
                                            </small>
                                        </div>
                                    }
                                </div>
                                <div class="card-footer bg-transparent">
                                    <div class="d-flex justify-content-between">
                                        <a asp-page="./Details" asp-route-id="@profile.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye me-1"></i>View
                                        </a>
                                        <a asp-page="./Edit" asp-route-id="@profile.Id" class="btn btn-sm btn-primary">
                                            <i class="bi bi-pencil me-1"></i>Edit
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-person-badge display-1 text-muted"></i>
                    <h4 class="mt-3">No Interview Profiles Found</h4>
                    <p class="text-muted">
                        @if (!string.IsNullOrEmpty(Model.SearchTerm))
                        {
                            <text>No profiles match your search criteria. Try adjusting your search.</text>
                        }
                        else
                        {
                            <text>Create your first interview profile to get started with AI-powered interview preparation.</text>
                        }
                    </p>
                    @if (string.IsNullOrEmpty(Model.SearchTerm))
                    {
                        <a asp-page="./Create" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>
                            Create First Profile
                        </a>
                    }
                </div>
            }
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this interview profile?</p>
                <p class="text-danger"><small>This action cannot be undone.</small></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let deleteProfileId = '';

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', filterProfiles);

        function filterProfiles() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const profileItems = document.querySelectorAll('.profile-item');
            
            profileItems.forEach(item => {
                const searchText = item.dataset.search;
                const matches = !searchTerm || searchText.includes(searchTerm);
                item.style.display = matches ? 'block' : 'none';
            });
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            filterProfiles();
        }

        function confirmDelete(profileId) {
            deleteProfileId = profileId;
            new bootstrap.Modal(document.getElementById('deleteModal')).show();
        }

        document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
            if (deleteProfileId) {
                window.location.href = `/Consultant/InterviewProfiles/Delete/${deleteProfileId}`;
            }
        });

        // Auto-search on page load if search term exists
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('searchInput').value) {
                filterProfiles();
            }
        });
    </script>
}
