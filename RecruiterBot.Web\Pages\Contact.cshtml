@page "/contact"
@model ContactModel
@{
    ViewData["Title"] = "Contact Us - RecruiterBot";
}

@section Styles {
    <style>
        .contact-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 80px 0;
        }
        
        .contact-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .contact-card:hover {
            transform: translateY(-5px);
        }
        
        .contact-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 20px;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
            color: white;
        }
    </style>
}

<!-- Hero Section -->
<section class="contact-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-3 fw-bold mb-4">Get in Touch</h1>
                <p class="lead mb-4">
                    Have questions about RecruiterBot? We're here to help you transform your hiring process. 
                    Reach out to our team for support, demos, or partnership opportunities.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Contact Options -->
<section class="py-5">
    <div class="container">
        <div class="row g-4 mb-5">
            <div class="col-lg-4 col-md-6">
                <div class="contact-card text-center p-4 h-100">
                    <div class="contact-icon">
                        <i class="bi bi-envelope"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Email Support</h4>
                    <p class="text-muted mb-3">
                        Get help with your account, technical issues, or general questions.
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary">
                        <EMAIL>
                    </a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="contact-card text-center p-4 h-100">
                    <div class="contact-icon">
                        <i class="bi bi-telephone"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Phone Support</h4>
                    <p class="text-muted mb-3">
                        Speak directly with our support team during business hours.
                    </p>
                    <a href="tel:******-RECRUIT" class="btn btn-outline-primary">
                        +****************
                    </a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="contact-card text-center p-4 h-100">
                    <div class="contact-icon">
                        <i class="bi bi-calendar"></i>
                    </div>
                    <h4 class="fw-bold mb-3">Schedule Demo</h4>
                    <p class="text-muted mb-3">
                        Book a personalized demo to see RecruiterBot in action.
                    </p>
                    <a href="#demo-form" class="btn btn-outline-primary">
                        Schedule Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 shadow">
                    <div class="card-body p-5">
                        <h2 class="text-center fw-bold mb-4">Send us a Message</h2>

                        @if (!string.IsNullOrEmpty(Model.SuccessMessage))
                        {
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="bi bi-check-circle me-2"></i>@Model.SuccessMessage
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.ErrorMessage))
                        {
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>@Model.ErrorMessage
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        }

                        <form id="contactForm" method="post">
                            @Html.AntiForgeryToken()
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label asp-for="ContactForm.FirstName" class="form-label">First Name *</label>
                                    <input asp-for="ContactForm.FirstName" class="form-control" />
                                    <span asp-validation-for="ContactForm.FirstName" class="text-danger"></span>
                                </div>
                                <div class="col-md-6">
                                    <label asp-for="ContactForm.LastName" class="form-label">Last Name *</label>
                                    <input asp-for="ContactForm.LastName" class="form-control" />
                                    <span asp-validation-for="ContactForm.LastName" class="text-danger"></span>
                                </div>
                                <div class="col-md-6">
                                    <label asp-for="ContactForm.Email" class="form-label">Email Address *</label>
                                    <input asp-for="ContactForm.Email" class="form-control" />
                                    <span asp-validation-for="ContactForm.Email" class="text-danger"></span>
                                </div>
                                <div class="col-md-6">
                                    <label asp-for="ContactForm.Company" class="form-label">Company</label>
                                    <input asp-for="ContactForm.Company" class="form-control" />
                                    <span asp-validation-for="ContactForm.Company" class="text-danger"></span>
                                </div>
                                <div class="col-12">
                                    <label asp-for="ContactForm.InquiryType" class="form-label">Inquiry Type *</label>
                                    <select asp-for="ContactForm.InquiryType" class="form-select">
                                        <option value="">Select an option</option>
                                        <option value="demo">Request Demo</option>
                                        <option value="support">Technical Support</option>
                                        <option value="sales">Sales Inquiry</option>
                                        <option value="partnership">Partnership</option>
                                        <option value="other">Other</option>
                                    </select>
                                    <span asp-validation-for="ContactForm.InquiryType" class="text-danger"></span>
                                </div>
                                <div class="col-12">
                                    <label asp-for="ContactForm.Message" class="form-label">Message *</label>
                                    <textarea asp-for="ContactForm.Message" class="form-control" rows="5"
                                              placeholder="Tell us how we can help you..."></textarea>
                                    <span asp-validation-for="ContactForm.Message" class="text-danger"></span>
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input asp-for="ContactForm.Newsletter" class="form-check-input" />
                                        <label asp-for="ContactForm.Newsletter" class="form-check-label">
                                            I'd like to receive updates about RecruiterBot features and news
                                        </label>
                                    </div>
                                </div>
                                <div class="col-12 text-center">
                                    <button type="submit" class="btn btn-gradient btn-lg px-5">
                                        <i class="bi bi-send me-2"></i>Send Message
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h2 class="text-center fw-bold mb-5">Frequently Asked Questions</h2>
                <div class="accordion" id="faqAccordion">
                    <div class="accordion-item">
                        <h3 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#faq1" aria-expanded="true">
                                How quickly can I get started with RecruiterBot?
                            </button>
                        </h3>
                        <div id="faq1" class="accordion-collapse collapse show" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                You can start using RecruiterBot immediately after signing up. Our onboarding process 
                                takes less than 10 minutes, and you can begin uploading job requirements and candidate 
                                resumes right away.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h3 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#faq2">
                                What file formats do you support for resumes?
                            </button>
                        </h3>
                        <div id="faq2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                RecruiterBot supports all major resume formats including PDF, DOC, DOCX, TXT, and RTF. 
                                Our AI can extract information from both structured and unstructured resume formats.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h3 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#faq3">
                                Is my candidate data secure?
                            </button>
                        </h3>
                        <div id="faq3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Absolutely. We use enterprise-grade security with end-to-end encryption, SOC 2 compliance, 
                                and GDPR compliance. Your candidate data is stored securely and never shared with third parties.
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h3 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" 
                                    data-bs-target="#faq4">
                                Can I integrate RecruiterBot with my existing ATS?
                            </button>
                        </h3>
                        <div id="faq4" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                            <div class="accordion-body">
                                Yes! RecruiterBot offers integrations with popular ATS platforms like Workday, 
                                Greenhouse, and BambooHR. We also provide a robust API for custom integrations.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
