using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Data;
using RecruiterBot.Infrastructure.Identity;
using RecruiterBot.Infrastructure.Services;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace RecruiterBot.Infrastructure.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddMongoDbIdentity(this IServiceCollection services, IConfiguration configuration)
        {
            // Register MongoDB settings
            services.Configure<MongoDbSettings>(configuration.GetSection("MongoDbSettings"));
            
            // Register MongoDB context
            services.AddSingleton<MongoDbContext>();

            // Configure Identity
            services.AddIdentity<User, IdentityRole>()
                .AddDefaultTokenProviders();

            // Register our custom UserStore and RoleStore
            services.AddScoped<IUserStore<User>, MongoUserStore>();
            services.AddScoped<IRoleStore<IdentityRole>, MongoRoleStore>();

            // Register role management service
            services.AddScoped<IRoleManagementService, RoleManagementService>();

            // Register seed data service
            services.AddScoped<ISeedDataService, SeedDataService>();

            // Configure Identity options
            services.Configure<IdentityOptions>(options =>
            {
                // Password settings
                options.Password.RequireDigit = true;
                options.Password.RequiredLength = 8;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequireUppercase = true;
                options.Password.RequireLowercase = true;
                options.Password.RequiredUniqueChars = 6;

                // Lockout settings
                options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(30);
                options.Lockout.MaxFailedAccessAttempts = 5;
                options.Lockout.AllowedForNewUsers = true;

                // User settings
                options.User.RequireUniqueEmail = true;

                // Email confirmation settings
                options.SignIn.RequireConfirmedEmail = true;
                options.SignIn.RequireConfirmedAccount = false;

                // Token settings
                options.Tokens.EmailConfirmationTokenProvider = TokenOptions.DefaultEmailProvider;
                options.Tokens.PasswordResetTokenProvider = TokenOptions.DefaultEmailProvider;
            });

            // Configure cookie settings
            services.ConfigureApplicationCookie(options =>
            {
                // Cookie settings
                options.Cookie.HttpOnly = true;
                options.ExpireTimeSpan = TimeSpan.FromDays(30);
                options.LoginPath = "/Account/Login";
                options.AccessDeniedPath = "/Account/AccessDenied";
                options.SlidingExpiration = true;
            });

            return services;
        }
    }

    // Simple role store that throws NotSupportedException for all operations
    // If you need role support, implement a proper IRoleStore<IdentityRole>
    public class NotImplementedRoleStore : IRoleStore<IdentityRole>
    {
        public Task<IdentityResult> CreateAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<IdentityResult> DeleteAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public void Dispose() { }

        public Task<IdentityRole> FindByIdAsync(string roleId, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<IdentityRole> FindByNameAsync(string normalizedRoleName, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<string> GetNormalizedRoleNameAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<string> GetRoleIdAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<string> GetRoleNameAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task SetNormalizedRoleNameAsync(IdentityRole role, string normalizedName, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task SetRoleNameAsync(IdentityRole role, string roleName, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");

        public Task<IdentityResult> UpdateAsync(IdentityRole role, CancellationToken cancellationToken)
            => throw new NotSupportedException("Role management is not implemented");
    }
}
