@page "/Account/Manage/ChangePassword"
@model ChangePasswordModel
@{
    ViewData["Title"] = "Change Password";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
    Layout = "_AuthenticatedLayout";
}

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-0 d-flex align-items-center">
                <i class="bi bi-shield-lock me-2 text-primary"></i>
                @ViewData["Title"]
            </h1>
            <p class="text-muted">Update your account password to keep your account secure</p>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <partial name="_StatusMessage" for="StatusMessage" />
        </div>
    </div>

    <div class="row">
        <div class="col-12 col-lg-8">
            <div class="card shadow">
                <div class="card-header bg-gradient-to-r from-indigo-600 to-purple-600 text-white border-0">
                    <h5 class="card-title mb-0 d-flex align-items-center">
                        <i class="bi bi-key me-2"></i>
                        Password Settings
                    </h5>
                </div>
                <div class="card-body p-4">
                    <form id="change-password-form" method="post" class="needs-validation" novalidate>
                        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

                        <div class="mb-3">
                            <label asp-for="Input.OldPassword" class="form-label">
                                <i class="bi bi-lock me-1"></i>
                                Current Password
                            </label>
                            <div class="input-group">
                                <input asp-for="Input.OldPassword" type="password" class="form-control" required />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('Input_OldPassword', this)">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Please enter your current password.
                            </div>
                            <span asp-validation-for="Input.OldPassword" class="text-danger small"></span>
                        </div>

                        <div class="mb-3">
                            <label asp-for="Input.NewPassword" class="form-label">
                                <i class="bi bi-shield-plus me-1"></i>
                                New Password
                            </label>
                            <div class="input-group">
                                <input asp-for="Input.NewPassword" type="password" class="form-control" required minlength="6" />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('Input_NewPassword', this)">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="form-text">
                                <i class="bi bi-info-circle me-1"></i>
                                Password must be at least 6 characters long
                            </div>
                            <div class="invalid-feedback">
                                Please enter a new password (minimum 6 characters).
                            </div>
                            <span asp-validation-for="Input.NewPassword" class="text-danger small"></span>
                        </div>

                        <div class="mb-4">
                            <label asp-for="Input.ConfirmPassword" class="form-label">
                                <i class="bi bi-shield-check me-1"></i>
                                Confirm New Password
                            </label>
                            <div class="input-group">
                                <input asp-for="Input.ConfirmPassword" type="password" class="form-control" required />
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword('Input_ConfirmPassword', this)">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                Please confirm your new password.
                            </div>
                            <span asp-validation-for="Input.ConfirmPassword" class="text-danger small"></span>
                        </div>

                        <div class="d-flex justify-content-between align-items-center">
                            <a href="/Account/Manage" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-1"></i>
                                Back to Profile
                            </a>
                            <button type="submit" class="btn btn-primary px-4">
                                <span class="spinner-border spinner-border-sm d-none me-1" role="status" aria-hidden="true"></span>
                                <i class="bi bi-check-lg me-1"></i>
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12 col-lg-4 mt-4 mt-lg-0">
            <div class="card border-info">
                <div class="card-header bg-info text-white">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-shield-check me-2"></i>
                        Password Security Tips
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Use at least 8 characters
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Include uppercase and lowercase letters
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Add numbers and special characters
                        </li>
                        <li class="mb-2">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Avoid common words or patterns
                        </li>
                        <li class="mb-0">
                            <i class="bi bi-check-circle text-success me-2"></i>
                            Don't reuse old passwords
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Password visibility toggle
        function togglePassword(inputId, button) {
            const input = document.getElementById(inputId);
            const icon = button.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                input.type = 'password';
                icon.className = 'bi bi-eye';
            }
        }

        // Password strength indicator
        function checkPasswordStrength(password) {
            let strength = 0;
            const checks = {
                length: password.length >= 8,
                lowercase: /[a-z]/.test(password),
                uppercase: /[A-Z]/.test(password),
                numbers: /\d/.test(password),
                special: password.length > 0 && (password.indexOf('!') !== -1 || password.indexOf('@@') !== -1 || password.indexOf('#') !== -1 || password.indexOf('$') !== -1 || password.indexOf('%') !== -1 || password.indexOf('^') !== -1 || password.indexOf('&') !== -1 || password.indexOf('*') !== -1)
            };

            strength = Object.values(checks).filter(Boolean).length;
            return { strength, checks };
        }

        // Form validation and submission handling
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('change-password-form');
            const newPasswordInput = document.getElementById('Input_NewPassword');
            const confirmPasswordInput = document.getElementById('Input_ConfirmPassword');

            // Password strength feedback
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    const { strength, checks } = checkPasswordStrength(password);

                    // Update visual feedback based on strength
                    this.classList.remove('is-valid', 'is-invalid');
                    if (password.length > 0) {
                        if (strength >= 3) {
                            this.classList.add('is-valid');
                        } else if (strength < 2) {
                            this.classList.add('is-invalid');
                        }
                    }
                });
            }

            // Confirm password validation
            if (confirmPasswordInput && newPasswordInput) {
                confirmPasswordInput.addEventListener('input', function() {
                    const newPassword = newPasswordInput.value;
                    const confirmPassword = this.value;

                    this.classList.remove('is-valid', 'is-invalid');
                    if (confirmPassword.length > 0) {
                        if (newPassword === confirmPassword) {
                            this.classList.add('is-valid');
                        } else {
                            this.classList.add('is-invalid');
                        }
                    }
                });
            }

            // Form submission with loading state
            if (form) {
                form.addEventListener('submit', function(e) {
                    if (!form.checkValidity()) {
                        e.preventDefault();
                        e.stopPropagation();
                    } else {
                        const submitButton = form.querySelector('button[type="submit"]');
                        if (submitButton) {
                            const spinner = submitButton.querySelector('.spinner-border');
                            const icon = submitButton.querySelector('.bi-check-lg');
                            const buttonText = submitButton.childNodes[submitButton.childNodes.length - 1];

                            submitButton.disabled = true;
                            spinner.classList.remove('d-none');
                            if (icon) icon.style.display = 'none';
                            buttonText.textContent = ' Updating...';
                        }
                    }

                    form.classList.add('was-validated');
                }, false);
            }
        });
    </script>
}
