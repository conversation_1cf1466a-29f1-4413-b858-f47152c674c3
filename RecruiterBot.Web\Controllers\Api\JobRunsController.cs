using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RecruiterBot.Web.Models;
using RecruiterBot.Web.Services;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace RecruiterBot.Web.Controllers.Api
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    [Produces("application/json")]
    public class JobRunsController : ControllerBase
    {
        private readonly IBotConfigurationService _botConfigService;
        private readonly ILogger<JobRunsController> _logger;

        public JobRunsController(IBotConfigurationService botConfigService, ILogger<JobRunsController> logger)
        {
            _botConfigService = botConfigService;
            _logger = logger;
        }

        // GET: api/JobRuns/5
        /// <summary>
        /// Get a specific job run by ID
        /// </summary>
        /// <param name="id">The ID of the job run to retrieve</param>
        /// <returns>The job run details</returns>
        /// <response code="200">Returns the job run</response>
        /// <response code="401">If the user is not authenticated</response>
        /// <response code="404">If the job run is not found</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult<JobRun>> GetJobRun(string id)
        {
            try
            {
                var userId = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
                
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "User not authenticated" });
                }

                _logger.LogInformation("Fetching job run {JobRunId} for user {UserId}", id, userId);
                var jobRun = await _botConfigService.GetJobRunAsync(id, userId);

                if (jobRun == null)
                {
                    _logger.LogWarning("Job run {JobRunId} not found or access denied for user {UserId}", id, userId);
                    return NotFound(new { message = "Job run not found or access denied" });
                }

                return Ok(jobRun);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting job run {JobRunId}", id);
                return StatusCode(500, new { message = "An error occurred while retrieving the job run.", error = ex.Message });
            }
        }
    }
}
