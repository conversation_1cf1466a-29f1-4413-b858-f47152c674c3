using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Admin.LLMModels
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class IndexModel : PageModel
    {
        private readonly ILLMModelService _llmModelService;
        private readonly ILogger<IndexModel> _logger;

        public IndexModel(ILLMModelService llmModelService, ILogger<IndexModel> logger)
        {
            _llmModelService = llmModelService;
            _logger = logger;
        }

        public IEnumerable<LLMModel> Models { get; set; } = new List<LLMModel>();
        
        [BindProperty(SupportsGet = true)]
        public string? SearchTerm { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public ModelProvider? SelectedProvider { get; set; }
        
        [BindProperty(SupportsGet = true)]
        public bool? ActiveOnly { get; set; }

        [TempData]
        public string? SuccessMessage { get; set; }
        
        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Get all models first
                var allModels = await _llmModelService.GetAllModelsAsync();

                // Apply filters
                Models = allModels;

                if (!string.IsNullOrWhiteSpace(SearchTerm))
                {
                    Models = await _llmModelService.SearchModelsAsync(SearchTerm);
                }

                if (SelectedProvider.HasValue)
                {
                    Models = Models.Where(m => m.ModelProvider == SelectedProvider.Value);
                }

                if (ActiveOnly.HasValue)
                {
                    Models = Models.Where(m => m.IsActive == ActiveOnly.Value);
                }

                _logger.LogInformation("Admin user {UserId} accessed LLM models page with {Count} models", 
                    userId, Models.Count());

                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading LLM models page");
                ErrorMessage = "An error occurred while loading the models. Please try again.";
                Models = new List<LLMModel>();
                return Page();
            }
        }

        public async Task<IActionResult> OnPostToggleStatusAsync(string id)
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                var success = await _llmModelService.ToggleModelStatusAsync(id, userId);
                
                if (success)
                {
                    SuccessMessage = "Model status updated successfully.";
                }
                else
                {
                    ErrorMessage = "Failed to update model status.";
                }

                return RedirectToPage();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling model status for {ModelId}", id);
                ErrorMessage = "An error occurred while updating the model status.";
                return RedirectToPage();
            }
        }
    }
}
