using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.Mvc.Rendering;
using RecruiterBot.Core.Constants;
using RecruiterBot.Core.Models;
using RecruiterBot.Infrastructure.Services;
using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace RecruiterBot.Web.Pages.Admin.LLMModels
{
    [Authorize(Policy = AuthorizationPolicies.AdminOnly)]
    public class EditModel : PageModel
    {
        private readonly ILLMModelService _llmModelService;
        private readonly ILogger<EditModel> _logger;

        public EditModel(ILLMModelService llmModelService, ILogger<EditModel> logger)
        {
            _llmModelService = llmModelService;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; } = new();

        public SelectList ProviderOptions { get; set; } = new(new List<SelectListItem>());
        public SelectList TypeOptions { get; set; } = new(new List<SelectListItem>());
        
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }

        public class InputModel
        {
            public string Id { get; set; } = string.Empty;

            [Required(ErrorMessage = "Model provider is required")]
            [Display(Name = "Model Provider")]
            public ModelProvider ModelProvider { get; set; }

            [Required(ErrorMessage = "Model name is required")]
            [StringLength(100, ErrorMessage = "Model name cannot exceed 100 characters")]
            [Display(Name = "Model Name")]
            public string ModelName { get; set; } = string.Empty;

            [Required(ErrorMessage = "Display name is required")]
            [StringLength(150, ErrorMessage = "Display name cannot exceed 150 characters")]
            [Display(Name = "Display Name")]
            public string DisplayName { get; set; } = string.Empty;

            [StringLength(500, ErrorMessage = "Description cannot exceed 500 characters")]
            [Display(Name = "Description")]
            public string? Description { get; set; }

            [Display(Name = "API Key")]
            [DataType(DataType.Password)]
            public string? ApiKey { get; set; }

            [Display(Name = "Model Type")]
            public ModelType ModelType { get; set; } = ModelType.Free;

            [Display(Name = "Is Active")]
            public bool IsActive { get; set; } = true;

            [Required(ErrorMessage = "Max tokens is required")]
            [Range(1, 1000000, ErrorMessage = "Max tokens must be between 1 and 1,000,000")]
            [Display(Name = "Max Tokens")]
            public int MaxTokens { get; set; } = 4096;
        }

        public async Task<IActionResult> OnGetAsync(string id)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(id))
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model ID is required." });
                }

                var model = await _llmModelService.GetModelByIdAsync(id);
                if (model == null)
                {
                    return RedirectToPage("./Index", new { ErrorMessage = "Model not found." });
                }

                // Populate the input model
                Input = new InputModel
                {
                    Id = model.Id,
                    ModelProvider = model.ModelProvider,
                    ModelName = model.ModelName,
                    DisplayName = model.DisplayName,
                    Description = model.Description,
                    ApiKey = model.ApiKey, // This will be decrypted by the service
                    ModelType = model.ModelType,
                    IsActive = model.IsActive,
                    MaxTokens = model.MaxTokens
                };

                CreatedAt = model.CreatedAt;
                UpdatedAt = model.UpdatedAt;

                LoadSelectLists();
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading edit LLM model page for ID {ModelId}", id);
                return RedirectToPage("./Index", new { ErrorMessage = "An error occurred while loading the model." });
            }
        }

        public async Task<IActionResult> OnPostAsync()
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    LoadSelectLists();
                    return Page();
                }

                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Challenge();
                }

                // Get the existing model to preserve certain fields
                var existingModel = await _llmModelService.GetModelByIdAsync(Input.Id);
                if (existingModel == null)
                {
                    ModelState.AddModelError(string.Empty, "Model not found.");
                    LoadSelectLists();
                    return Page();
                }

                // Create the updated model
                var model = new LLMModel
                {
                    Id = Input.Id,
                    ModelProvider = Input.ModelProvider,
                    ModelName = Input.ModelName.Trim(),
                    DisplayName = Input.DisplayName.Trim(),
                    Description = Input.Description?.Trim(),
                    ModelType = Input.ModelType,
                    IsActive = Input.IsActive,
                    MaxTokens = Input.MaxTokens
                };

                // Handle API key update
                if (!string.IsNullOrWhiteSpace(Input.ApiKey))
                {
                    // New API key provided
                    model.ApiKey = Input.ApiKey.Trim();
                }
                else
                {
                    // Keep existing API key
                    model.ApiKey = existingModel.ApiKey;
                }

                var updatedModel = await _llmModelService.UpdateModelAsync(model, userId);

                _logger.LogInformation("Admin user {UserId} updated LLM model {ModelId} ({ModelName})", 
                    userId, updatedModel.Id, updatedModel.ModelName);

                return RedirectToPage("./Index", new { SuccessMessage = $"Model '{updatedModel.DisplayName}' updated successfully." });
            }
            catch (InvalidOperationException ex)
            {
                _logger.LogWarning(ex, "Validation error updating LLM model");
                ModelState.AddModelError(string.Empty, ex.Message);
                LoadSelectLists();
                return Page();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating LLM model {ModelId}", Input.Id);
                ModelState.AddModelError(string.Empty, "An error occurred while updating the model. Please try again.");
                LoadSelectLists();
                return Page();
            }
        }

        private void LoadSelectLists()
        {
            // Load provider options
            var providerItems = Enum.GetValues<ModelProvider>()
                .Select(p => new SelectListItem
                {
                    Value = p.ToString(),
                    Text = p.ToString()
                })
                .ToList();
            ProviderOptions = new SelectList(providerItems, "Value", "Text");

            // Load type options
            var typeItems = Enum.GetValues<ModelType>()
                .Select(t => new SelectListItem
                {
                    Value = t.ToString(),
                    Text = t.ToString()
                })
                .ToList();
            TypeOptions = new SelectList(typeItems, "Value", "Text");
        }
    }
}
